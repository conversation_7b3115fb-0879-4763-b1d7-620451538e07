@import "colorui/main.wxss";
@import "colorui/icon.wxss";
@import 'miniprogram_npm/@vant/weapp/common/index.wxss';

/*单元格样式*/
.cust-row{
  min-height: 60rpx;
	background-color: var(--white);
	padding: 1rpx 10rpx;
	display: flex;
	align-items: center;
  justify-content:start;
}

.cust-row+.cust-row {
	border-top: 1rpx solid #eee;
}

.cust-col{
	display: flex;
	align-items: center;
  justify-content:start;
}

/*自定义微信顶部导航栏*/
.cu-custom .cu-bar .content{
  width: calc(100% - 160rpx);
  text-align:left;
}

/*文本截断(2行)*/
.text-multcut{
  line-height: 1.4em;
  overflow: hidden; 
  text-overflow: ellipsis;
  word-break: break-all;   
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/*文本截断(2行)*/
.text-multcut3{
  line-height: 1.4em;
  overflow: hidden; 
  text-overflow: ellipsis;
  word-break: break-all;   
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/*顶部底部工具栏*/
.sg-fixed{
  position: fixed;
	width: 100%;
	top: 0;
	z-index: 1024;
	box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}
.sg-foot {
	position: fixed;
	width: 100%;
	bottom: 0;
	z-index: 1024;
	box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

/*下拉加载*/
.cu-load.more::after {
	content: "继续下拉加载更多";
}

.sg-row{
  min-height: 90rpx;
  margin-left: 8rpx;
  margin-right: 8rpx;
}

.bg-current {
	font-weight: bold;
	background-color:  #d3edee;
  border: 1rpx solid #009fa8;
}

.bg-shimge {
	background-color: #009fa8;
	color: #ffffff;
}

.line-shimge::after,
.lines-shimge::after {
  border-color:  #009fa8;
  color: #009fa8;
}

.sg-white {
	background-color: var(--white);
}

.text-shimge{
  color: #009fa8;
}

/*分割线*/
.sg-divider {
  display: block;
  height: 4rpx;
  width: 100%;
  clear: both;
  border-top: 4rpx solid  #eee;
}

/*操作条最小高度重定义*/
.cu-bar {
  min-height: 90rpx;
}

.cu-bar.tabbar {
	height: calc(90rpx + env(safe-area-inset-bottom) / 2);
}


/*标签导航栏*/
.tag-nav{
  min-height: 80rpx;
  display: flex;
  align-items: center;
  background-color: var(--white);
}
.tag-nav .cu-tag{
	font-size: 28rpx;
  text-align: center;
	padding: 0rpx 16rpx;
	height: 60rpx;
  line-height: 60rpx;
  display: inline-block;
  min-width: 5em;
}
.tag-nav .cu-tag+.cu-tag {
	margin-left: 25rpx;
}

/*导航栏重定义*/
.nav {
  min-height: 70rpx;
	padding: auto 10rpx;
	background-color: var(--white);
}
.nav .cu-item {
  font-size: 28rpx;
  text-align: center;
  display: inline-block;
	height: 60rpx;
	line-height: 60rpx;
	margin: 0 10rpx;
  padding: 0 12rpx;
	min-width: 4em;
}
.nav .cu-item.cur {
	border-bottom: 5rpx solid;
  font-weight: bold;
}

/*下拉选择列表*/
.sg-select{
  max-height: 500rpx;
  position: absolute;
  z-index: 1100;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

/*占位符样式*/
.sg-placeholder{
  color: #808080;
}

/*表单*/
.cu-form-group .cu-bar{
  width: 100%;
}
.cu-form-group .cu-bar .action:first-child {
	margin-left: 0rpx;
}
.cu-form-group .cu-bar .action:last-child {
	margin-right: 0rpx;
}


/*文本栏样式*/
.sg-content{
  text-align: left;
  word-break: break-all; 
	font-size: 28rpx;
	color: #555;
}
.sg-content-red{
  text-align: left;
  word-break: break-all; 
	font-size: 28rpx;
	color: #e54d42;
}
/*序号栏样式*/
.sg-seqnum {
	text-align: right;
	font-size: 30rpx;
  font-weight: bold;
  min-width: 1.1em;
  height: 60rpx;
  line-height: 60rpx;
}
/*标签栏样式*/
.sg-label {
	text-align: left;
	padding-right: 10rpx;
	font-size: 30rpx;
  font-weight: bold;
  height: 60rpx;
  line-height: 60rpx;
}
/*标签栏样式,留白序号宽度*/
.sg-label-seq {
	text-align: left;
	padding-right: 10rpx;
	font-size: 30rpx;
  font-weight: bold;
  margin-left: 1.1em;
  height: 60rpx;
  line-height: 60rpx;  
}

/*用于表单栏目对齐*/
.sg-mwidth2{
  min-width: calc(2.5em + 10rpx);
}
.sg-mwidth3{
  min-width: calc(3.5em + 10rpx);
}
.sg-mwidth4{
  min-width: calc(4.5em + 10rpx);
}
.sg-mwidth5{
  min-width: calc(5.5em + 10rpx);
}
.sg-mwidth5{
  min-width: calc(6.5em + 10rpx);
}

/*单选框 复选框*/
radio .wx-radio-input,
checkbox .wx-checkbox-input {
	margin-left: 5px;
	width: 18px;
	height: 18px;
}

radio::before,
checkbox::before {
	margin-top: -8px;
	right: 3px;
	font-size: 28;
	line-height: 18px;
}

checkbox.shimge[checked] .wx-checkbox-input,
radio.shimge[checked] .wx-radio-input {
	border-color: #009fa8 !important;
}
checkbox.shimge[checked] .wx-checkbox-input,
radio.shimge[checked] .wx-radio-input {
	background-color: #009fa8 !important;
	color: var(--white) !important;
}

/*日期时间选择器*/
.cu-form-group date-time-picker {
	flex: 1;
	padding-right: 40rpx;
	overflow: hidden;
	position: relative;
}
.cu-form-group date-time-picker .date-time-picker {
	line-height: 100rpx;
	font-size: 28rpx;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	width: 100%;
	text-align: right;
}
.cu-form-group date-time-picker::after {
	font-family: "cuIcon";
	display: block;
	content: "\e6a3";
	position: absolute;
	font-size: 34rpx;
	color: var(--grey);
	line-height: 100rpx;
	width: 60rpx;
	text-align: center;
	top: 0;
	bottom: 0;
	right: -20rpx;
	margin: auto;
}

.sg-flex-center {
  display: flex;
  justify-content: center;
}
.sg-flex-auto{
  flex:0 auto !important;
}
.sg-text-bold {
  font-weight:bold !important;
}