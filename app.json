{"pages": ["pages/login/login", "pages/wklogin/wklogin", "pages/wklogin/wkpassword/wkpassword", "pages/index/index", "pages/pu/pu-unreceive/pu-unreceive", "pages/pu/pu-unreceive/pu-unreceive-detail/pu-unreceive-detail", "pages/mo/mo-shortage/mo-shortage", "pages/mo/mo-shortage/mo-shortage-detail/mo-shortage-detail", "pages/mo/mo-shortage/mo-shortage-purchase/mo-shortage-purchase", "pages/mo/mo-shortage/mo-shortage-reply/mo-shortage-reply", "pages/mo/mo-delivery/mo-delivery", "pages/mo/mo-delivery/mo-delivery-detail/mo-delivery-detail", "pages/mo/mo-cycletime/mo-cycletime", "pages/mo/mo-cycletime/mo-cycletime-detail/mo-cycletime-detail", "pages/mo/mo-cycletimeZuz/mo-cycletimeZuz", "pages/mo/mo-cycletimeZuz/mo-cycletimeZuz-detail/mo-cycletimeZuz-detail", "pages/mo/mo-storagescan/mo-storagescan", "pages/mo/mo-storage/mo-storage", "pages/repair/repair-send/repair-send", "pages/repair/repair-datetimepicker/repair-datetimepicker", "pages/repair/repair-arrange/repair-arrange", "pages/repair/repair-arrange/repair-arrange-detail/repair-arrange-detail", "pages/repair/repair-solve/repair-solve", "pages/repair/repair-solve/repair-solve-detail/repair-solve-detail", "pages/repair/repair-comment/repair-comment", "pages/repair/repair-comment/repair-comment-detail/repair-comment-detail", "pages/repair/repair-Myown/repair-Myown", "pages/repair/repair-Myown/repair-Myown-detail/repair-Myown-detail", "pages/repair/repair-Spotcheck/repair-Spotcheck", "pages/repair/repair-MyownSpot/repair-MyownSpot", "pages/repair/repair-SpotcheckWarning/repair-SpotcheckWarning", "pages/repair/repair-EquipmentResume/repair-EquipmentResume", "pages/repair/repair-EquipmentResume/repair-EquipmentResume-detail/repair-EquipmentResume-detail", "pages/wk/wk-worksheet/wk-worksheet-pick/wk-worksheet-pick", "pages/wk/wk-worksheet/wk-worksheet", "pages/wk/wk-worktask/wk-worktask", "pages/wk/wk-salary/wk-salary", "pages/wk/wk-prodjob/wk-prodjob", "pages/wk/wk-prodjob/wk-prodjob-detail/wk-prodjob-detail", "pages/finance/finance-Scan/finance-Scan", "pages/finance/finance-Scan-Common/finance-Scan-Common", "pages/qt/qt-assinspect/qt-assinspect", "pages/qt/qt-assinspect/qt-assunqitem/qt-assunqitem", "pages/qt/qt-assinspect/qt-assunqdetail/qt-assunqdetail", "pages/qt/qt-preuninspect/qt-preuninspect", "pages/qt/qt-preinspect/qt-preinspect", "pages/qt/qt-preinspect/qt-preunqitem/qt-preunqitem", "pages/qt/qt-preinspect/qt-preunqdetail/qt-preunqdetail", "pages/qt/qt-preinspect/qt-standard/qt-standard", "pages/qt/qt-rawuninspect/qt-rawuninspect", "pages/qt/qt-rawinspect/qt-rawinspect", "pages/qt/qt-rawunstore/qt-rawunstore", "pages/qt/qt-rawunstore/qt-rawunstoredata/qt-rawunstoredata", "pages/qt/qt-abnormal/qt-abnormal", "pages/qt/qt-abnormal/qt-assrecord/qt-assrecord", "pages/qt/qt-abnormal/qt-prerecord/qt-prerecord", "pages/qt/qt-abnormal/qt-rawrecord/qt-rawrecord", "pages/qt/qt-rawinspect/qt-changerd/qt-changerd", "pages/market/market-project/market-project", "pages/market/market-shipplan/market-shipplan", "pages/market/market-addParts-login/market-addParts-login", "pages/market/market-addParts/market-addParts", "pages/market/market-addpartsDetailed/market-addpartsDetailed", "pages/market/market-addpartsDetailed/market-addpartsDetailed-Plus/market-addpartsDetailed-Plus", "pages/market/market-addpartsDetailed/market-addpartsDetailed-List/market-addpartsDetailed-List", "pages/market/cust-bom/cust-bom", "pages/market/cust-bom/cust-bom-detail/cust-bom-detail", "pages/market/market-ExitPermit/ExitPermit", "pages/pu/pu-jitship/pu-jitship", "pages/wk/wk-upload/wk-upload"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#009fa8", "navigationBarTitleText": "新界泵业内部管理系统", "navigationBarTextStyle": "white", "onReachBottomDistance": 300}, "networkTimeout": {"request": 300000, "connectSocket": 300000, "uploadFile": 300000, "downloadFile": 300000}, "usingComponents": {"cu-custom": "/colorui/components/cu-custom", "van-config-provider": "@vant/weapp/config-provider/index", "van-button": "@vant/weapp/button/index", "van-cell": "@vant/weapp/cell/index", "van-cell-group": "@vant/weapp/cell-group/index", "van-image": "@vant/weapp/image/index", "van-row": "@vant/weapp/row/index", "van-col": "@vant/weapp/col/index", "van-checkbox": "@vant/weapp/checkbox/index", "van-checkbox-group": "@vant/weapp/checkbox-group/index", "van-datetime-picker": "@vant/weapp/datetime-picker/index", "van-field": "@vant/weapp/field/index", "van-picker": "@vant/weapp/picker/index", "van-radio": "@vant/weapp/radio/index", "van-radio-group": "@vant/weapp/radio-group/index", "van-search": "@vant/weapp/search/index", "van-stepper": "@vant/weapp/stepper/index", "van-dialog": "@vant/weapp/dialog/index", "van-dropdown-menu": "@vant/weapp/dropdown-menu/index", "van-dropdown-item": "@vant/weapp/dropdown-item/index", "van-loading": "@vant/weapp/loading/index", "van-swipe-cell": "@vant/weapp/swipe-cell/index", "van-collapse": "@vant/weapp/collapse/index", "van-collapse-item": "@vant/weapp/collapse-item/index", "van-divider": "@vant/weapp/divider/index", "van-empty": "@vant/weapp/empty/index", "van-notice-bar": "@vant/weapp/notice-bar/index", "van-progress": "@vant/weapp/progress/index", "van-sticky": "@vant/weapp/sticky/index", "van-tag": "@vant/weapp/tag/index", "van-grid": "@vant/weapp/grid/index", "van-grid-item": "@vant/weapp/grid-item/index", "van-tab": "@vant/weapp/tab/index", "van-tabs": "@vant/weapp/tabs/index", "van-tabbar": "@vant/weapp/tabbar/index", "van-tabbar-item": "@vant/weapp/tabbar-item/index", "van-tree-select": "@vant/weapp/tree-select/index", "van-goods-action": "@vant/weapp/goods-action/index", "van-goods-action-icon": "@vant/weapp/goods-action-icon/index", "van-goods-action-button": "@vant/weapp/goods-action-button/index"}, "sitemapLocation": "sitemap.json"}