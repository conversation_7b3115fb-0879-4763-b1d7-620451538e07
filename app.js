//app.js
App({
  onLaunch: function() {

    //版本更新
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      updateManager.onCheckForUpdate(function (res) {
        // 请求完新版本信息的回调
        console.log(res.hasUpdate);
      });
      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: function (res) {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate()
            }
          }
        });
      });
      updateManager.onUpdateFailed(function () {
        // 新版本下载失败
      });
    }

    // 获取系统信息
    try{
      let sy = wx.getSystemInfoSync();

      if (sy.environment)
        this.globalData.environment = sy.environment;

      this.globalData.platform = sy.platform;
      this.globalData.StatusBar = sy.statusBarHeight;
      let capsule = wx.getMenuButtonBoundingClientRect();
      if (capsule) {
        this.globalData.Custom = capsule;
        this.globalData.CustomBar = capsule.bottom + capsule.top - sy.statusBarHeight;
      } 
      else {
        this.globalData.CustomBar = sy.statusBarHeight + 50;
      }

      let rate = sy.windowHeight/sy.windowWidth;
      this.globalData.ScreenHeight = 750*rate;

    } catch (ex){

    }

    // // 微信登录
    // if (this.globalData.environment !='wxwork'){

    //   wx.login({
    //     success: res => {
    //       // 发送 res.code 到后台换取 openId, sessionKey, unionId
    //     }
    //   })
    //   // 获取用户信息
    //   wx.getSetting({
    //     success: res => {
    //       if (res.authSetting['scope.userInfo']) {
    //         // 已经授权，可以直接调用 getUserInfo 获取头像昵称，不会弹框
    //         wx.getUserInfo({
    //           success: res => {
    //             // 可以将 res 发送给后台解码出 unionId
    //             this.globalData.userInfo = res.userInfo

    //             // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
    //             // 所以此处加入 callback 以防止这种情况
    //             if (this.userInfoReadyCallback) {
    //               this.userInfoReadyCallback(res)
    //             }
    //           }
    //         })
    //       }
    //     }
    //   });
    // }


    //当前用户信息
    let account = wx.getStorageSync('accountInfo');
    this.globalData.accountInfo = account;
    
    //有效期内免登录,跳转至首页
    //if (account && account.code && account.expireTimeStamp >= Date.now()) {
    //  wx.redirectTo({ url: '/pages/index/index' });
    //}

  },
  //启动或切前台
  onShow:function(){

  },
  globalData: {
    environment: null, //是否企业微信 wxwork
    userInfo: null,  //微信用户信息
    accountInfo: {}, //系统用户信息
    pageSize: 50,
    entCode: '001', //U9企业编码,正式库001
    apiBase: "https://youyou.shimge.com:55570" //webapi服务地址 https://youyou.shimge.com:55570   http://192.168.1.185:55590
  }
})