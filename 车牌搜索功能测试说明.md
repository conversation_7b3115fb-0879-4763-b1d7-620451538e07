# 车牌搜索功能测试说明

## 功能概述
在ExitPermit页面的选择车牌号弹窗中添加了模糊搜索功能，用户可以通过车牌号或司机姓名进行搜索。

## 修改的文件
1. `pages/market/market-ExitPermit/ExitPermit.js` - 添加搜索逻辑
2. `pages/market/market-ExitPermit/ExitPermit.wxml` - 添加搜索框UI

## 新增功能
1. **搜索框**: 在车牌选择弹窗顶部添加搜索输入框
2. **模糊搜索**: 支持按车牌号或司机姓名进行模糊匹配
3. **防抖处理**: 输入300ms后执行搜索，避免频繁查询
4. **实时过滤**: 搜索结果实时更新显示

## 测试步骤
1. 打开ExitPermit页面
2. 点击"请选择已进门的车辆"
3. 在弹出的车牌选择器中，应该能看到搜索框
4. 在搜索框中输入车牌号的部分字符（如"浙A"）
5. 验证列表是否正确过滤显示匹配的车辆
6. 在搜索框中输入司机姓名的部分字符
7. 验证列表是否正确过滤显示匹配的车辆
8. 清空搜索框，验证是否显示所有车辆
9. 输入不存在的内容，验证是否显示"未找到匹配的车辆"

## 技术实现
- 使用防抖技术避免频繁搜索
- 支持车牌号和司机姓名的模糊匹配
- 搜索不区分大小写
- 复用了客户选择器的样式，保持UI一致性
