var formatNumber = function(n) {
  n = n.toString()
  return n[1] ? n : '0' + n
}

var formatTime = function(d) {
  var rs = ''
  if(d){
    //解决ios时间格式有兼容性问题
    var reg = getRegExp('-', 'g')
    var dt = d ? d.replace(reg, '/') : d

    var date = getDate(dt)
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate()
    var hour = date.getHours()
    var minute = date.getMinutes()
    var second = date.getSeconds()
    rs = [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
  }

  return rs
}

var formatDate = function(d){
  var rs = ''
  if(d){
    //解决ios时间格式有兼容性问题
    var reg = getRegExp('-', 'g')
    var dt = d ? d.replace(reg, '/') : d

    var date = getDate(dt)
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate()
    rs = [year, month, day].map(formatNumber).join('/')
  }

  return rs
}

var formatDay = function(d){
  var rs = ''
  if(d){
    //解决ios时间格式有兼容性问题
    var reg = getRegExp('-', 'g')
    var dt = d ? d.replace(reg, '/') : d

    var date = getDate(dt)
    var month = date.getMonth() + 1
    var day = date.getDate()
    rs = [month, day].map(formatNumber).join('/')
  }

  return rs
}

var formatPercent = function (d,n) {
  var m = ''
  if (typeof(d) != 'undefined' && d != null){
    m = Math.round(d * 100 * Math.pow(10, n)) / Math.pow(10, n)
    m = m + '%'
  }
  
  return m
}

var formatDecimal = function (d,n) {
  var m = ''
  if (typeof(d) != 'undefined' && d != null){
    m = Math.round(d * Math.pow(10, n)) / Math.pow(10, n)
  }
  
  return m
}


module.exports = {
  formatTime: formatTime,
  formatDate: formatDate,
  formatDay: formatDay,
  formatPercent: formatPercent,
  formatDecimal: formatDecimal
}
