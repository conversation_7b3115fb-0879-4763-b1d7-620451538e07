

const dateAdd = (interval,number,d)=>{
  var dt = d ? d.toString().replace(/\-/g, '/') : d
  var date = new Date(dt)

  switch(interval.toLowerCase()){ 
      case "y": return new Date(date.setFullYear(date.getFullYear()+number)); 
      case "m": return new Date(date.setMonth(date.getMonth()+number)); 
      case "d": return new Date(date.setDate(date.getDate()+number)); 
      case "w": return new Date(date.setDate(date.getDate()+7*number)); 
      case "h": return new Date(date.setHours(date.getHours()+number)); 
      case "n": return new Date(date.setMinutes(date.getMinutes()+number)); 
      case "s": return new Date(date.setSeconds(date.getSeconds()+number)); 
      case "l": return new Date(date.setMilliseconds(date.getMilliseconds()+number)); 
  }
}


const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : '0' + n
}


const formatDate = d => {
  var rs = ''
  if(d){
    //解决ios时间格式有兼容性问题
    var dt = d ? d.toString().replace(/\-/g, '/') : d

    var date = new Date(dt)
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate()
    
    rs = [year, month, day].map(formatNumber).join('/') 
  }

  return rs
}

//日期时间控件使用
const formatTime2 = d => {
  var rs = ''
  if(d){
    var dt = d ? d.toString().replace(/\-/g, '/') : d

    var date = new Date(dt)
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate()
    var hour = date.getHours()
    var minute = date.getMinutes()
    var second = date.getSeconds()
    rs = [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
  }

  return rs
}


const formatTime = d => {
  var rs = ''
  if(d){
    //解决ios时间格式有兼容性问题
    var dt = d ? d.toString().replace(/\-/g, '/') : d

    var date = new Date(dt)
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate()
    var hour = date.getHours()
    var minute = date.getMinutes()
    var second = date.getSeconds()
    rs = [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
  }

  return rs
}

const wuxToast  = text =>{
  wx.showToast({
    title: text,
    icon: 'none',
    duration: 2000
  });
}

const updateAccountExpire = () => {
  var app = getApp();
  var account = wx.getStorageSync('accountInfo');
  if (account && account.code){
    account.expireTimeStamp = Date.now() + 8 * 60 * 60 * 1000;
    app.globalData.accountInfo.expireTimeStamp = account.expireTimeStamp;
    wx.setStorageSync('accountInfo', account);
  }
}

const http = (url, method, data, success, fail) =>{
  wx.request({
    url: url,
    method: method,
    data: data,
    header: { 'content-type': 'application/json' },
    complete: function () {

    },
    success: function (res) {
      console.log(res);

      if (res.statusCode == '200' || res.statusCode == '204'){
        if (success)
          success(res.data);
      }
      else {
        console.log(res.statusCode);

        if (fail)
          fail();
      }
    },
    fail: function (error) {
      console.log(error);

      if (fail)
        fail();
    }
  });
}

module.exports = {
  dateAdd: dateAdd,
  formatDate: formatDate,
  formatTime: formatTime,
  formatTime2:formatTime2,
  updateAccountExpire: updateAccountExpire,
  wuxToast: wuxToast,
  http: http
}
