<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索单号、料品、工序" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="showModal" class="cu-btn bg-shimge shadow-blur round"  disabled="{{isLoading}}" data-target="DrawerModalR" type="">筛选</button>
    </view>
  </view>
</view>

<view class="bg-white"> 
  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="basis-p19 padding-sm">单号</view>
    <view class="basis-p23 padding-sm">规格</view>
    <view class="basis-p16 padding-sm">工序</view>
    <view class="basis-p14 padding-sm">数量</view>
    <view class="basis-p14 padding-sm">工价</view>
    <view class="basis-p14 padding-sm">工资</view>
  </view>
	<block wx:for="{{dataList}}" wx:for-item="item" wx:key="id">
    <view class="flex align-center solids-bottom sg-row" data-docno="{{item.docNo}}" data-line-id="{{item.id}}">
      <view class="basis-p19 padding-xs text-multcut">{{item.docNo}}</view>
      <view class="basis-p23 padding-xs text-multcut">{{item.itemSPECS}}</view>
      <view class="basis-p16 padding-xs text-multcut">{{item.process}}</view>
      <view class="basis-p14 padding-xs text-multcut text-center">{{item.productQty}}</view>
      <view class="basis-p14 padding-xs text-multcut text-center">{{item.price}}</view>
      <view class="basis-p14 padding-xs text-multcut text-center">{{item.submitAmt}}</view>
    </view>
  </block>

  <view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
  <view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>
</view>

<view class="sg-foot bg-shimge">
  <view class="flex align-center text-bold text-content">
    <view class="basis-p45 text-right padding-sm">合计数据</view>
    <view class="basis-p15 padding-sm">{{dateMain.totalQty}}</view>
    <view class="basis-p25 text-right padding-sm">合计工资</view>
    <view class="basis-p15 padding-sm">{{dateMain.totalAmt}}</view>
  </view>
</view>

<view class="cu-modal drawer-modal justify-end {{modalName=='DrawerModalR'?'show':''}}" bindtap="hideModal">
  <view class="cu-dialog basis-lg" catchtap style="top:{{CustomBar}}px;height:calc(100vh - {{CustomBar}}px)">
    <view class="cu-form-group">
      <view class="title">开始日期</view>
      <picker mode="date" value="{{dateBegin}}"  bindchange="DateBeginChange">
        <view class="picker">
          {{ filter.formatDate(dateBegin) }}
        </view>
      </picker>
    </view>
    <view class="cu-form-group">
      <view class="title">结束日期</view>
      <picker mode="date" value="{{dateEnd}}"  bindchange="DateEndChange">
        <view class="picker">
          {{ filter.formatDate(dateEnd) }}
        </view>
      </picker>
    </view> 
    <view class="cu-form-group">
      <button class="cu-btn bg-shimge shadow" bindtap="dlgModalOk">确定</button>
      <button class="cu-btn bg-shimge shadow" bindtap="dlgModalCancel">取消</button>
    </view>        
  </view>
</view>