const app = getApp()
import { wuxToast, http, updateAccountExpire, formatDate, dateAdd } from '../../../utils/util.js'

Page({
  data: {
    scrollLeft: 0,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    loadState: 0,//加载状态  1:继续下拉加载更多,2:加载中,3:没有更多了
    isError:false,//加载失败
    currentItem: 1,//当前的筛选项
    dateMain:{},//工单合计
    dataList:[],//工单列表
    bottomLineId:0,//底部最后一行LineId,用于上拉触底事件
    bottomId:0,//底部最后一行Id
    bottomDate:null,//底部最后一行日期
    searchKey:'',//搜索关键字
    user:null, //当前用户
    dateBegin:null, //筛选-开始日期
    dateEnd:null, //筛选-结束日期
    dateFrom:null, //查询-开始日期
    dateTo:null   //查询-结束日期
  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;
    
    var now = new Date();
    let dateBegin = formatDate( new Date(now.getFullYear(),now.getMonth(),1) ) ;
    let dateEnd = dateAdd('m',1,dateBegin);
    dateEnd = formatDate( dateAdd('d',-1,dateEnd) );
    this.setData({
      dateBegin: dateBegin,
      dateEnd: dateEnd,
      dateFrom: dateBegin,
      dateTo: dateEnd
    });

    this.getRequestData(true);
  },
  //获取后台数据
  getRequestData: function (again, callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    if (again) {
      //重新获取数据
      this.data.bottomId = 0;
      this.data.bottomLineId = 0;
      this.data.bottomDate = null;
      this.data.dataList = [];
      this.data.dateMain = {};
    }
    else{
      //上拉加载提示
      this.setData({ loadState: 2 });  
    }
    console.log('id:' + this.data.bottomLineId);
    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.personID;
    let type = -3; //当月计件工资
    let dateFrom = this.data.dateFrom;
    let dateTo = this.data.dateTo;
    let key = this.data.searchKey;
    let beginDate = this.data.bottomDate;
    let begin = this.data.bottomId;
    let beginline = this.data.bottomLineId;
    let url = app.globalData.apiBase + '/api/WKWorkSalary?user=' + user + '&org=' + org +'&fromDate=' + dateFrom +'&toDate=' + dateTo + '&key=' + key
             + '&beginDate=' + beginDate+ '&begin=' + begin + '&beginline=' + beginline + '&pagesize=' + pagesize;
    console.log(url);

    let suc = rd => {
      //更新数据
      let mainData = {};
      let totalData = that.data.dataList;
      //判断数据大小是否等于每页数大小，相等则认为还有数据，否则认为没有更多数据
      let hasmore = rd.length == pagesize ? 1 : 0;
      if (rd.length > 0) {
        totalData = totalData.concat(rd);
        that.data.bottomId = rd[rd.length - 1].workSheet;
        that.data.bottomLineId = rd[rd.length - 1].id;
        that.data.bottomDate = rd[rd.length - 1].submitDate;

        //合计工资 合计数量
        mainData.totalAmt = rd[0].totalAmt;
        mainData.totalQty = rd[0].totalQty;
      }

      that.setData({
        loadState: hasmore,
        dataList: totalData,
        dateMain: mainData,
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;

      if (callBack)
        callBack(rd);
    };

    let fl = () => {
      //更新数据
      that.data.bottomId = 0;
      that.data.bottomLineId = 0;
      that.data.bottomDate = null;
      that.setData({
        isError: true,
        loadState: 0,
        dataList: [],
        dateMain: {}
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false; 
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });     
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading){
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else{
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress>0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //搜索框输入完成
  confirmKeyInput: function(e){
    console.log(e.detail);
    this.data.searchKey = e.detail.value;

    this.getRequestData(true, this.goTop);
  },  
  //查询按钮
  searchData: function(){
    this.getRequestData(true, this.goTop);
  },
  //滚动条回到顶部
  goTop: function () {
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //搜索文本改变事件
  bindKeyInput:function(e){
    console.log(e.detail);

    this.data.searchKey = e.detail.value;
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData(true);
  },
  //上拉加载更多
  onReachBottom: function () {
    if (this.data.loadState == 0 ||this.data.loadState == 3)
      return;
    this.getRequestData(false);
  },
  //显示弹窗
  showModal: function(e){
    this.setData({
      modalName: e.currentTarget.dataset.target,
      dateBegin: this.data.dateFrom,
      dateEnd: this.data.dateTo,
    })
  },
  //关闭弹窗
  hideModal: function(e) {
    this.setData({
      modalName: null
    })
  },
  //弹窗-确定按钮
  dlgModalOk: function(e){
    this.setData({
      dateFrom: this.data.dateBegin,
      dateTo: this.data.dateEnd,
    })

    this.hideModal();

    this.getRequestData(true, this.goTop);
  },  
  //弹窗-取消按钮
  dlgModalCancel: function(e){

    this.hideModal();
  },  
  //弹窗-开始日期
  DateBeginChange: function(e){
    this.setData({
      dateBegin: e.detail.value
    })
  },
  //弹窗-结束日期
  DateEndChange: function(e){
    this.setData({
      dateEnd: e.detail.value
    })
  },
})