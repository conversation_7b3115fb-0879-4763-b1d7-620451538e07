// pages/wk/wk-upload/wk-upload.js
const app = getApp();
import { wuxToast, http, updateAccountExpire } from "../../../utils/util.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    search: "", //搜索关键词
    photos: [], //选中的图片
  },
  /**
   * 搜索按钮点击事件
   * @param {object} event
   */
  search: function (event) {},
  /**
   * 输入事件
   * @param {object} event
   */
  input: function (event) {
    this.data.search = event.detail.value;
  },
  /**
   * 输入确定事件
   * @param {object} event
   */
  confirm: function (event) {
    this.data.search = event.detail.value;
  },
  /**
   * 料号查询
   * @param {string} itemCode
   */
  queryItem: function (itemCode) {},
  /**
   * 选择图片
   * @param {object} event
   */
  chooseImage: function (event) {
    if (this.data.search.length != 11) {
      wuxToast("请输入11位料号");
      return;
    }
    let that = this;

    wx.chooseMedia({
      count: 50,
      mediaType: ["image"],
      sizeType: ["original", "compressed"],
      sourceType: ["album", "camera"],
      camera: "back",
      success(res) {
        that.setData({ photos: res.tempFiles });
      },
    });
  },
  /**
   * 文件上传
   * @param {Array<object>} tempFiles
   * @param {string} action
   * @returns
   */
  uploadImages: function (tempFiles, action) {
    if (this.data.search.length != 11) {
      wuxToast("请输入11位料号");
      return;
    }
    let that = this;
     let url = app.globalData.apiBase + "/api/photo/upload?code=" + this.data.search + "&action="
    //let url = "http://127.0.0.1:80/api/photo/upload?code=" + this.data.search + "&action=";
    for (let i = 0; i < tempFiles.length; i++) {
      wx.uploadFile({
        url: url + action,
        filePath: tempFiles[i].tempFilePath,
        name: "key",
        success(res) {},
      });
      if (i == 0 && action == "update") action = "add";
    }
    wuxToast("上传完成");
  },
  /**
   * 覆盖上传
   * @param {object} event
   */
  updateUpload: function (event) {
    if (this.data.photos.length == 0) {
      wuxToast("未选择图片无法上传");
      return;
    }
    this.uploadImages(this.data.photos, "update");
  },
  /**
   * 新增上传
   * @param {object} event
   */
  addUpload: function (event) {
    if (this.data.photos.length == 0) {
      wuxToast("未选择图片无法上传");
      return;
    }
    this.uploadImages(this.data.photos, "add");
  },
});
