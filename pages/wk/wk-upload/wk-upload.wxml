<!-- pages/wk/wk-upload/wk-upload.wxml -->
<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<view class="sg-fixed">
    <view class="cu-bar search sg-white">
        <view class="search-form round">
            <!-- <text class="cuIcon-search"></text> -->
            <input type="text" maxlength="100" placeholder="输入料号" type="number" value="{{search}}" confirm-type="search" bind:input="input" bind:confirm="confirm"></input>
        </view>
        <!-- <view class="action">
            <button bind:tap="search" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">
                搜索
            </button>
        </view> -->
    </view>
</view>
<button class="sg-label sg-mwidth4" style="width:80%; margin-left: 10%;margin-right: 10%;margin-top: 1%;margin-bottom: 1%;" class="cu-btn bg-shimge shadow-blur round" type="" bind:tap="chooseImage">
    选择上传图片
</button>
<view ></view>
<button class="sg-label sg-mwidth4" style="width:39%; margin-left: 10%;margin-right: 1%;margin-top: 1%;margin-bottom: 1%;" class="cu-btn bg-shimge shadow-blur round" type="" bind:tap="addUpload">
    新增上传
</button>
<button class="sg-label sg-mwidth4" style="width:39%; margin-left: 1%;margin-right: 10%;margin-top: 1%;margin-bottom: 1%;" class="cu-btn bg-shimge shadow-blur round" type="" bind:tap="updateUpload">
    覆盖上传
</button>
<view wx:for="{{photos}}">
<view style="float:left;width:48%; margin-top: 1%;margin-bottom: 1%;margin-left: 1%;margin-right: 1%">
    <cover-image src="{{item.tempFilePath}}" style="width:100%;heigth:auto"></cover-image>
</view>
</view>