<wxs module="filter" src="../../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view wx:if="{{!isError}}">
  <view wx:if="{{dataLine.length>0}}">
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="sg-label sg-mwidth4">生产订单:</view>
        <view class="sg-content">{{dataMain.docNo}}</view>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth4">产品料号:</view>
        <view class="sg-content">{{dataMain.moItem_Code}}</view>   
      </view>
    </view> 
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="sg-label sg-mwidth4">产品名称:</view>
        <view class="sg-content">{{dataMain.moItem_Name}}</view>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth4">生产数量:</view>
        <view class="sg-content">{{dataMain.productQty}}</view>   
      </view>
    </view> 
    <view class="cust-row">
      <view class="sg-label sg-mwidth4">产品规格:</view>
      <text selectable="true" class="sg-content">{{dataMain.moItem_SPECS}}</text>
    </view>
  </view>
  <block wx:for="{{dataLine}}" wx:for-item="item" wx:for-index="idx">
    <view class="cust-row margin-top-xs">
      <view class="cust-col sg-box">
        <view class="sg-seqnum">{{idx+1}}.</view>
        <view class="sg-label sg-mwidth2">料号:</view>
        <text selectable="true" class="sg-content">{{item.item_Code}}</text>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth2">品名:</view>
        <text selectable="true" class="sg-content">{{item.item_Name}}</text>   
      </view> 
    </view>
    <view class="cust-row">
      <view class="sg-label-seq sg-mwidth2">规格:</view>
      <text selectable="true" class="sg-content">{{item.item_SPECS}}</text>
    </view>
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="sg-label-seq sg-mwidth2">发料:</view>
        <text selectable="true" class="sg-content">{{item.issueStyleName}}</text>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth4">需求数量:</view>
        <view class="sg-content">{{item.actualReqQty+item.uom}}</view>
      </view>     
    </view>
  </block>  
</view>
<view class="cu-load bg-red erro" wx:else></view>  