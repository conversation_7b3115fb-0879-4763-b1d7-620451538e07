<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="输入工单" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="scanCode" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">
        扫码
      </button>
    </view>
  </view>
</view>
<view class="bg-white">
  <view class="cust-row">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth4">生产订单:</view>
      <text selectable="true" class="sg-content">{{dataMain.moDocNo}}</text>
    </view>
    <view class="cust-col">
      <view class="sg-label sg-mwidth4">客户简称:</view>
      <text selectable="true" class="sg-content">{{dataMain.moCust}}</text>
    </view>
  </view>
  <view class="cust-row">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth2">车间:</view>
      <text selectable="true" class="sg-content">{{dataMain.deptName}}</text>
    </view>
    <view class="cust-col">
      <view class="sg-label sg-mwidth2">产线:</view>
      <text selectable="true" class="sg-content">{{dataMain.productLineName}}</text>
    </view>
  </view>
  <view class="cust-row">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth4">产品名称:</view>
      <text selectable="true" class="sg-content">{{dataMain.itemName}}</text>
    </view>
    <view class="cust-col">
      <view class="sg-label sg-mwidth4">计划数量:</view>
      <view class="sg-content">{{dataMain.productQty}}</view>
    </view>
  </view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth4">规格型号:</view>
    <text selectable="true" class="sg-content">{{dataMain.itemSPECS}}</text>
  </view>
  <view class="cust-row">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth4">计划开工:</view>
      <view class="sg-content">{{filter.formatDate(dataMain.startDate)}}</view>
    </view>
    <view class="cust-col">
      <view class="sg-label sg-mwidth4">计划完工: </view>
      <view class="sg-content">{{filter.formatDate(dataMain.completeDate)}}</view>
    </view>
  </view>
  <view class="cust-row" wx:if="{{ user.org=='205' || user.org=='305' }}">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth4">图片信息:</view>
      <button class="sg-label sg-mwidth4" class="cu-btn bg-shimge shadow-blur round" type="" bind:tap="openPhoto">
        点击查看
      </button>
    </view>
  </view>
</view>

<view class="bg-white" wx:if="{{!isError}}">
  <view class="flex align-center bg-shimge text-bold text-center text-content margin-top-xs">
    <view class="basis-p14 padding-sm">勾选</view>
    <view class="basis-p23 padding-sm">工序</view>
    <view class="basis-p22 padding-sm">接收人</view>
    <view class="basis-p22 padding-sm">接收时间</view>
    <view class="basis-p19 padding-sm">已报工</view>
  </view>
  <checkbox-group bindchange="checkboxChange">
    <label class="flex align-center solids-bottom sg-row" wx:for="{{dataLine}}" wx:for-item="item" wx:for-index="idx" wx:key="id">
      <view class="basis-p14 padding-xs">
        <checkbox value="{{item.id}}" checked="{{item.checked}}" class="round shimge"></checkbox>
      </view>
      <view class="basis-p23 padding-xs">{{item.process}}</view>
      <view class="basis-p22 padding-xs">{{item.receiver}}</view>
      <view class="basis-p22 padding-xs">{{filter.formatDate(item.receiveDate)}}</view>
      <view class="basis-p19 padding-xs text-center">{{item.submitQty==null?0:item.submitQty}}</view>
    </label>
  </checkbox-group>
</view>
<view class="cu-load bg-red erro" wx:else></view>
<view class="cu-bar tabbar border foot bg-shimge" disabled="{{isLoading}}">
  <view class="submit" bindtap="receWorkSheet">任务接收</view>
  <view class="submit solids-left" data-target="DialogReportWork" bindtap="reportWork">报 工</view>
  <view class="submit solids-left" bindtap="viewBom">查看BOM</view>
  <view wx:if="{{!linkid}}" class="submit solids-left" bindtap="workTaskList">查看列表</view>
</view>
<view class="cu-modal {{modalName=='DialogReportWork'?'show':''}}">
  <view class="cu-dialog">
    <view class="cu-bar bg-white justify-end">
      <view class="content">报工</view>
    </view>
    <view class="padding bg-white">
      <view class="cu-form-group" style="margin-left: 120rpx;">
        <view class="title">报工数量</view>
        <input type="number" cursor-spacing="150rpx" class="text-left" maxlength="10" placeholder="请输入" value="{{workQty}}" bindinput="bindQtyInput"></input>
      </view>
      <view class="cu-form-group" style="margin-left: 120rpx;">
        <view class="title">工废数量</view>
        <input type="number" cursor-spacing="150rpx" class="text-left" maxlength="10" placeholder="请输入" value="{{scrapQty}}" bindinput="bindQty2Input"></input>
      </view>
      <view class="cu-form-group" style="margin-left: 120rpx;">
        <view class="title">料废数量</view>
        <input type="number" cursor-spacing="150rpx" class="text-left" maxlength="10" placeholder="请输入" value="{{wasteQty}}" bindinput="bindQty4Input"></input>
      </view>
      <view wx:if="{{showItem}}" class="cu-form-group" style="margin-left: 120rpx;">
        <view class="title">节拍(秒)</view>
        <input type="number" cursor-spacing="150rpx" class="text-left" maxlength="10" placeholder="请输入" value="{{beat}}" bindinput="bindQty5Input"></input>
      </view>
      <view wx:if="{{showItem}}" class="cu-form-group" style="margin-left: 120rpx;">
        <view class="title">重量(克)</view>
        <input type="number" cursor-spacing="150rpx" class="text-left" maxlength="10" placeholder="请输入" value="{{weight}}" bindinput="bindQty6Input"></input>
      </view>
      <view class="cu-form-group" style="margin-left: 120rpx;">
        <view class="title">备注</view>
        <input type="text" cursor-spacing="150rpx" class="text-left" maxlength="10" placeholder="请输入" value="{{memo}}" bindinput="bindQty3Input"></input>
      </view>
    </view>
    <view class="cu-bar bg-white justify-end">
      <view class="action">
        <button class="cu-btn line-green text-green" bindtap="hideModal">取消</button>
        <button class="cu-btn bg-green margin-left" bindtap="finishWork">确定</button>
      </view>
    </view>
  </view>
</view>