const app = getApp();
import { wuxToast, http, updateAccountExpire } from "../../../utils/util.js";

Page({
  data: {
    loadProgress: 0, //加载进度
    isLoading: false, //是否正在加载数据
    isError: false, //加载失败
    dataMain: {}, //主表
    dataLine: [], //明细表
    searchKey: "", //搜索关键字
    workQty: "", //报工数量
    scrapQty: "", //工废数量
    wasteQty: "", //料废数量
    beat: "", //节拍
    weight: "", //重量
    memo: "", //报废数量
    showItem: false, //是否显示节拍、重量
    user: null, //当前用户
    linkid: null, //跳转链接id
    looked: false, // 是否看过图片
    showPhoto: false, // 是否展示图片
  },
  onLoad: function (options) {
    this.data.user = app.globalData.accountInfo;
    this.setData({user : app.globalData.accountInfo})
    //方鑫205
    if (this.data.user.org == "205" || this.data.user.org == "305") {
      this.setData({ showItem: true });
    }

    //跳转链接
    if (options.docno) {
      this.setData({
        searchKey: options.docno,
        linkid: options.id,
      });

      this.getRequestData(options.docno, null, options.id);
    }
  },
  //获取后台数据
  getRequestData: function (key, callBack, linkid) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading) return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();
    wx.showLoading({ title: "加载中...", mask: true });

    //加载失败提示
    if (this.data.isError) this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.personID;

    let url =
      app.globalData.apiBase +
      "/api/WKWorkSheet?user=" +
      user +
      "&org=" +
      org +
      "&key=" +
      key;
    console.log(url);

    let suc = (rd) => {
      //更新数据
      let line = rd;
      let main = {};
      if (rd.length > 0) {
        main = {
          orgCode: rd[0].orgCode,
          org: rd[0].org,
          workSheet: rd[0].workSheet,
          moDocNo: rd[0].moDocNo,
          moCust: rd[0].moCust,
          deptName: rd[0].deptName,
          productLineName: rd[0].productLineName,
          itemCode: rd[0].itemCode,
          itemName: rd[0].itemName,
          itemSPECS: rd[0].itemSPECS,
          startDate: rd[0].startDate,
          completeDate: rd[0].completeDate,
          productQty: rd[0].productQty,
        };
      }

      //勾选跳转链接对应的工序行
      if (linkid) {
        for (let i = 0; i < line.length; i++) {
          if (line[i].id == linkid) line[i].checked = true;
        }
      }

      that.setData({
        dataMain: main,
        dataLine: line,
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      wx.hideLoading();
      that.data.isLoading = false;

      if (callBack) callBack(rd);
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        dataMain: {},
        dataLine: [],
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      wx.hideLoading();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == "none") wuxToast("请检查网络是否正常");
        },
      });
    };
    http(url, "GET", null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110) progress = 100;
      else if (progress >= 110) progress = 10;
    } else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading,
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100);
    }
  },
  //扫码按钮
  scanCode: function () {
    let that = this;

    wx.scanCode({
      onlyFromCamera: true,
      success(res) {
        console.log(res);

        that.setData({ searchKey: res.result });

        that.getRequestData(res.result, that.goTop);
      },
    });
  },
  //搜索框输入完成
  confirmKeyInput: function (e) {
    console.log(e.detail);
    this.data.searchKey = e.detail.value;

    this.getRequestData(this.data.searchKey, this.goTop);
  },
  //滚动条回到顶部
  goTop: function () {
    wx.pageScrollTo({
      scrollTop: 0,
    });
  },
  //搜索文本改变事件
  bindKeyInput: function (e) {
    console.log(e.detail);
    this.data.searchKey = e.detail.value;
  },
  //下拉刷新
  onPullDownRefresh: function () {
    let key = this.data.searchKey;
    console.log(key);

    this.getRequestData(key);
  },
  //勾选事件
  checkboxChange: function (e) {
    console.log(e.detail);
    let items = this.data.dataLine;
    let values = e.detail.value;

    for (let i = 0; i < items.length; i++) {
      items[i].checked = false;

      for (let j = 0; j < values.length; j++) {
        if (items[i].id == values[j]) {
          items[i].checked = true;
          break;
        }
      }
    }

    this.setData({ dataLine: items });
  },
  //获取勾选的记录
  getCheckLine: function (type, qty, qty2, qty3, qty4, qty5, memo) {
    let lines = new Array();
    for (let i = 0; i < this.data.dataLine.length; i++) {
      if (this.data.dataLine[i].checked) {
        let l = {};
        l.LineID = this.data.dataLine[i].id;
        if (qty) l.SubmitQty = qty;
        if (qty2) l.ScrapQty = qty2;
        if (qty3) l.WasteQty = qty3;
        if (qty4) l.Beat = qty4;
        if (qty5) l.Weight = qty5;
        l.Memo = memo;
        l.DocBeat = this.data.dataLine[i].beatTime;
        l.DocWeight = this.data.dataLine[i].weight;
        lines.push(l);
      }
    }

    let data = {
      data: {
        Type: type,
        UserCode: this.data.user.personID,
        WorkSheetID: this.data.dataMain.workSheet,
        Lines: lines,
      },
    };

    return data;
  },
  //任务接收
  receWorkSheet: function () {
    if (!this.data.dataMain.workSheet) return;

    if (!this.data.user || !this.data.user.personID) {
      wuxToast("无法获取员工编号,请联系系统管理员");
      return;
    }

    let cklist = this.getCheckLine(1, null, null, null, null, null, null);
    console.log(cklist);
    if (!cklist.data.Lines || cklist.data.Lines.length == 0) {
      wuxToast("请先勾选工序");
      return;
    }

    wx.showLoading({ title: "正在提交...", mask: true });
    let that = this;
    let entCode = app.globalData.entCode;
    let url =
      app.globalData.apiBase +
      "/U9SVCHandler.ashx?api=UFIDA.U9.Cust.SHIMGE.MO.WorkOrderSV.ISubmitWorkSheetSV" +
      "&entcode=" +
      entCode;
    console.log(url);

    let suc = (rd) => {
      wx.hideLoading();

      if (rd.d) {
        let msg = "任务接收成功";
        let lines = rd.d.Lines;
        if (rd.d.Error) msg = rd.d.Error;
        //刷新数据
        let rs = new Array();
        for (let i = 0; i < lines.length; i++) {
          let r = {
            id: lines[i].ID,
            workSheet: lines[i].WorkSheet,
            docNo: lines[i].DocNo,
            org: lines[i].Org,
            orgCode: lines[i].OrgCode,
            moDocNo: lines[i].MODocNo,
            moCust: lines[i].MOCust,
            deptName: lines[i].DeptName,
            productLineName: lines[i].ProductLineName,
            itemCode: lines[i].ItemCode,
            itemName: lines[i].ItemName,
            itemSPECS: lines[i].ItemSPECS,
            startDate: lines[i].StartDate,
            completeDate: lines[i].CompleteDate,
            productQty: lines[i].ProductQty,
            sequence: lines[i].Sequence,
            process: lines[i].Process,
            receiver: lines[i].Receiver,
            receiveDate: lines[i].ReceiveDate,
            submiter: lines[i].Submiter,
            submitDate: lines[i].SubmitDate,
            submitQty: lines[i].SubmitQty == 0 ? "" : lines[i].SubmitQty,
            scrapQty: lines[i].ScrapQty == 0 ? "" : lines[i].ScrapQty,
            wasteQty: lines[i].WasteQty == 0 ? "" : lines[i].WasteQty,
            beat: lines[i].BeatTime == 0 ? "" : lines[i].BeatTime,
            weight: lines[i].Weight == 0 ? "" : lines[i].Weight,
          };

          rs.push(r);
        }
        that.setData({
          dataLine: rs,
        });

        wuxToast(msg);
      } else {
        wuxToast("任务接收失败");
      }
    };

    let fl = () => {
      wx.hideLoading();
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == "none") wuxToast("请检查网络是否正常");
          else wuxToast("任务接收失败");
        },
      });
    };

    http(url, "POST", cklist, suc, fl);
  },
  //隐藏对话框
  hideModal: function (e) {
    this.setData({
      modalName: null,
      workQty: null,
      scrapQty: null,
      wasteQty: null,
      beat: null,
      weight: null,
      memo: null,
    });
  },
  //报工数量对话框
  reportWork: function (e) {
    if (!this.data.looked && (this.data.user.org == "205" || this.data.user.org == "305")) {
      wuxToast("请查看完所有图片后再报工");
      return;
    }
    if (!this.data.dataMain.workSheet) return;
    if (!this.data.user || !this.data.user.personID) {
      wuxToast("无法获取员工编号,请联系系统管理员");
      return;
    }

    let cklist = this.getCheckLine(2, null, null, null, null, null, null);
    if (!cklist.data.Lines || cklist.data.Lines.length == 0) {
      wuxToast("请先勾选工序");
      return;
    }

    console.log("报工:");
    console.log(cklist);

    //显示之前的节拍和重量
    if (
      cklist &&
      cklist.data &&
      cklist.data.Lines &&
      cklist.data.Lines.length == 1
    ) {
      let w = "";
      if (cklist.data.Lines[0].DocWeight) w = cklist.data.Lines[0].DocWeight;
      let b = "";
      if (cklist.data.Lines[0].DocBeat) b = cklist.data.Lines[0].DocBeat;

      this.setData({
        beat: b,
        weight: w,
      });
    }

    this.setData({
      modalName: e.currentTarget.dataset.target,
    });
  },
  //报工数量输入事件
  bindQtyInput: function (e) {
    console.log(e.detail);

    this.data.workQty = e.detail.value;
  },
  //工废数量输入事件
  bindQty2Input: function (e) {
    console.log(e.detail);

    this.data.scrapQty = e.detail.value;
  },
  //料废数量输入事件
  bindQty4Input: function (e) {
    console.log(e.detail);

    this.data.wasteQty = e.detail.value;
  },
  //节拍输入事件
  bindQty5Input: function (e) {
    console.log(e.detail);

    this.data.beat = e.detail.value;
  },
  //重量输入事件
  bindQty6Input: function (e) {
    console.log(e.detail);

    this.data.weight = e.detail.value;
  },
  //备注输入事件
  bindQty3Input: function (e) {
    console.log(e.detail);

    this.data.memo = e.detail.value;
  },
  //提交报工数量
  finishWork: function (e) {
    if (!this.data.user || !this.data.user.personID) {
      wuxToast("无法获取员工编号,请联系系统管理员");
      return;
    }
    let qty = this.data.workQty;
    if (!qty || qty <= 0) {
      wuxToast("请输入报工数量");
      return;
    }
    let qty3 = this.data.wasteQty;
    let qty2 = this.data.scrapQty;
    let memo = this.data.memo;
    let qty4 = this.data.beat;
    let qty5 = this.data.weight;

    let cklist = this.getCheckLine(2, qty, qty2, qty3, qty4, qty5, memo);
    console.log(cklist);

    wx.showLoading({ title: "正在提交...", mask: true });

    let that = this;
    let entCode = app.globalData.entCode;
    let url =
      app.globalData.apiBase +
      "/U9SVCHandler.ashx?api=UFIDA.U9.Cust.SHIMGE.MO.WorkOrderSV.ISubmitWorkSheetSV" +
      "&entcode=" +
      entCode;
    console.log(url);

    let suc = (rd) => {
      if (rd.d) {
        let msg = "报工成功";
        let lines = rd.d.Lines;
        if (rd.d.Error) {
          msg = rd.d.Error;
        } else {
          //刷新数据
          let rs = new Array();
          for (let i = 0; i < lines.length; i++) {
            let r = {
              id: lines[i].ID,
              workSheet: lines[i].WorkSheet,
              docNo: lines[i].DocNo,
              org: lines[i].Org,
              orgCode: lines[i].OrgCode,
              moDocNo: lines[i].MODocNo,
              moCust: lines[i].MOCust,
              deptName: lines[i].DeptName,
              productLineName: lines[i].ProductLineName,
              itemCode: lines[i].ItemCode,
              itemName: lines[i].ItemName,
              itemSPECS: lines[i].ItemSPECS,
              startDate: lines[i].StartDate,
              completeDate: lines[i].CompleteDate,
              productQty: lines[i].ProductQty,
              sequence: lines[i].Sequence,
              process: lines[i].Process,
              receiver: lines[i].Receiver,
              receiveDate: lines[i].ReceiveDate,
              submiter: lines[i].Submiter,
              submitDate: lines[i].SubmitDate,
              submitQty: lines[i].SubmitQty == 0 ? "" : lines[i].SubmitQty,
              scrapQty: lines[i].ScrapQty == 0 ? "" : lines[i].ScrapQty,
              wasteQty: lines[i].WasteQty == 0 ? "" : lines[i].WasteQty,
              beat: lines[i].BeatTime == 0 ? "" : lines[i].BeatTime,
              weight: lines[i].Weight == 0 ? "" : lines[i].Weight,
            };

            rs.push(r);
          }
          that.setData({
            dataLine: rs,
            modalName: null,
            workQty: null,
            scrapQty: null,
            wasteQty: null,
            beat: null,
            weight: null,
            memo: null,
          });

          that.WorkComplete(cklist);
        }

        if (msg != "报工成功") {
          wx.hideLoading();
          wuxToast(msg);
        }
      } else {
        wx.hideLoading();
        wuxToast("报工失败");
      }
    };

    let fl = () => {
      wx.hideLoading();
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == "none") wuxToast("请检查网络是否正常");
          else wuxToast("报工失败");
        },
      });
    };

    http(url, "POST", cklist, suc, fl);
  },
  //生成完工报告
  WorkComplete: function (cklist) {
    let that = this;
    let entCode = app.globalData.entCode;
    let url =
      app.globalData.apiBase +
      "/U9SVCHandler.ashx?api=UFIDA.U9.Cust.SHIMGE.MO.WorkOrderSV.IWorkCompleteSV" +
      "&entcode=" +
      entCode;
    console.log(url);

    let suc = (rd) => {
      wx.hideLoading();

      if (rd.d) {
        wuxToast("报工成功;但生成完工报告失败,原因:" + rd.d);
      } else {
        wuxToast("报工成功");
      }
    };

    let fl = () => {
      wx.hideLoading();
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == "none") wuxToast("请检查网络是否正常");
          else wuxToast("报工成功;但生成完工报告失败");
        },
      });
    };

    http(url, "POST", cklist, suc, fl);
  },
  //跳转至生产任务列表
  workTaskList: function () {
    wx.navigateTo({
      url: "../wk-worktask/wk-worktask",
    });
  },
  //查看BOM信息
  viewBom: function () {
    console.log(this.data.dataMain);
    console.log(this.data.user);

    let org = this.data.user.org;
    let code = this.data.searchKey;

    if (
      this.data.dataMain &&
      this.data.dataMain.orgCode &&
      this.data.dataMain.moDocNo
    ) {
      org = this.data.dataMain.orgCode;
      code = this.data.dataMain.moDocNo;
    }

    wx.navigateTo({
      url: "wk-worksheet-pick/wk-worksheet-pick?org=" + org + "&docno=" + code,
    });
  },
  /**
   * 查看图片
   */
  openPhoto: function (event) {
    if (
      this.data.dataMain.itemCode != null &&
      this.data.dataMain.itemCode != ""
    ) {
      this.getPhotos(this.data.dataMain.itemCode);
    }
  },
  closePhoto: function (event) {
    this.setData({ looked: true });
    this.setData({ showPhoto: false });
  },
  getPhotos: function (code) {
    let url = app.globalData.apiBase + "/api/photo/getPhotosByCode?code=" + code
    // let url = "http://127.0.0.1:80/api/photo/getPhotosByCode?code=" + code;
    console.log(url);
    let that = this;
    let success = function (res) {
      that.setData({ photo: res });
      let len = res.length;
      if (len == 0) {
        that.setData({ looked: true });
        wuxToast("无图片可直接报工");
        return;
      }
      let urls = [];
      for (let i = 0; i < len; i++) {

        console.log('图片地址',res[i],app.globalData.apiBase+'/FXImage/'+ res[i].name)

        urls.push(app.globalData.apiBase+'/'+ res[i].url,);
      }

      let sources = [];
      for (let i = 0; i < len; i++) {

        console.log('图片地址',res[i],app.globalData.apiBase+'/FXImage/'+ res[i].name)

        sources.push({
          url:app.globalData.apiBase+'/'+ res[i].url,
          type: "image",
        });
      }

      let succ = function (that) {
        that.setData({ looked: true });
      };    
      
      
      // wx.previewMedia({
      //   sources: sources,
      //   success: succ(that),
      // });

      wx.previewImage({
        urls: urls,
        success: succ(that),
      });
    };

    http(url, "get", null, success, null);
    console.log(this.data.photo);
  },
});
