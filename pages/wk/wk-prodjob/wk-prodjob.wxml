<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索车间,产线,单号" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="scanCode" class="cu-btn bg-shimge shadow-blur round"  disabled="{{isLoading}}" type="">扫码</button>
    </view>
  </view>

  <view class="cu-bar sg-white" style="min-height:80rpx">
    <view class="action">
      <scroll-view scroll-x class="tag-nav" scroll-with-animation scroll-left="{{scrollLeft2}}">
        <view class="cu-tag radius {{lxSelect.index!=0?'bg-current':'bg-gray'}}" data-current-item2="1" bindtap="tapconditionType">
          {{lxSelect.displayName}}<text class="cuIcon-{{dataItem==1 && isSelectPop2?'triangleupfill':'triangledownfill'}} lg"></text>
        </view>
        <view class="cu-tag radius {{ztSelect.index!=0?'bg-current':'bg-gray'}}" data-current-item2="2" bindtap="tapconditionType">
          {{ztSelect.displayName}}<text class="cuIcon-{{dataItem==2 && isSelectPop2?'triangleupfill':'triangledownfill'}} lg"></text>
        </view>
        <view class="cu-tag radius bg-current" data-current-item2="3">
          <picker mode="date" value="{{curDate}}" bindchange="DateChange">
            <view class="picker">
              {{filter.formatDay(curDate)}}
            </view>
          </picker>
        </view>
      </scroll-view>
    </view>
  </view>
  <scroll-view class="sg-select" scroll-y wx:if="{{isSelectPop2}}">
      <block wx:for="{{popSelect.items}}" wx:for-item="item" wx:key="value">
        <view class="cu-form-group" data-value="{{item.value}}" bindtap="tapPopSelect">
          <view class="title {{item.checked?'text-shimge':''}}">{{item.name}}</view>
          <text class="cuIcon-check lg text-shimge" wx:if="{{item.checked}}"></text>
        </view>
      </block>
  </scroll-view>  
</view>

<view class="bg-white"> 
  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="basis-p12 padding-sm">日期</view>
    <view class="basis-p19 padding-sm">单号</view>
    <view class="basis-p28 padding-sm">规格</view>
    <view class="basis-p14 padding-sm">类型</view>
    <view class="basis-p13 padding-sm">数量</view>
    <view class="basis-p14 padding-sm">状态</view>
  </view>
  <block wx:for="{{dataList}}" wx:for-item="item" wx:key="id">
    <view class="flex align-center solids-bottom sg-row" bindtap="taprow" data-id="{{item.id}}">
      <view class="basis-p12 padding-xs text-multcut">{{filter.formatDay(item.releaseDate)}}</view>
      <view class="basis-p19 padding-xs text-multcut">{{item.docNo}}</view>
      <view class="basis-p28 padding-xs text-multcut">{{item.itemMaster_SPECS}}</view>
      <view class="basis-p14 padding-xs text-multcut">{{item.itemJobType}}</view>
      <view class="basis-p13 padding-xs text-multcut text-center">{{item.releaseQty}}</view>
      <view class="basis-p14 padding-xs text-multcut">{{item.jobState}}</view>
    </view>
  </block>
  <view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
  <view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>
</view>
