const app = getApp()
import { wuxToast, http, formatDate, formatTime,updateAccountExpire } from '../../../../utils/util.js'

Page({
  data: {
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    isError: false,//加载失败       
    arrtLine: [],//明细
    id: null, //主键
    user: null, //当前用户
    showbar: false, //是否显示工具栏
  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;
    this.data.id = options.id;

    this.getRequestData(this.data.id);
  },
  //获取后台数据
  getRequestData: function (id) {
    if (!id)
      return;    
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    //隐藏加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let org = this.data.user.org;
    let user = this.data.user.code;
    let url = app.globalData.apiBase + '/api/WKPaintTask/' + id + '/' + user + '/' + org;

    console.log(url);

    let suc = rd => {
      //更新数据
      let rows = [];
      if (rd.length > 0) {
        rows = rd;
      }

      let showbar = false;
      if(rows && rows.length>0 && rows[0].jobType_Name=='喷漆' && rows[0].completeQty<rows[0].releaseQty){
        showbar = true;
      }

      that.setData({
        arrtLine: rows,
        showbar: showbar
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        arrtLine: []
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData(this.data.id);
  },
  //开工
  workStart:function(){
    this.workTask('开工');
  },
  workEnd:function(){
    this.workTask('完工');
  },
  //取字符串的结尾
  getLastStr:function(str){
    var arr =  str.split('/');
    if(arr && arr.length>0)
      return arr[arr.length-1];
    else
      return '';
  },  
  //刷新数据
  updateData:function(data){

    let pages = getCurrentPages(); //获取当前页面栈
    let prevPage = pages[pages.length - 2]; //上一页

    if(this.getLastStr(prevPage.route) == 'wk-prodjob')
      prevPage.updateState(data);

  },
  //开工/完工
  workTask: function(type){

    let that = this;
    let url = app.globalData.apiBase + '/api/WKPaintTask';
    console.log(url);
    wx.showLoading({ title: '正在提交...', mask: true });
    let data={};
    data.id= that.data.id;
    data.type=type;

    let suc = rd => {

      wx.hideLoading();
      if(rd && rd.length>0 && rd[0].msg)
        wuxToast(rd[0].msg);
      else if(rd && rd.length>0){
        var d = rd[0].data;
        if(that.data.arrtLine!=null && that.data.arrtLine.length>0){
          if(type=='开工'){
            that.data.arrtLine[0].realStartDate = formatTime(d.realStartDate);

            that.updateData(d);
          }
          else if(type=='完工'){
            that.data.arrtLine[0].realEndDate = formatTime(d.realEndDate);
            that.data.arrtLine[0].completeQty = d.completeQty;
            that.data.arrtLine[0].realHours = d.realHours;

            that.updateData(d);
          }

          that.setData({arrtLine:that.data.arrtLine});
        }
      }
    };

    let fl = () => {
      wx.hideLoading();
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
          else
            wuxToast('操作失败');
        }
      });
    };

    http(url, 'POST', data, suc, fl);
  },   
})