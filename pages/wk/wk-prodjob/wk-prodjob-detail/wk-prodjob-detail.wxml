<wxs module="filter" src="../../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view wx:if="{{!isError}}">
  <block wx:for="{{arrtLine}}" wx:for-item="item"  wx:key="id" wx:for-index="idx">
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="label sg-mwidth2">部门:</view>
        <view class="content">{{item.department_Name}}</view>
      </view>
      <view class="cust-col">
        <view class="label sg-mwidth2">产线:</view>
        <view class="content">{{item.productLineName}}</view>   
      </view> 
    </view>
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="label sg-mwidth3">工单号:</view>
        <view class="content">{{item.docNo}}</view>
      </view>
      <view class="cust-col">
        <view class="label sg-mwidth4">计划数量:</view>
        <view class="content">{{item.releaseQty}}</view>   
      </view> 
    </view>
    <view class="cust-row">
      <view class="label sg-mwidth2">品名:</view>
      <view class="content">{{item.itemMaster_Name}}</view>
    </view>
    <view class="cust-row">
      <view class="label sg-mwidth2">规格:</view>
      <view class="content">{{item.itemMaster_SPECS}}</view>
    </view>
    <view class="cust-row" wx:if="{{item.custName}}">
      <view class="label sg-mwidth2">客户:</view>
      <view class="content">{{item.custName}}</view>
    </view>
    <view class="cust-row" wx:if="{{item.deliveryDate}}">
      <view class="label sg-mwidth4">交货日期:</view>
      <view class="content">{{filter.formatDate(item.deliveryDate)}}</view>
    </view>        
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="label sg-mwidth4">喷漆选项:</view>
        <view class="content">{{item.paintType_Name}}</view>
      </view>
      <view class="cust-col">
        <view class="label sg-mwidth2">类型:</view>
        <view class="content">{{item.itemJobType}}</view>
      </view>     
    </view>
    <view class="cust-row">
      <view class="label sg-mwidth4">喷漆颜色:</view>
      <view class="content">{{item.paintInfo}}</view>
    </view> 
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="label sg-mwidth4">计划开工:</view>
        <view class="content">{{filter.formatDate(item.startDate)}}</view>
      </view>
      <view class="cust-col">
        <view class="label sg-mwidth4">计划完工:</view>
        <view class="content">{{filter.formatDate(item.completeDate)}}</view>
      </view>     
    </view>       
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="label sg-mwidth4">实际开工:</view>
        <view class="content">{{filter.formatDate(item.realStartDate)}}</view>
      </view>
      <view class="cust-col">
        <view class="label sg-mwidth4">实际完工:</view>
        <view class="content">{{filter.formatDate(item.realEndDate)}}</view>
      </view>     
    </view>
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="label sg-mwidth4">实际用时:</view>
        <view class="content">{{item.realHours==0?'':filter.formatDecimal(item.realHours,2)}}</view>
      </view>
      <view class="cust-col">
        <view class="label sg-mwidth4">完工数量:</view>
        <view class="content">{{item.completeQty}}</view>
      </view>     
    </view>    
  </block>
  <view wx:if="{{showbar}}" class="cu-bar tabbar foot bg-shimge">
    <view class="submit" bindtap="workStart">开 工</view>
    <view class="submit solids-left" bindtap="workEnd">完 工</view>
  </view>    
</view>
<view class="cu-load bg-red erro" wx:else></view>  