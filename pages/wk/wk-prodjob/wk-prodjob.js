const app = getApp()
import { wuxToast, http, formatDate, formatTime, updateAccountExpire,dateAdd } from '../../../utils/util.js'

Page({
  data: {
    scrollLeft: 0,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    loadState: 0,//加载状态  1:继续下拉加载更多,2:加载中,3:没有更多了
    isError:false,//加载失败
    dataList:[],//作业列表
    searchKey:'',//搜索关键字
    user:null, //当前用户
    curDate:null,
    scrollLeft2: 0,
    isSelectPop2: false,//下拉选择列表
    popSelect: null,  //下拉选择列表(作业类型，状态)) 
    dataItem: 1,
    lxSelect: {
      index: 0,
      displayName: '类型',
      items: [
        { name: '全部', value: 0, checked: true, displayName: '类型' },
        { name: '喷漆', value: 1, displayName: '喷漆' },
        { name: '组装', value: 2, displayName: '组装' },
        { name: '前工序', value: 3, displayName: '前工序' }
    ]},
    ztSelect: {
      index: 0,
      displayName: '状态',
      items: [
        { name: '全部', value: 0, checked: true, displayName: '状态' },
        { name: '已完工', value: 1, displayName: '已完工' },
        { name: '未完工', value: 2,  displayName: '未完工' }
    ]},      
  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;
    
    let now = new Date();
    let curDate = formatDate(now) ;
    this.setData({curDate: curDate});

    this.getRequestData('',true);
  },
  //查询时间
  DateChange: function(e){
    this.setData({curDate: e.detail.value});

    this.getRequestData('',true, this.goTop);
  },
  //获取后台数据
  getRequestData: function (docno,again, callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    if (again) {
      //重新获取数据
      this.data.dataList = [];
    }
    else{
      //上拉加载提示
      this.setData({ loadState: 2 });  
    }
    console.log('id:' + this.data.bottomLineId);
    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.personID;
    let key = this.data.searchKey;
    let sdate = this.data.curDate;

    let lx = this.data.lxSelect.index;
    let zt = this.data.ztSelect.index;

    let url = app.globalData.apiBase + '/api/WKPaintTask?key=' + key+'&docno='+docno+'&org='+org+'&itype='+lx+'&istate='+zt
                                     +'&sdate='+sdate;

    console.log(url);

    let suc = rd => {
      //更新数据
      let totalData = that.data.dataList;
      //判断数据大小是否等于每页数大小，相等则认为还有数据，否则认为没有更多数据
      let hasmore = rd.length == pagesize ? 1 : 0;
      if (rd.length > 0) {
        totalData = totalData.concat(rd);

        if(docno){
          let curDate = formatDate(rd[0].releaseDate) ;
          that.setData({curDate: curDate});
        }
      }

      that.setData({
        loadState: hasmore,
        dataList: totalData
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;

      if (callBack)
        callBack(rd);
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        loadState: 0,
        dataList: []
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false; 
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });     
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading){
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else{
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress>0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //搜索框输入完成
  confirmKeyInput: function(e){
    console.log(e.detail);

    this.data.searchKey = e.detail.value;

    this.getRequestData('',true, this.goTop);
  },
  //选择作业类型，状态
  tapconditionType: function (e) {

    let pop = this.data.isSelectPop2;
    if (e.currentTarget.dataset) {
      let cur = e.currentTarget.dataset.currentItem2;
      let tag = this.data.dataItem;

      if (cur == tag) {
        let sl = this.data.popSelect;
        if (cur == 1)
          sl = this.data.lxSelect;//作业类型
        else if (cur == 2)
          sl = this.data.ztSelect;//状态

        this.setData({
          dataItem: cur,
          scrollLeft2: (cur-1) * 30,
          popSelect: sl,
          isSelectPop2: !pop,
        });
      }
      else {
        //避免加载数据时又去服务器取数据
        if (this.data.isLoading)
          return;

        this.setData({
          dataItem: cur,
          scrollLeft2: (cur - 1) * 30,
          isSelectPop2: false,
        });

      }
    }

  }, 
  //下拉(作业类型,状态)选择点击事件
  tapPopSelect: function (e) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //改变下拉列表的当前值
    let index = e.currentTarget.dataset.value;
    let popSelect = this.data.popSelect;
    popSelect.index = index;
    popSelect.displayName = popSelect.items[index].displayName;
    for (let i = 0; i < popSelect.items.length; i++) {
      popSelect.items[i].checked = popSelect.items[i].value == index;
    }
    this.setData({ popSelect: popSelect });

    let da = this.data.dataItem;
    if(da == 1){

      this.setData({ lxSelect: popSelect });
    }
    else if (da == 2){

      this.setData({ ztSelect: popSelect });
    }

    //延迟隐藏下拉列表
    setTimeout(() => {
      this.setData({ isSelectPop2: false });
    }, 200);

    this.getRequestData('',true, this.goTop);
  },   

  //滚动条回到顶部
  goTop: function () {
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //搜索文本改变事件
  bindKeyInput:function(e){
    console.log(e.detail);

    this.data.searchKey = e.detail.value;
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData('',true);
  },
  //上拉加载更多
  onReachBottom: function () {
    if (this.data.loadState == 0 ||this.data.loadState == 3)
      return;
    //this.getRequestData(false);
  },
  //跳转至明细页
  taprow: function (e) {
    console.log(e.currentTarget.dataset);

    let id = e.currentTarget.dataset.id;
    if (id){
      wx.navigateTo({
        url: 'wk-prodjob-detail/wk-prodjob-detail?id=' + id 
      });
    }

  },
  //更新完工状态
  updateState: function(data){
    if(this.data.dataList && data){
      for(let i=0;i<this.data.dataList.length;i++){
        let d = this.data.dataList[i];
        if(d.id == data.id){
          d.realStartDate = formatTime(data.realStartDate);
          d.realEndDate = formatTime(data.realEndDate);
          d.completeQty = data.completeQty;
          d.realHours = data.realHours;
          if(d.completeQty && d.completeQty>=d.releaseQty){
            d.jobState = '已完工';
          }
          break;
        }
      }
      this.setData({dataList: this.data.dataList});
    }
  },
  //扫码
  scanCode:function(){
    let that = this;

    wx.scanCode({
      onlyFromCamera: true,
      success (res) {
        console.log(res)

        //清除选项
        let pop1 = that.data.lxSelect;
        pop1.index = 0;
        pop1.displayName = pop1.items[0].displayName;
        for (let i = 0; i < pop1.items.length; i++) {
          pop1.items[i].checked = pop1.items[i].value == 0;
        }

        let pop2 = that.data.ztSelect;
        pop2.index = 0;
        pop2.displayName = pop2.items[0].displayName;
        for (let i = 0; i < pop2.items.length; i++) {
          pop2.items[i].checked = pop2.items[i].value == 0;
        }

        that.setData({ searchKey: res.result, lxSelect: pop1, ztSelect: pop2 });

        that.getRequestData(res.result,true, that.goTop);
      }
    });
  },
})