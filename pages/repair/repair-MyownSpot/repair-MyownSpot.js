// pages/repair/repair-MyownSpot/repair-MyownSpot.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    actionSheetHidden: true,
    actionSheetItems: [
      { bindtap: 'Menu1', txt: '进入我的点检表' },
      { bindtap: 'Menu2', txt: '跳转生成点检表' }
    ],
    TapA:'',//存储点击行后的数据,设备编码
    TapB:'',//存储点击行后的数据,MainID
    loadState:0,//加载状态  1:加载中,2:没有更多了
    pagenum:0,
    pagesize:50,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    inputvalue:'',//搜索字段
    postList:{} //数组信息
  },

  actionSheetbindchange: function () {
    this.setData({
      actionSheetHidden: !this.data.actionSheetHidden
    })
  },

  //查询按钮及input框确认
  searchData:function(){
    //查询按钮先失效，防止多次点击
    this.setData({
      postList: {},
      loadState:0,
      pagenum: 0,//分页记录数
      pagesize: 50,//分页大小
      loadProgress:0,
    })
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',  
      data: {
        api: 'MyownSpotsearch',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        inputvalue:that.data.inputvalue,
        ReporterCode:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '没有数据！',
            icon: 'none',
            duration: 2000
          })
        that.setData({
          isLoading:false //按钮启用
        })
        }else{
        that.setData({
          postList:res.data,
          isLoading:false //按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'MyownSpotsearch',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        inputvalue:that.data.inputvalue,
        ReporterCode:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '没有数据！',
            icon: 'none',
            duration: 2000
          })
        that.setData({
          isLoading:false //按钮启用
        })
        }else{
        that.setData({
          postList:res.data,
          isLoading:false //按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    //提交过后，第二次回到界面，为了之前已经提交的行不出现，这边先给数组清空，再按照条件重新查询加载一遍
    this.setData({
      postList:{}
    })
    this.onLoad()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    //查询按钮先失效，防止多次点击
    this.setData({
      postList: {},
      loadState:0,
      pagenum: 0,//分页记录数
      pagesize: 50,//分页大小
      loadProgress:0,
    })
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条
      //隐藏加载动画,停止下拉刷新
    wx.hideNavigationBarLoading();
    wx.stopPullDownRefresh();

    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'MyownSpotsearch',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        inputvalue:that.data.inputvalue,
        ReporterCode:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '没有数据！',
            icon: 'none',
            duration: 2000
          })
        that.setData({
          isLoading:false //按钮启用
        })
        }else{
        that.setData({
          postList:res.data,
          isLoading:false //按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('加载更多');
    this.setData({
      loadState:1,
      pagenum: this.data.pagenum+1,
      loadProgress:0,
    })
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'MyownSpotsearch',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        inputvalue:that.data.inputvalue,
        ReporterCode:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '暂无更多数据',
            icon: 'none',
            duration: 1000
          }),
          that.setData({
            loadState:2,
            pagenum:that.data.pagenum-1,
            isLoading:false //按钮启用
          })
        }else{
          wx: wx.showToast({
            title: '数据加载完毕',
            icon: 'none',
            duration: 1000
          }),
        that.setData({
          postList:that.data.postList.concat(res.data),
          loadState:0,
          isLoading:false //按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },
  bindKeyInput:function(e){
    this.setData({
      inputvalue:e.detail.value
    })
  },

  //加载进度,状态控制
  loadProgress() {
      let loading = this.data.isLoading;
      let progress = this.data.loadProgress;
      if (loading) {
        progress = progress + 10;
        if (progress > 100 && progress < 110)
          progress = 100;
        else if (progress >= 110)
          progress = 10;
      }
      else {
        progress = 0;
      }
  
      this.setData({
        loadProgress: progress,
        isLoading: loading
      });
  
      if (progress > 0 && progress <= 100) {
        setTimeout(() => {
          this.loadProgress();
        }, 100)
      }
    },
    //跳转明细界面
    onTapToDetail(event) {
      let post = event.currentTarget.dataset;
      this.setData({
        actionSheetHidden: !this.data.actionSheetHidden,
        TapA:post.index[0],
        TapB:post.index[4],
      })
      console.log(post.index[0]+'--'+post.index[4]);
    },

    bindMenu1: function () {//进入我的点检表
      console.log('进入我的点检表');
      this.setData({
        actionSheetHidden: !this.data.actionSheetHidden
      })
      var tapa=this.data.TapA;//编码
      var tapb=this.data.TapB;//id
      wx.navigateTo({
        url: '../repair-Spotcheck/repair-Spotcheck?MainID=' + tapb +'&equipmentinput='+ tapa
      });
    },

    bindMenu2: function () {//跳转生成点检表
      console.log('跳转生成点检表');
      this.setData({
        actionSheetHidden: !this.data.actionSheetHidden
      })
      var tapa=this.data.TapA;//编码
      wx.navigateTo({
        url: '../repair-Spotcheck/repair-Spotcheck?assetNumber=' + tapa
      });
    },
})