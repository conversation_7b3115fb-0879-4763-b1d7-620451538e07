<template name="myownspotitem">
<view class="statusA" wx:if="{{item[3]=='已基础点检'&&item[6]=='0'}}">
 <view class="statusB" style="width:20%">{{item[5]}}</view>
<view class="statusB" style="width:20%">{{item[0]}}</view>
<view class="statusB" style="width:20%">{{item[1]}}</view>
<view class="statusB" style="width:20%">{{item[2]}}</view>
<view class="statusB" style="width:20%;color:red">{{item[3]}}</view>
</view>
<view class="statusA" wx:if="{{item[3]=='已基础点检'&&item[6]=='1'}}">
 <view class="statusB" style="width:20%">{{item[5]}}</view>
<view class="statusB" style="width:20%">{{item[0]}}</view>
<view class="statusB" style="width:20%">{{item[1]}}</view>
<view class="statusB" style="width:20%;color:blue">{{item[2]}}</view>
<view class="statusB" style="width:20%;color:red">{{item[3]}}</view>
</view>
<view class="statusA" wx:if="{{item[3]=='已基础点检'&&item[6]=='2'}}">
 <view class="statusB" style="width:20%">{{item[5]}}</view>
<view class="statusB" style="width:20%">{{item[0]}}</view>
<view class="statusB" style="width:20%">{{item[1]}}</view>
<view class="statusB" style="width:20%;color:red">{{item[2]}}</view>
<view class="statusB" style="width:20%;color:red">{{item[3]}}</view>
</view>
<view class="statusA" wx:if="{{item[3]=='点检完毕'&&item[6]=='0'}}">
 <view class="statusB" style="width:20%">{{item[5]}}</view>
<view class="statusB" style="width:20%">{{item[0]}}</view>
<view class="statusB" style="width:20%">{{item[1]}}</view>
<view class="statusB" style="width:20%">{{item[2]}}</view>
<view class="statusB" style="width:20%">{{item[3]}}</view>
</view>
<view class="statusA" wx:if="{{item[3]=='点检完毕'&&item[6]=='1'}}">
 <view class="statusB" style="width:20%">{{item[5]}}</view>
<view class="statusB" style="width:20%">{{item[0]}}</view>
<view class="statusB" style="width:20%">{{item[1]}}</view>
<view class="statusB" style="width:20%;color:blue">{{item[2]}}</view>
<view class="statusB" style="width:20%">{{item[3]}}</view>
</view>
<view class="statusA" wx:if="{{item[3]=='点检完毕'&&item[6]=='2'}}">
 <view class="statusB" style="width:20%">{{item[5]}}</view>
<view class="statusB" style="width:20%">{{item[0]}}</view>
<view class="statusB" style="width:20%">{{item[1]}}</view>
<view class="statusB" style="width:20%;color:red">{{item[2]}}</view>
<view class="statusB" style="width:20%">{{item[3]}}</view>
</view>
 </template>