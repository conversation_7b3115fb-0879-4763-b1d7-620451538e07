// pages/repair/repair-comment/repair-comment-detail/repair-comment-detail.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    index: null,
    picker: [[13,'工种A']],
    MainID:'',
    isComplete:false,
    t1:'',
    t2:'',
    t3:'',
    textareaAValue:'',
    commentType:''//维修评价
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      MainID:options.MainID
    })
    var that = this
    //获取维修评价的东西(满意 一般 不满意)
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'BasicCommentType',
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log('OnLoad:',res);//打印请求返回的结果
        that.setData({
          picker:res.data
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //获取维修工单信息
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'EquipmentComment',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          t1:res.data[0][0],
          t2:res.data[0][1],
          t3:res.data[0][2]
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this
    //获取维修评价的东西(满意 一般 不满意)
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'BasicCommentType',
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log('OnLoad:',res);//打印请求返回的结果
        that.setData({
          picker:res.data
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //维修评价的选择框
  PickerChange(e) {
    console.log(e);
    this.setData({
      index: e.detail.value
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  //维修评价
  textareaAInput(e) {
    this.setData({
      textareaAValue: e.detail.value
    })
  },
  //评价完成按钮--点击按钮提交信息，把状态改成5
  btnComplete:function(e){
    var that=this
    
    if(that.data.index==null){
      wx: wx.showToast({
        title: '请先选择维修评价,谢谢!',
        icon: 'none',
        duration: 2000
      })
      return;
    }

    wx.showModal({
    title:'评价完毕',
    content:'确认完成吗？',
    success:function(res){
      if(res.confirm){
        console.log('确认完成');

        wx.request({
          url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
          data: {
            api: 'EquipmentCommentComplete',
            MainID:that.data.MainID,
            RepairComment:that.data.textareaAValue,
            commentType:that.data.picker[0][that.data.index][1],
          },
          method: 'POST',
          header: { 'Content-Type': 'application/x-www-form-urlencoded' },
          success: function (res) {
            // success
            console.log(res);//打印请求返回的结果
            if(res.data=='提交成功'){
            //提醒
            wx: wx.showToast({
              title: '评价成功！',
              icon: 'none',
              duration: 2000
            })
            //提交成功后就回到列表界面，免得再次提交影响数据,redirectTo关闭当前页面，延时2秒执行，让别人看清楚提交成功的信息
            //把按钮都失效，防止在这2秒的时间内被人点击
            that.setData({
              isComplete:true
            })
            setTimeout(function(){
            wx.redirectTo({
              url: '../../../repair/repair-comment/repair-comment'
            })
            },2000)
            }else if(res.data=='提交失败'){
            //提醒
            wx: wx.showToast({
              title: '提交失败！',
              icon: 'none',
              duration: 2000
            })
            }
          },
          fail: function (res) {
            // fail
          },
          complete: function (res) {
            // complete
          }
        })

      }else if(res.cancel){
        console.log('取消完成')
      }
    }
  })}
})