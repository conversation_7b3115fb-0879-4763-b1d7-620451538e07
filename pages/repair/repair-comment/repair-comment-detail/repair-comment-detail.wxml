<!--pages/repair/repair-comment/repair-comment-detail/repair-comment-detail.wxml-->
    <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">维修工单信息</text>
    </view>
  </view>

    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">维修人员:</view>
      <text selectable="true" class="sg-content">{{t1}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">故障原因分析:</view>
      <text selectable="true" class="sg-content">{{t2}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">维修处理情况:</view>
      <text selectable="true" class="sg-content">{{t3}}</text>
    </view>

    <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">维修评价</text>
    </view>
  </view>

	<view class="cu-form-group">
		<view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">维修评价：</view>
		<picker bindchange="PickerChange" value="{{index}}" range="{{picker[0]}}" range-key="{{[1]}}">
			<view class="picker" style="text-align:left;color:gray">
				{{index?picker[0][index][1]:'请选择评价内容'}}
			</view>
		</picker>
	</view>

    <view class="cu-form-group" style="border-top: 0rpx">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">详细评价：</view>
    </view>
  	<view class="cu-form-group" style="border-top: 0rpx;height:400rpx">
		<textarea maxlength="-1" disabled="{{modalName!=null}}" value="{{textareaAValue}}" bindinput="textareaAInput" placeholder="请填写详细评价(可不填)" style="height:400rpx;border: 1px dashed gray;text-color:gray;padding:5rpx;font-size:28rpx"></textarea>
	</view>

  <view class="padding flex" style="flex-direction:row;padding-top:50rpx">
  <button class="send-btn bg-shimge lg" style="font-size:30rpx" bindtap="btnComplete" disabled="{{isComplete}}">评价完毕</button>
</view>

