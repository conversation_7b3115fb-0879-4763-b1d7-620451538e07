// pages/repair/repair-send/repair-send.js
const app = getApp();
var time = require('../../../utils/util.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    index: null,
    picker: [[13,'工种A']],
    datetime:time.formatTime2(new Date()),
    modalName: null,
    textareaAValue:'',
    placeholder:'自动带出设备名称',
    equipmentinput:'',
    equipmentname:'',
    usercode:'',//用户编码
    username:'',//用户名称
    userphone:'',//用户电话
    worktype:'',//工种
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    //在加载这边再获取一次时间，不然他不会刷新
    this.setData({
      datetime:time.formatTime2(new Date()),
    })
    //加载需求工种的信息
    var that=this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'BasicRepairWorkType',
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log('OnLoad:',res);//打印请求返回的结果
        that.setData({
          picker:res.data
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //获取当前用户的电话号码，后续存入数据库
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'BasicPersonPhone',
        PersonName:app.globalData.accountInfo.name
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log('OA联系方式:',res);//打印请求返回的结果
        that.setData({
          userphone:res.data[0][1]
        })
    //如果电话号码在OA这边获取不到，那就在U9这边的人员档案中查，实在没有就显示未维护
    if(that.data.userphone=='未记录'){
      wx.request({
        url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
        data: {
          api: 'BasicPersonPhone2',
          PersonID:app.globalData.accountInfo.personID
        },
        method: 'POST',
        header: { 'Content-Type': 'application/x-www-form-urlencoded' },
        success: function (res) {
          // success
          console.log('OA上电话号码未维护，已自动寻踪U9资料重新获取:',res);//打印请求返回的结果
          that.setData({
            userphone:res.data[0][0]
          })
        },
        fail: function (res) {
          // fail
        },
        complete: function (res) {
          // complete
        }
      })
    }
    //U9取数结束
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    //加载需求工种的信息
    var that=this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'BasicRepairWorkType',
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log('OnShow:',res);//打印请求返回的结果
        that.setData({
          picker:res.data
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  //需求工种的选择框
  PickerChange(e) {
    console.log(e);
    this.setData({
      index: e.detail.value
    })
  },
  //datetimepicker
  handleChange(e) {
    console.log(e)
    this.setData({
      datetime: e.detail.dateString
    })
  },
  //故障描述
  textareaAInput(e) {
    this.setData({
      textareaAValue: e.detail.value
    })
  },
  //设备编号input
  equipmentinput:function(e){
    this.setData({
      equipmentinput: e.detail.value
    })
  },
  //扫码功能把设备编号带过来
  btnscan:function(e){
  var that=this
  //微信自带扫码功能
  wx.scanCode({
    onlyFromCamera: true,
    success (res) {
      console.log(res)
      //赋值
      that.setData({ equipmentinput: res.result});
      //调用一下设备编号模糊焦点的方法
      that.equipmentblur();
    }
    });
  },
  //设备编号输入框失去焦点事件，需要带出设备名称，没有的话就注上没有
  //input框失去焦点事件，因为有些人输入信息后不会点确认,他的执行优先级在input后面，所以也可以实时获取数据
  equipmentblur:function(e){
    var that=this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'equipmentgetname',
        equipmentinput:that.data.equipmentinput,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          equipmentname:res.data[0][0]
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //放弃按钮--点击按钮清空信息
  btncancel:function(e){
  var that=this
  wx.showModal({
  title:'放弃',
  content:'确认放弃吗？',
  success:function(res){
    if(res.confirm){
      console.log('确认放弃');
      that.setData({
        index: null,
        datetime:time.formatTime2(new Date()),
        textareaAValue:'',
        placeholder:'自动带出设备名称',
        equipmentinput:'',
        equipmentname:'',
      })
    }else if(res.cancel){
      console.log('取消放弃')
    }
  }
})},
  //提交按钮
  btnsend:function(e){
    var that=this
    wx.showModal({
    title:'提交',
    content:'确认提交吗？',
    success:function(res){
      if(res.confirm){
        console.log('确认提交');
        if(that.data.equipmentinput==''|| that.data.datetime=='请选择计划维修日期'||that.data.index==null||that.data.textareaAValue==''){
          wx: wx.showToast({
            title: '故障报修信息未填写完整！',
            icon: 'none',
            duration: 2000
          })
        }else if(that.data.equipmentname=='无此设备信息'){
          wx: wx.showToast({
            title: '当前设备不存在\r\n请填写正确的设备编码！',
            icon: 'none',
            duration: 2000
          })
        }else{//提交数据插入到数据库中去
          wx.request({
            url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
            data: {
              api: 'equipmentinsert',
              usercode:app.globalData.accountInfo.personID,
              username:app.globalData.accountInfo.name,
              userphone:that.data.userphone,
              equipmentinput:that.data.equipmentinput,
              equipmentname:that.data.equipmentname,
              textareaAValue:that.data.textareaAValue,
              datetime:that.data.datetime,
              worktype:that.data.picker[0][that.data.index][1],
              LoginOrg:app.globalData.accountInfo.org
            },
            method: 'POST',
            header: { 'Content-Type': 'application/x-www-form-urlencoded' },
            success: function (res) {
              // success
              console.log(res);//打印请求返回的结果
              if(res.data=='提交成功'){
              //复原
              that.setData({
                index: null,
                datetime:time.formatTime2(new Date()),
                textareaAValue:'',
                placeholder:'自动带出设备名称',
                equipmentinput:'',
                equipmentname:'',
              });
              //提醒
              wx: wx.showToast({
                title: '提交成功！',
                icon: 'none',
                duration: 2000
              })
              }else if(res.data=='提交失败'){
              //提醒
              wx: wx.showToast({
                title: '提交失败！',
                icon: 'none',
                duration: 2000
              })
              }
            },
            fail: function (res) {
              // fail
            },
            complete: function (res) {
              // complete
            }
          })
        }
      }else if(res.cancel){
        console.log('取消提交')
      }
    }
  })
  }
})