<!--pages/repair/repair-SpotcheckWarning/repair-SpotcheckWarning.wxml-->
<import src="repair-SpotcheckWarning-item/repair-SpotcheckWarning-item.wxml" />
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search bg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索设备编号、名称、车间" confirm-type="search" bindinput="bindKeyInput" bindconfirm="searchData" value="{{inputvalue}}"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>

  <view class="title_wrap">
<view class="titleA">未点检设备预警{{warningQty}}</view>
</view>

  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="padding-sm" style="flex-basis:25%">设备编号</view>
    <view class="padding-sm" style="flex-basis:30%">设备名称</view>
    <view class="padding-sm" style="flex-basis:25%">设备规格</view>
    <view class="padding-sm" style="flex-basis:20%">车间</view>
  </view>
</view>

<view class="bg-white"> 
    <block wx:for="{{postList}}" wx:for-item="item" wx:for-index="idx">
    <view bindtap="onTapToDetail" data-index="{{item}}" wx:if="{{idx%2==0}}">
      <template is="soptcheckwarningitem" data="{{item}}" />
    </view>
    <view bindtap="onTapToDetail" data-index="{{item}}" class="block" wx:else>
      <template is="soptcheckwarningitem" data="{{item}}" />
    </view>
  </block>

<action-sheet hidden="{{actionSheetHidden}}" bindchange="actionSheetbindchange">
  <block wx:for-items="{{actionSheetItems}}">
    <action-sheet-item bindtap="bind{{item.bindtap}}">{{item.txt}}</action-sheet-item>
  </block>
  <action-sheet-cancel class="cancel">取消</action-sheet-cancel>
</action-sheet>

    <view class="cu-load {{(loadState==1?'loading':(loadState==2?'over':''))}}"></view>
    </view>