<!--pages/repair/repair-Spotcheck/repair-Spotcheck.wxml-->
<import src="repair-Spotcheck-item/repair-Spotcheck-item.wxml" />
    <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">设备点检基本信息</text>
    </view>
    <view class="action">
      <button bindtap="btnscan" class="cu-btn bg-shimge shadow-blur round" disabled="{{isok}}" type="">扫码</button>
    </view>
  </view>

  	<view class="cu-form-group">
		<view class="title">设备编号：</view>
		<input placeholder="请输入设备编号" style="font-size:28rpx" bindblur="equipmentblur" bindinput="equipmentinput" value="{{equipmentinput}}" disabled="{{isok}}"></input>
	</view>
  	<view class="cu-form-group">
		<view class="title">设备名称：</view>
		<input placeholder="{{placeholder}}" disabled="true" style="font-size:28rpx" value="{{equipmentname}}"></input>
	</view>
  	<view class="cu-form-group">
		<view class="title">设备规格：</view>
		<input placeholder="{{placeholder2}}" disabled="true" style="font-size:28rpx" value="{{equipmentspecs}}"></input>
	</view>
	  	<view class="cu-form-group">
		<view class="title">车　　间：</view>
		<input placeholder="{{placeholder3}}" disabled="true" style="font-size:28rpx" value="{{equipmentworkshop}}"></input>
	</view>

    <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">基础点检</text>
    </view>
    <view class="action">
      <button bindtap="btnonepunch" class="cu-btn bg-shimge shadow-blur round" disabled="{{isok}}" type="">未开机一键点检</button>
    </view>
  </view>

<!--正常点检上报使用的模板-->
    <block wx:for="{{postList}}" wx:for-item="item" wx:for-index="idx">
    <view data-index="{{item}}" wx:if="{{item[1]==1}}">
      <template is="Spotcheckitem" data="{{item,idx,isok}}" />
    </view>
    <view data-index="{{item}}" wx:else>
      <template is="Spotcheckitem2" data="{{item,idx,isok}}" />
    </view>
  </block>
<!--从点检列表过来的数据使用的模板-->
    <block wx:for="{{postList2}}" wx:for-item="item" wx:for-index="idx">
    <view data-index="{{item}}" wx:if="{{item[1]==1}}">
      <template is="ListSpotcheckitem" data="{{item,idx,isok}}" />
    </view>
    <view data-index="{{item}}" wx:else>
      <template is="ListSpotcheckitem2" data="{{item,idx,isok}}" />
    </view>
  </block>

    <view class="cu-bar bg-white" hidden="{{ishide}}">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">当日设备运行状况</text>
    </view>
  </view>

  <view class="cu-form-group" style="border-top: 0rpx" hidden="{{ishide}}">
    <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);font-weight:normal">
      <text>下班前15分钟是否擦拭设备，清理切削，打扫现场</text>
    </view>
  </view>
  <view class="flex  p-xs margin-bottom-sm mb-sm justify-center" hidden="{{ishide}}">
    <radio-group bindchange="mainscreenitem3">
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="yes" disabled="{{isuse}}" checked="{{beforework=='yes'?true:false}}"></radio>
        <text> 是</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="no" disabled="{{isuse}}" checked="{{beforework=='no'?true:false}}"></radio>
        <text> 否</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="nothing" disabled="{{isuse}}" checked="{{beforework=='nothing'?true:false}}"></radio>
        <text> 未开机</text>
      </label>
    </radio-group>
  </view>

  <view class="cu-form-group" style="border-top: 0rpx" hidden="{{ishide}}">
    <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);font-weight:normal">
      <text>下班后是否确认关闭设备电源(必填)</text>
    </view>
  </view>
  <view class="flex  p-xs margin-bottom-sm mb-sm justify-center" hidden="{{ishide}}">
    <radio-group bindchange="mainscreenitem4">
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="yes" disabled="{{isuse}}" checked="{{afterwork=='yes'?true:false}}"></radio>
        <text> 是</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="no" disabled="{{isuse}}" checked="{{afterwork=='no'?true:false}}"></radio>
        <text> 否</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="nothing" disabled="{{isuse}}" checked="{{afterwork=='nothing'?true:false}}"></radio>
        <text> 未开机</text>
      </label>
    </radio-group>
  </view>

  <view class="cu-form-group" style="border-top: 0rpx" hidden="{{ishide}}">
    <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);font-weight:normal">
      <text>当前工作日是否无其他异常现象(必填)</text>
    </view>
  </view>
  <view class="flex  p-xs margin-bottom-sm mb-sm justify-center" hidden="{{ishide}}">
    <radio-group bindchange="mainscreenitem">
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="yes" disabled="{{isuse}}" checked="{{todayiswrong=='yes'?true:false}}"></radio>
        <text> 是</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="no" disabled="{{isuse}}" checked="{{todayiswrong=='no'?true:false}}"></radio>
        <text> 否</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="nothing" disabled="{{isuse}}" checked="{{todayiswrong=='nothing'?true:false}}"></radio>
        <text> 未开机</text>
      </label>
    </radio-group>
  </view>

  <view class="cu-form-group" style="border-top: 0rpx" hidden="{{ishide}}">
    <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);font-weight:normal">
      <text>当前工作日异常状态(必填)</text>
    </view>
  </view>
  <view class="flex  p-xs margin-bottom-sm mb-sm justify-center" hidden="{{ishide}}">
    <radio-group bindchange="mainscreenitem2">
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="ok" disabled="{{isuse}}" checked="{{todaywhatswrong=='ok'?true:false}}"></radio>
        <text> 正常</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="holiday" disabled="{{isuse}}" checked="{{todaywhatswrong=='holiday'?true:false}}"></radio>
        <text> 放假</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="repair" disabled="{{isuse}}" checked="{{todaywhatswrong=='repair'?true:false}}"></radio>
        <text> 维修</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="missitem" disabled="{{isuse}}" checked="{{todaywhatswrong=='missitem'?true:false}}"></radio>
        <text> 缺料</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="noplan" disabled="{{isuse}}" checked="{{todaywhatswrong=='noplan'?true:false}}"></radio>
        <text> 无计划</text>
      </label>
    </radio-group>
  </view>

<!--外面套了view的textarea，disable属性关了的话文字就不会显示了，所以这边直接不对textarea做禁止动作，反正按钮被禁止了，数据也无法提交-->
    <view class="cu-form-group" style="border-top: 0rpx" hidden="{{ishide}}">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">当前工作日设备故障描述(必填)</view>
    </view>
  	<view class="cu-form-group" style="border-top: 0rpx;height:150rpx" hidden="{{ishide}}">
		<textarea maxlength="-1" value="{{textareaAValue}}" bindinput="textareaAInput" placeholder="请填写设备故障描述" style="height:200rpx;border: 1px dashed gray;text-color:gray;padding:5rpx;font-size:28rpx"></textarea>
	</view>
    <view class="cu-form-group" style="border-top: 0rpx" hidden="{{ishide}}">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">当前工作日异常停机时间(必填)</view>
    </view>
  	<view class="cu-form-group" style="border-top: 0rpx;height:100rpx;justify-content:center" hidden="{{ishide}}">
    <van-stepper value="{{textareaBValue}}" step="0.5" decimal-length="{{ 2 }}" input-width="100rpx" button-size="70rpx" min="0" disabled="{{isuse}}" bind:change="textareaBInput"/>
    <view class="title" style="font-size:28rpx">（小 时）</view>
	</view>
    <view class="cu-form-group" style="border-top: 0rpx" hidden="{{ishide}}">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">当前工作日实际开机时间(必填)</view>
    </view>
  	<view class="cu-form-group" style="border-top: 0rpx;height:100rpx;justify-content:center" hidden="{{ishide}}">
    <van-stepper value="{{textareaCValue}}" step="0.5" decimal-length="{{ 2 }}" input-width="100rpx" button-size="70rpx" min="0" disabled="{{isuse}}" bind:change="textareaCInput"/>
    <view class="title" style="font-size:28rpx">（小 时）</view>
	</view>

<view class="padding flex" style="flex-direction:row;padding-top:50rpx">
  <button class="send-btn bg-shimge lg" style="font-size:30rpx" bindtap="btnsave" disabled="{{isok}}">基础点检完毕</button>
  <button class="send-btn bg-shimge lg" style="font-size:30rpx" bindtap="btnsend" hidden="{{ishide}}" disabled="{{isuse}}">点检数据提交</button>
</view>