<template name="Spotcheckitem"><!--正常点检上报 选项是 是 否 未开机-->
  <view class="cu-form-group" style="border-top: 0rpx">
  <view class="padding-xs flex align-center">
  <view class="cuIcon-title text-blue"/>
  {{item[0]}}
  </view>
  </view>
  <view class="flex  p-xs margin-bottom-sm mb-sm justify-center">
    <radio-group bindchange="itemchoose">
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="{{item[2]}}-yes_{{idx}}" disabled="{{isok}}" ></radio>
        <text> 是</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="{{item[2]}}-no_{{idx}}" disabled="{{isok}}" ></radio>
        <text> 否</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="{{item[2]}}-nothing_{{idx}}" disabled="{{isok}}" ></radio>
        <text> 未开机</text>
      </label>
    </radio-group>
  </view>
 </template>

 <template name="ListSpotcheckitem"><!--点检列表过来的 选项是 是 否 未开机-->
  <view class="cu-form-group" style="border-top: 0rpx">
  <view class="padding-xs flex align-center">
  <view class="cuIcon-title text-blue"/>
  {{item[0]}}
  </view>
  </view>
  <view class="flex  p-xs margin-bottom-sm mb-sm justify-center">
    <radio-group>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="是" disabled="{{isok}}" checked="{{item[2]=='yes'?true:false}}"></radio>
        <text> 是</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="否" disabled="{{isok}}" checked="{{item[2]=='no'?true:false}}"></radio>
        <text> 否</text>
      </label>
      <label class="flex-sub padding-sm margin-xs radius">
        <radio class="green sm radio" value="未开机" disabled="{{isok}}" checked="{{item[2]=='nothing'?true:false}}"></radio>
        <text> 未开机</text>
      </label>
    </radio-group>
  </view>
 </template>


 <template name="Spotcheckitem2"><!--正常点检上报 现在暂定是输入数字-->
  <view class="cu-form-group" style="border-top: 0rpx">
  <view class="padding-xs flex align-center">
  <view class="cuIcon-title text-blue"/>
  {{item[0]}}
  </view>
  </view>
  <view class="flex  p-xs margin-bottom-sm mb-sm justify-center">
  <view>实际值：</view>
    <input type="digit" placeholder="请输入" bindinput="itemchange" id="{{item[2]}}-_{{idx}}" value="{{}}" disabled="{{isok}}"/>
  </view>
 </template>

 <template name="ListSpotcheckitem2"><!--点检列表过来的 现在暂定是输入数字-->
  <view class="cu-form-group" style="border-top: 0rpx">
  <view class="padding-xs flex align-center">
  <view class="cuIcon-title text-blue"/>
  {{item[0]}}
  </view>
  </view>
  <view class="flex  p-xs margin-bottom-sm mb-sm justify-center">
  <view>实际值：</view>
    <input type="digit" placeholder="请输入" value="{{item[2]}}" disabled="{{isok}}"/>
  </view>
 </template>