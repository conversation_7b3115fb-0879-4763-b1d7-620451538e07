// pages/repair/repair-Spotcheck/repair-Spotcheck.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    MainID:'',
    status:0,//控制点检表列表进来的时候控件是否可用
    placeholder:'自动带出设备名称',
    placeholder2:'自动带出设备规格',
    placeholder3:'自动带出车间',
    equipmentinput:'',
    equipmentname:'',
    equipmentspecs:'',
    equipmentworkshop:'',
    beforework:'',//下班前15分钟是否擦拭设备，清理切削，打扫现场
    afterwork:'',//下班后是否确认关闭设备电源
    todayiswrong:'',//当日设备运行状况是否异常信息
    todaywhatswrong:'',//当日设备运行状况哪种异常状况
    textareaAValue:'',
    textareaBValue:'',
    textareaCValue:'',
    oldpersonid:'',//因为现在管理员可以进其他人的点检表查看了，为了不让他提交，这边获取原单的创建人校验，不是同一个id的话无法提交数据
    postList:{},//正常点检上报的
    postList2:{},//点检表过来的
    ishide:true, //控制当前工作日和 点检数据提交按钮的显示与否
    isok:false,//控制本界面第一部分控件(设备编号和基础点检)当状态为1的时候就禁止使用
    isuse:false, //控制本界面的第二部分控件(当日设备运行状况)当状态是1的时候就全部禁止使用
    datacount:0, //返回点检表的个数
    radiovalue:[], //问题选项的数组，因为点击只能展示value，所以把item和idx的信息放一起了，这边是给他分开好后面使用，字符是 _ 下划线
    ans: new Array(0) //数组
  },
  //设备编号input
  equipmentinput:function(e){
    this.setData({
      equipmentinput: e.detail.value
    })
  },
  //设备编号输入框失去焦点事件，需要带出设备名称，没有的话就注上没有
  //input框失去焦点事件，因为有些人输入信息后不会点确认,他的执行优先级在input后面，所以也可以实时获取数据
  //equipmentblur逻辑解释：先request获取设备基本信息--再判断设备编码格子有无数据，数据是否正确--再request判断今天是否点检了--点检了后就报错，没点检再执行基础点检问题加载的request
  equipmentblur:function(e){
    var that=this
    //每次获取前先把基础点检之前加载的东西清空，免得影响
    that.setData({
      postList:{}
    })
        //获取设备基本信息
        wx.request({
          url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
          data: {
            api: 'SpotCheckInput',
            equipmentinput:that.data.equipmentinput,
            LoginOrg:app.globalData.accountInfo.org
          },
          method: 'POST',
          header: { 'Content-Type': 'application/x-www-form-urlencoded' },
          success: function (res) {
            // success
            console.log(res);//打印请求返回的结果
            that.setData({
              equipmentname:res.data[0][0],
              equipmentspecs:res.data[0][1],
              equipmentworkshop:res.data[0][2]
            })
    //判断该设备今天是否已经被点检了，这边判断一下，然后后面的基础点检处也判断一下，因为会有一起做单但未提交导致两个人同时操作同一台设备的现象
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'SpotCheckIsToday',
        EquipmentCode:that.data.equipmentinput,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        if(res.data.length==0){//说明没有返回数据，说明今天已经点检了
          if(that.data.equipmentinput==''||that.data.equipmentinput==null){
          wx: wx.showToast({
            title: '请输入设备编号，谢谢!',
            icon: 'none',
            duration: 2000
          })           
          }else if(that.data.equipmentname=='无此设备信息'){
          wx: wx.showToast({
            title: '请检查设备编号是否正确!',
            icon: 'none',
            duration: 2000
          })
          }else{
          wx: wx.showToast({
            title: '该设备已失效或今天已被点检，请勿重复点检!',
            icon: 'none',
            duration: 2000
          })
          }
        }else{
    console.log('今天未被点检，正常运行!')
    //获取基础点检表的信息
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'SpotCheckInputList',
        equipmentinput:that.data.equipmentinput,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '该设备没有基础点检表！',
            icon: 'none',
            duration: 2000
          })
        }else{
          wx: wx.showToast({
            title: '数据加载成功！',
            icon: 'none',
            duration: 2000
          })
          that.setData({
            postList:res.data,
            datacount:res.data[0][3]
            })
        that.data.ans=new Array(parseInt(that.data.datacount))//获取到的问题个数新建一个数组
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
          },
          fail: function (res) {
            // fail
          },
          complete: function (res) {
            // complete
          }
        })
  },
//radio单项点击
itemchoose:function(e){
  console.log(e)
  var that=this
  var a=e.detail.value.split("_")//这边把radio的value拆了
  that.setData({
    radiovalue:[a]//放到数组里面去
  })
  that.data.ans[that.data.radiovalue[0][1]]=that.data.radiovalue[0][0]//把数组元素按需分配到ans这个数组中去，后面就可以判断是否选项都填满了
  console.log(that.data.ans)
},
//选项输入
itemchange:function(e){
console.log(e)
let val = e.detail.value.replace(/(^\s*)|(\s*$)/g, "")
if (!val) {
  val=''
}
var reg = /[^\d.]/g
//只能是数字和小数点，不能是其他输入
val = val.replace(reg, "")
//保证第一位只能是数字，不能是点
val = val.replace(/^\./g, "");
//小数只能出现1位
val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
//小数点后面保留2位
val = val.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
e.detail.value=val;

var that=this
var a=e.currentTarget.id.split("_")//这边id拆开
that.setData({
  radiovalue:[a]//放到数组里面去
})
that.data.ans[that.data.radiovalue[0][1]]=that.data.radiovalue[0][0]+e.detail.value//把数组元素按需分配到ans这个数组中去，后面就可以判断是否选项都填满了
console.log(that.data.ans)
},
//主界面 当日设备运行状况的按钮事件
mainscreenitem:function(e){
  var that=this
  var b=e.detail.value
  that.setData({
    todayiswrong:b
  })
  },
mainscreenitem2:function(e){
  var that=this
  var c=e.detail.value
  that.setData({
    todaywhatswrong:c
  })
  },
  //下班前15分钟...
mainscreenitem3:function(e){
  var that=this
  var d=e.detail.value
  that.setData({
    beforework:d
  })
  },
  //下班后有没有关电
mainscreenitem4:function(e){
  var that=this
  var f=e.detail.value
  that.setData({
    afterwork:f
  })
  },
//主界面 当日设备运行状况的多选框事件
  textareaAInput(e) {
    this.setData({
      textareaAValue: e.detail.value
    })
  },
  textareaBInput(e) {
    this.setData({
      textareaBValue: e.detail
    })
  },
  textareaCInput(e) {
    this.setData({
      textareaCValue: e.detail
    })
  },
//基础点检完毕按钮
btnsave:function(){
  var that=this
  that.setData({
    isok:true //点击后把按钮禁用，避免重复点击
  })
  //判断该设备今天是否已经被点检了，因为会有一起做单但未提交导致两个人同时操作同一台设备的现象
  wx.request({
    url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
    data: {
      api: 'SpotCheckIsToday',
      EquipmentCode:that.data.equipmentinput,
      LoginOrg:app.globalData.accountInfo.org
    },
    method: 'POST',
    header: { 'Content-Type': 'application/x-www-form-urlencoded' },
    success: function (res) {
      // success
      if(res.data.length==0){//说明没有返回数据，说明今天已经点检了
        console.log('基础点检过程中被其他用户点检完成了!')
        if(that.data.equipmentinput==''||that.data.equipmentinput==null){
        that.setData({
          isok:false //解控
        })
        wx: wx.showToast({
          title: '请输入设备编号，谢谢!',
          icon: 'none',
          duration: 2000
        })           
        }else if(that.data.equipmentname=='无此设备信息'){
        that.setData({
          isok:false //解控
        })
        wx: wx.showToast({
          title: '请检查设备编号是否正确!',
          icon: 'none',
          duration: 2000
        })
        }else{
        that.setData({
          isok:false //解控
        })
        wx: wx.showToast({
          title: '在基础点检过程中，已有用户点检完成该设备，请退出点检!',
          icon: 'none',
          duration: 4000
        })
        }
      }else{
  console.log('正常运行!')
    var finish='false'
    var i=0
    //console.log(that.data.datacount)
    console.log(that.data.ans)
    while(i<that.data.datacount){
      if(that.data.ans[i]==""||that.data.ans[i]==null){
        finish='false'
        break;
      }else{
        finish='true'
      }
      i++;
    }
    if(finish=='false'){
      console.log("finish:"+finish)
      that.setData({
        isok:false //解控
      })
      wx: wx.showToast({
        title: '基础点检有未完成的选项！',
        icon: 'none',
        duration: 2000
      })
    }else{
      console.log("finish:"+finish)
    //把数组给转换成后台可以使用的字符串格式
    var ans2;
    ans2=that.data.ans.join(",");
    console.log("ans2:"+ans2);
    wx.showModal({
      title:'基础点检',
      content:'基础点检完毕？',
      success:function(res){
        if(res.confirm){
          console.log('基础点检完毕')
          that.setData({
            isok:false //解控
          })
          wx.request({
            url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
            data: {
              api: 'SpotCheckBasicSave',
              EquipmentCode:that.data.equipmentinput,
              EquipmentName:that.data.equipmentname,
              EquipmentSPECS:that.data.equipmentspecs,
              EquipmentWorkShop:that.data.equipmentworkshop,
              SpotCheckPerson:app.globalData.accountInfo.personID,
              answer:ans2,
              LoginOrg:app.globalData.accountInfo.org
            },
            method: 'POST',
            header: { 'Content-Type': 'application/x-www-form-urlencoded' },
            success: function (res) {
              // success
              console.log(res);//打印请求返回的结果
              if(res.data=='提交成功'){
                //提醒
                wx: wx.showToast({
                  title: '基础点检信息成功保存！',
                  icon: 'none',
                  duration: 2000
                })
              //提交成功后就回到列表界面，免得再次提交影响数据,redirectTo关闭当前页面，延时2秒执行，让别人看清楚提交成功的信息
              //把按钮都失效，防止在这2秒的时间内被人点击
              that.setData({
                isok:true
              })
              setTimeout(function(){
                wx.redirectTo({
                  url: '../../repair/repair-MyownSpot/repair-MyownSpot'
                })
              },2000)
                }else if(res.data=='提交失败'){
                //提醒
                wx: wx.showToast({
                  title: '基础点检信息保存失败！',
                  icon: 'none',
                  duration: 2000
                })
                }
            },
            fail: function (res) {
              // fail
            },
            complete: function (res) {
              // complete
            }
          })
        }else if(res.cancel){
          console.log('取消基础点检')
          that.setData({
            isok:false //解控
          })
        }
      }
    })
    }
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
},

//点检数据提交按钮
btnsend:function(){
var that=this
console.log('111:'+that.data.textareaBValue,that.data.textareaCValue)
if(that.data.oldpersonid!=app.globalData.accountInfo.personID){
  wx: wx.showToast({
    title: '无法提交别人的点检单据！',
    icon: 'none',
    duration: 2000
  })
}else if(that.data.todayiswrong==''||that.data.todayiswrong==null){
  wx: wx.showToast({
    title: '有无其他异常现象未选择！',
    icon: 'none',
    duration: 2000
  })
}else if(that.data.todaywhatswrong==''||that.data.todaywhatswrong==null){
  wx: wx.showToast({
    title: '异常状态未选择！',
    icon: 'none',
    duration: 2000
  })
}else if(that.data.beforework==''||that.data.beforework==null){
  wx: wx.showToast({
    title: '下班前15分钟...选项未选择！',
    icon: 'none',
    duration: 2000
  })
}else if(that.data.afterwork==''||that.data.afterwork==null){
  wx: wx.showToast({
    title: '下班后是否确认关闭设备电源未选择！',
    icon: 'none',
    duration: 2000
  })
}else if(that.data.textareaAValue==''||that.data.textareaAValue==null){
  wx: wx.showToast({
    title: '设备故障描述未填写！',
    icon: 'none',
    duration: 2000
  })
}else if(that.data.textareaBValue==''||that.data.textareaBValue==null){
  wx: wx.showToast({
    title: '异常停机时间未填写！',
    icon: 'none',
    duration: 2000
  })
}else if(that.data.textareaCValue==''||that.data.textareaCValue==null){
  wx: wx.showToast({
    title: '实际开机时间未填写！',
    icon: 'none',
    duration: 2000
  })
}else{//数据都填写完整了，可以提交了
  wx.showModal({
    title:'数据提交',
    content:'点检数据提交？',
    success:function(res){
      if(res.confirm){
        console.log('确认提交')
        wx.request({
          url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
          data: {
            api: 'SpotCheckTodaySend',
            MainID:that.data.MainID,
            beforework:that.data.beforework,
            afterwork:that.data.afterwork,
            todayiswrong:that.data.todayiswrong,
            todaywhatswrong:that.data.todaywhatswrong,
            textareaAValue:that.data.textareaAValue,
            textareaBValue:that.data.textareaBValue,
            textareaCValue:that.data.textareaCValue,
            personID:app.globalData.accountInfo.personID
          },
          method: 'POST',
          header: { 'Content-Type': 'application/x-www-form-urlencoded' },
          success: function (res) {
            // success
            console.log(res);//打印请求返回的结果
            if(res.data=='提交成功'){
              //提醒
              wx: wx.showToast({
                title: '点检数据提交成功！',
                icon: 'none',
                duration: 2000
              })
            //提交成功后就回到列表界面，免得再次提交影响数据,redirectTo关闭当前页面，延时2秒执行，让别人看清楚提交成功的信息
            //把按钮都失效，防止在这2秒的时间内被人点击
            that.setData({
              isok:true,
              isuse:true
            })
            setTimeout(function(){
              wx.redirectTo({
                url: '../../repair/repair-MyownSpot/repair-MyownSpot'
              })
            },2000)
              }else if(res.data=='提交失败'){
              //提醒
              wx: wx.showToast({
                title: '点检数据提交失败！',
                icon: 'none',
                duration: 2000
              })
              }
          },
          fail: function (res) {
            // fail
          },
          complete: function (res) {
            // complete
          }
        })
      }else if(res.cancel){
        console.log('取消提交')
      }
    }
  })
}
},
//未开机一键点检功能
btnonepunch:function(e){
  var that=this
  that.setData({
    isok:true //点击后把按钮禁用，避免重复点击
  })
  wx.request({
    url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
    data: {
      api: 'SpotCheckIsToday',
      EquipmentCode:that.data.equipmentinput,
      LoginOrg:app.globalData.accountInfo.org
    },
    method: 'POST',
    header: { 'Content-Type': 'application/x-www-form-urlencoded' },
    success: function (res) {
      // success
      if(res.data.length==0){//说明没有返回数据，说明今天已经点检了
        if(that.data.equipmentinput==''||that.data.equipmentinput==null){
        that.setData({
          isok:false //解控
        })
        wx: wx.showToast({
          title: '请输入设备编号，谢谢!',
          icon: 'none',
          duration: 2000
        })           
        }else if(that.data.equipmentname=='无此设备信息'){
        that.setData({
          isok:false //解控
        })
        wx: wx.showToast({
          title: '请检查设备编号是否正确!',
          icon: 'none',
          duration: 2000
        })
        }else{
        that.setData({
          isok:false //解控
        })
        wx: wx.showToast({
          title: '该设备已失效或今天已被点检，请勿重复点检!',
          icon: 'none',
          duration: 4000
        })
        }
      }else{
        console.log('正常运行!')
        wx.showModal({
          title:'一键点检',
          content:'未开机设备一键点检吗？',
          success:function(res){
            if(res.confirm){
          console.log('确认一键点检')
          that.setData({
            isok:false //解控
          })
          wx.request({
            url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
            data: {
              api: 'SpotCheckOnePunch',
              EquipmentCode:that.data.equipmentinput,
              LoginOrg:app.globalData.accountInfo.org,
              ReporterCode:app.globalData.accountInfo.personID,
            },
            method: 'POST',
            header: { 'Content-Type': 'application/x-www-form-urlencoded' },
              success: function (res) {
                //success
                if(res.data=='提交成功'){
                  //复原
                  that.setData({
                    placeholder:'自动带出设备名称',
                    placeholder2:'自动带出设备规格',
                    placeholder3:'自动带出车间',
                    equipmentinput:'',
                    equipmentname:'',
                    equipmentspecs:'',
                    equipmentworkshop:'',
                    postList:{}
                  });
                  //提醒
                  wx: wx.showToast({
                    title: '未开机设备一键点检成功!',
                    icon: 'none',
                    duration: 4000
                  })
                }else if(res.data=='提交失败'){
                  //提醒
                  wx: wx.showToast({
                    title: '一键点检失败！',
                    icon: 'none',
                    duration: 2000
                  })
                  }
              },
              fail: function (res) {
                // fail
              },
              complete: function (res) {
                // complete
              }
            })
        }else if(res.cancel){
          console.log('取消一键点检')
          that.setData({
            isok:false //解控
          })
        }
      }
      })
      }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
},
//扫码功能把设备编号带过来
btnscan:function(e){
  var that=this
  //微信自带扫码功能
  wx.scanCode({
    onlyFromCamera: true,
    success (res) {
      console.log(res)
      //赋值
      that.setData({ equipmentinput: res.result});
      //调用一下设备编号模糊焦点的方法
      that.equipmentblur();
    }
  });
},
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      MainID:options.MainID,
      equipmentinput:options.equipmentinput,
      assetNumber:options.assetNumber,
    })
    //onload这边默认设备编码是一定有的，除非被删除，所以只要判断这两种情况
    if(this.data.assetNumber!=''&&this.data.assetNumber!=null){
      //这种情况需要判断设备今日是否点检，因为是生成新单子
      console.log('来源未点检设备预警/我的点检表历史单据跳转生成')//相当于设备编号失去焦点的事件，只不过把获取过来的设备编号直接赋值了
      var that = this
    //获取设备基本信息
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'SpotCheckInput',
        equipmentinput:that.data.assetNumber,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          equipmentinput:that.data.assetNumber,
          equipmentname:res.data[0][0],
          equipmentspecs:res.data[0][1],
          equipmentworkshop:res.data[0][2]
        })
        wx.request({
          url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
          data: {
            api: 'SpotCheckIsToday',
            EquipmentCode:that.data.assetNumber,
            LoginOrg:app.globalData.accountInfo.org
          },
          method: 'POST',
          header: { 'Content-Type': 'application/x-www-form-urlencoded' },
          success: function (res) {
            // success
            if(res.data.length==0){//说明没有返回数据，说明今天已经点检了
              console.log('该设备今天已经被点检了!')
              if(that.data.assetNumber==''||that.data.assetNumber==null){
              wx: wx.showToast({
                title: '请输入设备编号，谢谢!',
                icon: 'none',
                duration: 2000
              })           
              }else if(that.data.equipmentname=='无此设备信息'){
              wx: wx.showToast({
                title: '请检查设备编号是否正确!',
                icon: 'none',
                duration: 2000
              })
              }else{
              wx: wx.showToast({
                title: '该设备已失效或今天已被点检，请勿重复点检!',
                icon: 'none',
                duration: 2000
              })
              }
            }else{
        console.log('今天未被点检，正常运行!')
      //每次获取前先把基础点检之前加载的东西清空，免得影响
      that.setData({
        postList:{}
      })
      //获取基础点检表的信息
      wx.request({
        url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
        data: {
          api: 'SpotCheckInputList',
          equipmentinput:that.data.assetNumber,
          LoginOrg:app.globalData.accountInfo.org
        },
        method: 'POST',
        header: { 'Content-Type': 'application/x-www-form-urlencoded' },
        success: function (res) {
          // success
          console.log(res);//打印请求返回的结果
          if(res.data.length==0){
            wx: wx.showToast({
              title: '该设备没有基础点检表！',
              icon: 'none',
              duration: 2000
            })
          }else{
            wx: wx.showToast({
              title: '数据加载成功！',
              icon: 'none',
              duration: 2000
            })
            that.setData({
              postList:res.data,
              datacount:res.data[0][3]
              })
          that.data.ans=new Array(parseInt(that.data.datacount))//获取到的问题个数新建一个数组
          }
        },
        fail: function (res) {
          // fail
        },
        complete: function (res) {
          // complete
        }
      })
            }
          },
          fail: function (res) {
            // fail
          },
          complete: function (res) {
            // complete
          }
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    }else if(this.data.MainID==''||this.data.MainID==null){
    console.log('设备正常点检上报')//不用判断设备今天是否已点检
    }else{
    console.log('来源我的点检表,正常进入,准备点检完成')//有mainid的进入，不用判断设备今天是否已点检
    var that = this
    //获取设备基本信息,第一部分
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'SpotCheckInput',
        equipmentinput:that.data.equipmentinput,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          equipmentname:res.data[0][0],
          equipmentspecs:res.data[0][1],
          equipmentworkshop:res.data[0][2]
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //获取基础点检信息，第二部分
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'SpotCheckBasic',
        MainID:that.data.MainID,
        equipmentinput:that.data.equipmentinput,
        personID:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          postList2:res.data
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //获取当日设备运行状况，第三部分
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'SpotCheckToday',
        MainID:that.data.MainID,
        equipmentinput:that.data.equipmentinput,
        personID:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          status:res.data[0][0],
          todayiswrong:res.data[0][1],
          todaywhatswrong:res.data[0][2],
          textareaAValue:res.data[0][3],
          textareaBValue:res.data[0][4],
          textareaCValue:res.data[0][5],
          beforework:res.data[0][6],
          afterwork:res.data[0][7],
          oldpersonid:res.data[0][8]
        })
    //根据得到的状态掌控本界面控件的可用性
    console.log('textareaAValue:'+that.data.textareaAValue,' textareaBValue:'+that.data.textareaBValue,' textareaCValue:'+that.data.textareaCValue)
    if(that.data.textareaBValue==''||that.data.textareaBValue==null){
      that.setData({
        textareaBValue:'0',//把时间控件赋值默认值0
      })
    }
    if(that.data.textareaCValue==''||that.data.textareaCValue==null){
      that.setData({
        textareaCValue:'0',//把时间控件赋值默认值0
      })
    }
    if(that.data.status==0){
      that.setData({
        ishide:false,
        isok:true //设备编号，基础点检表，基础点检按钮都禁止
      })
    }else if(that.data.status==1){
      that.setData({
        ishide:false,
        isok:true,
        isuse:true
      })
    }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  }
})