<!--pages/repair/repair-arrange/repair-arrange-detail/repair-arrange-detail.wxml-->
    <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">维修工单信息</text>
    </view>
  </view>

    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">报修人姓名:</view>
      <text selectable="true" class="sg-content">{{t1}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">报修人电话:</view>
      <text selectable="true" class="sg-content">{{t2}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">报修时间:</view>
      <text selectable="true" class="sg-content">{{t3}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">设备名称:</view>
      <text selectable="true" class="sg-content">{{t4}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">事业部:</view>
      <text selectable="true" class="sg-content">{{t5}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">车间:</view>
      <text selectable="true" class="sg-content">{{t6}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">班组:</view>
      <text selectable="true" class="sg-content">{{t7}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">故障描述:</view>
      <text selectable="true" class="sg-content">{{t8}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">计划维修时间:</view>
      <text selectable="true" class="sg-content">{{t9}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">需求工种:</view>
      <text selectable="true" class="sg-content">{{t10}}</text>
    </view>

    <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">维修安排</text>
    </view>
  </view>

  	<view class="cu-form-group">
		<view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">安排维修工:</view>
		<picker bindchange="PickerChange" value="{{index}}" range="{{picker[0]}}" range-key="{{[1]}}">
			<view class="picker" style="text-align:left;color:gray">
				{{index?picker[0][index][1]:'请选择维修工'}}
			</view>
		</picker>
	</view>

		<view class="cu-form-group">
		<view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">计划完成时间:</view>
		<!--去除#shadow-root的padding-right不然箭头会空开-->
  <date-time-picker value="{{datetime}}" bind:change="handleChange" style="padding-right:0rpx">
	<view class="date-time-picker" style="text-align:left;color:gray">
	{{datetime}}
	</view>
	</date-time-picker>
  </view>

  <view class="padding flex" style="flex-direction:row">
  <button class="send-btn bg-shimge lg" style="font-size:30rpx" bindtap="btnsend" disabled="{{issend}}">提　交</button>
  <button class="send-btn bg-shimge lg" style="font-size:30rpx" bindtap="btncancel" disabled="{{iscancel}}">放　弃</button>
</view>