// pages/repair/repair-arrange/repair-arrange-detail/repair-arrange-detail.js
const app = getApp();
var time = require('../../../../utils/util.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    MainID:'',
    index:null,
    picker: [[13,'工种A']],
    datetime:time.formatTime2(new Date()),
    t1:'',
    t2:'',
    t3:'',
    t4:'',
    t5:'',
    t6:'',
    t7:'',
    t8:'',
    t9:'',
    t10:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      MainID:options.MainID,
      datetime:time.formatTime2(new Date())
    })
    //获取维修工单信息
    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'Equipmentarrange',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          t1:res.data[0][0],
          t2:res.data[0][1],
          t3:res.data[0][2],
          t4:res.data[0][3],
          t5:res.data[0][4],
          t6:res.data[0][5],
          t7:res.data[0][6],
          t8:res.data[0][7],
          t9:res.data[0][8],
          t10:res.data[0][9]
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //加载安排维修工的信息
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'BasicRepairerName',
        RepairerCode:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          picker:res.data
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    //加载安排维修工的信息
    var that=this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'BasicRepairerName',
        RepairerCode:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          picker:res.data
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  //datetimepicker
  handleChange(e) {
    console.log(e)
    this.setData({
      datetime: e.detail.dateString
    })
  },
  //安排维修工
  PickerChange(e) {
    console.log(e);
    this.setData({
      index: e.detail.value
    })
  },
  //放弃按钮--点击按钮清空信息
  btncancel:function(e){
    var that=this
    wx.showModal({
    title:'放弃',
    content:'确认放弃吗？',
    success:function(res){
      if(res.confirm){
        console.log('确认放弃');
        that.setData({
          index: null,
          datetime:time.formatTime2(new Date()),
        })
      }else if(res.cancel){
        console.log('取消放弃')
      }
    }
  })},
    //提交按钮
    btnsend:function(e){
      var that=this
      wx.showModal({
      title:'提交',
      content:'确认提交吗？',
      success:function(res){
        if(res.confirm){
          console.log('确认提交');
          if(that.data.datetime=='请选择计划完成日期'||that.data.index==null){
            wx: wx.showToast({
              title: '维修安排未填写完整！',
              icon: 'none',
              duration: 2000
            })
          }
          else{//更新数据到数据库中去
            wx.request({
              url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
              data: {
                api: 'EquipmentarrangeSend',
                MainID:that.data.MainID,
                RepairerNo:that.data.picker[0][that.data.index][0],
                RepairerName:that.data.picker[0][that.data.index][1],
                PlanCompleteTime:that.data.datetime
              },
              method: 'POST',
              header: { 'Content-Type': 'application/x-www-form-urlencoded' },
              success: function (res) {
                // success
                console.log(res);//打印请求返回的结果
                if(res.data=='提交成功'){
                //复原
                that.setData({
                  index: null,
                  datetime:time.formatTime2(new Date())
                });
                //提醒
                wx: wx.showToast({
                  title: '提交成功！',
                  icon: 'none',
                  duration: 2000
                })
                //提交成功后就回到列表界面，免得再次提交影响数据,redirectTo关闭当前页面，延时2秒执行，让别人看清楚提交成功的信息
                //把按钮都失效，防止在这2秒的时间内被人点击
                that.setData({
                  issend:true,
                  iscancel:true
                })
                setTimeout(function(){
                wx.redirectTo({
                  url: '../../../repair/repair-arrange/repair-arrange'
                })
                },2000)
                }else if(res.data=='提交失败'){
                //提醒
                wx: wx.showToast({
                  title: '提交失败！',
                  icon: 'none',
                  duration: 2000
                })
                }
              },
              fail: function (res) {
                // fail
              },
              complete: function (res) {
                // complete
              }
            })
          }
        }else if(res.cancel){
          console.log('取消提交')
        }
      }
    })
    }
})