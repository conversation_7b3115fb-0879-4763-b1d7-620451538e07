// pages/repair/repair-EquipmentResume/repair-EquipmentResume-detail/repair-EquipmentResume-detail.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    MainID:'',
    t1:'',
    t2:'',
    t3:'',
    t4:'',
    t5:'',
    t6:'',
    t7:'',
    t8:'',
    t9:'',
    t10:'',
    t11:'',
    t12:'',
    t13:'',
    t14:'',
    t15:'',
    t16:'',
    t17:'',
    t18:'',
    isHideinfo:true,
    sub:'+',//扩展收缩按键
    change:'+',
    maintain:'+',
    parts:'+',
    repair:'+',
    postList:{},//附属设备
    postList2:{},//设备变更
    postList3:{},//保养记录
    postList4:{},//配件记录
    postList5:{},//维修记录
    Subhide:true,
    Changehide:true,
    Maintainhide:true,
    Partshide:true,
    Repairhide:true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      MainID:options.MainID
    })
    //获取维修工单信息
    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'EquipmentResume',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        //普通数据绑定
        that.setData({
          t1:res.data[0][0],
          t2:res.data[0][1],
          t3:res.data[0][2],
          t4:res.data[0][3],
          t5:res.data[0][4],
          t6:res.data[0][5],
          t7:res.data[0][6],
          t8:res.data[0][7],
          t9:res.data[0][8],
          t10:res.data[0][9],
          t11:res.data[0][10],
          t12:res.data[0][11],
          t13:res.data[0][12],
          t14:res.data[0][13],
          t15:res.data[0][14],
          t16:res.data[0][15],
          t17:res.data[0][16],
          t18:res.data[0][17]
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //附属设备
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'EquipmentResumeSub',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          that.setData({
            sub:'无',
          })
        }else{
        that.setData({
          postList:res.data
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //设备变更
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'EquipmentResumeChange',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          that.setData({
            change:'无',
          })
        }else{
        that.setData({
          postList2:res.data
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //保养记录
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'EquipmentResumeMaintain',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          that.setData({
            maintain:'无',
          })
        }else{
        that.setData({
          postList3:res.data
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //配件记录
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'EquipmentResumeParts',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          that.setData({
            parts:'无',
          })
        }else{
        that.setData({
          postList4:res.data
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //维修记录
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'EquipmentResumeRepair',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          that.setData({
            repair:'无',
          })
        }else{
        that.setData({
          postList5:res.data
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //是否管理员
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'EquipmentResumeIsGM',
        ReporterCode:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log('isGM:'+res.data);//打印请求返回的结果
        if(res.data.length==0){
          that.setData({
            isHideinfo:true
          })
        }else{
          if(res.data[0][0]>=1){
            that.setData({
              isHideinfo:false
            })
          }else{
            that.setData({
              isHideinfo:true
            })
          }
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  //附属设备点击
  Subtap:function(e){
    var that=this;
    if(that.data.sub=='+'){
      that.setData({
        sub:'-',
        Subhide:false
      })
    }else if(that.data.sub=='-'){
      that.setData({
        sub:'+',
        Subhide:true
      })
    }
  },
  //设备变更点击
  Changetap:function(e){
    var that=this;
    if(that.data.change=='+'){
      that.setData({
        change:'-',
        Changehide:false
      })
    }else if(that.data.change=='-'){
      that.setData({
        change:'+',
        Changehide:true
      })
    }
  },
  //保养记录点击
  Maintaintap:function(e){
    var that=this;
    if(that.data.maintain=='+'){
      that.setData({
        maintain:'-',
        Maintainhide:false
      })
    }else if(that.data.maintain=='-'){
      that.setData({
        maintain:'+',
        Maintainhide:true
      })
    }
  },
  //配件记录点击
  Partstap:function(e){
    var that=this;
    if(that.data.parts=='+'){
      that.setData({
        parts:'-',
        Partshide:false
      })
    }else if(that.data.parts=='-'){
      that.setData({
        parts:'+',
        Partshide:true
      })
    }
  },
  //维修记录点击
  Repairtap:function(e){
    var that=this;
    if(that.data.repair=='+'){
      that.setData({
        repair:'-',
        Repairhide:false
      })
    }else if(that.data.repair=='-'){
      that.setData({
        repair:'+',
        Repairhide:true
      })
    }
  },
})