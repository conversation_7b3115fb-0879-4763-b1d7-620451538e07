<!--pages/repair/repair-EquipmentResume/repair-EquipmentResume-detail/repair-EquipmentResume-detail.wxml-->
<import src="../repair-EquipmentResume-item/repair-EquipmentResume-item.wxml" />
<view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">设备履历基本信息</text>
    </view>
  </view>
  <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">使用组织:</view>
      <text selectable="true" class="sg-content">{{t1}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">设备编号:</view>
      <text selectable="true" class="sg-content">{{t2}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">设备名称:</view>
      <text selectable="true" class="sg-content">{{t3}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">设备型号:</view>
      <text selectable="true" class="sg-content">{{t4}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">出厂日期:</view>
      <text selectable="true" class="sg-content">{{t5}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">进厂日期:</view>
      <text selectable="true" class="sg-content">{{t6}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">使用车间:</view>
      <text selectable="true" class="sg-content">{{t7}}</text>
    </view>
    <view class="cu-form-group" hidden="{{isHideinfo}}">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">设备原值(千元):</view>
      <text selectable="true" class="sg-content">{{t8}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">产地:</view>
      <text selectable="true" class="sg-content">{{t9}}</text>
    </view>
    <view class="cu-form-group" hidden="{{isHideinfo}}">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">生产厂商:</view>
      <text selectable="true" class="sg-content">{{t10}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">出厂编号:</view>
      <text selectable="true" class="sg-content">{{t11}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">类别:</view>
      <text selectable="true" class="sg-content">{{t12}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">完好状况:</view>
      <text selectable="true" class="sg-content">{{t13}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">使用年限:</view>
      <text selectable="true" class="sg-content">{{t14}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">线别:</view>
      <text selectable="true" class="sg-content">{{t15}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">装机容量(只):</view>
      <text selectable="true" class="sg-content">{{t16}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">装机容量(kw):</view>
      <text selectable="true" class="sg-content">{{t17}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">备注:</view>
      <text selectable="true" class="sg-content">{{t18}}</text>
    </view>

    <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">设备履历附属设备</text>
    </view>
    <view class="action">
    <button bindtap="Subtap" class="cu-btn bg-shimge shadow-blur " style="height:45rpx" type="">{{sub}}</button>
  </view>
  </view>
  <view hidden="{{Subhide}}">
  <block wx:for="{{postList}}" wx:for-item="item" wx:for-index="idx" >
  <template is="Subitem" data="{{item}}" />
  </block>
  </view>

  <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">设备履历设备变更</text>
    </view>
    <view class="action">
    <button bindtap="Changetap" class="cu-btn bg-shimge shadow-blur " style="height:45rpx" type="">{{change}}</button>
  </view>
  </view>
  <view hidden="{{Changehide}}">
  <block wx:for="{{postList2}}" wx:for-item="item" wx:for-index="idx" >
  <template is="Changeitem" data="{{item}}" />
  </block>
  </view>

  <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">设备履历保养记录</text>
    </view>
    <view class="action">
    <button bindtap="Maintaintap" class="cu-btn bg-shimge shadow-blur " style="height:45rpx" type="">{{maintain}}</button>
  </view>
  </view>
  <view hidden="{{Maintainhide}}">
  <block wx:for="{{postList3}}" wx:for-item="item" wx:for-index="idx" >
  <template is="Maintainitem" data="{{item}}" />
  </block>
  </view>

  <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">设备履历配件记录</text>
    </view>
    <view class="action">
    <button bindtap="Partstap" class="cu-btn bg-shimge shadow-blur " style="height:45rpx" type="">{{parts}}</button>
  </view>
  </view>
  <view hidden="{{Partshide}}">
  <block wx:for="{{postList4}}" wx:for-item="item" wx:for-index="idx" >
  <template is="Partsitem" data="{{item}}" />
  </block>
  </view>

  <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">设备履历维修记录</text>
    </view>
    <view class="action">
    <button bindtap="Repairtap" class="cu-btn bg-shimge shadow-blur " style="height:45rpx" type="">{{repair}}</button>
  </view>
  </view>
  <view hidden="{{Repairhide}}">
  <block wx:for="{{postList5}}" wx:for-item="item" wx:for-index="idx" >
  <template is="Repairitem" data="{{item}}" />
  </block>
  </view>
