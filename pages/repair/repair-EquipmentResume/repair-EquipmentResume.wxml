<!--pages/repair/repair-EquipmentResume/repair-EquipmentResume.wxml-->
<import src="repair-EquipmentResume-item/repair-EquipmentResume-item.wxml" />
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search bg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索设备编号、名称、车间" confirm-type="search" bindinput="bindKeyInput" bindconfirm="searchData" value="{{inputvalue}}"></input>
    </view>
    <view class="action">
    <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
    <view class="action">
    <button bindtap="btnscan" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">扫码</button>
  </view>
  </view>

<view class="title_wrap">
<view class="titleA">设备履历表</view>
</view>

  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">设备编号</view>
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">设备名称</view>
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">使用车间</view>
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">出厂日期</view>
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">进厂日期</view>
  </view>
</view>

<view class="bg-white"> 
    <block wx:for="{{postList}}" wx:for-item="item" wx:for-index="idx">
    <view bindtap="onTapToDetail" data-index="{{item}}" wx:if="{{idx%2==0}}">
      <template is="equresitem" data="{{item}}" />
    </view>
    <view bindtap="onTapToDetail" data-index="{{item}}" class="block" wx:else>
      <template is="equresitem" data="{{item}}" />
    </view>
  </block>

    <view class="cu-load {{(loadState==1?'loading':(loadState==2?'over':''))}}"></view>
    </view>

