<!--pages/repair/repair-Myown/repair-Myown.wxml-->
<import src="repair-Myown-item/repair-Myown-item.wxml" />
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search bg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索车间、维修人、设备、状态" confirm-type="search" bindinput="bindKeyInput" bindconfirm="searchData" value="{{inputvalue}}"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>

<view class="title_wrap">
<view class="titleA">我的报工单</view>
</view>

  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">维修人</view>
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">设备车间</view>
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">设备名称</view>
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">报修日期</view>
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">状态</view>
  </view>
</view>

<view class="bg-white"> 
    <block wx:for="{{postList}}" wx:for-item="item" wx:for-index="idx">
    <view bindtap="onTapToDetail" data-index="{{item}}" wx:if="{{idx%2==0}}">
      <template is="myownitem" data="{{item}}" />
    </view>
    <view bindtap="onTapToDetail" data-index="{{item}}" class="block" wx:else>
      <template is="myownitem" data="{{item}}" />
    </view>
  </block>

    <view class="cu-load {{(loadState==1?'loading':(loadState==2?'over':''))}}"></view>
    </view>
