// pages/repair/repair-Myown/repair-Myown-detail/repair-Myown-detail.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    MainID:'',
    t1:'',
    t2:'',
    t3:'',
    t4:'',
    t5:'',
    t6:'',
    t7:'',
    t8:'',
    t9:'',
    t10:'',
    t11:'',
    t12:'',
    t13:'',
    t14:'',
    t15:'',
    t16:'',
    t17:'',
    t18:'',
    t19:'',
    t20:'',
    t21:'',
    t22:'',
    t23:'',
    t24:'',
    t25:'',
    status:0,//获取这个维修单当前的状态是什么，用来退回按钮的控制
    isback:false,
    isdelete:false,
    ishide:false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      MainID:options.MainID
    })
    //获取信息
    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'Myowndetails',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          t1:res.data[0][0],
          t2:res.data[0][1],
          t3:res.data[0][2],
          t4:res.data[0][3],
          t5:res.data[0][4],
          t6:res.data[0][5],
          t7:res.data[0][6],
          t8:res.data[0][7],
          t9:res.data[0][8],
          t10:res.data[0][9],
          t11:res.data[0][10],
          t12:res.data[0][11],
          t13:res.data[0][12],
          t14:res.data[0][13],
          t15:res.data[0][14],
          t16:res.data[0][15],
          t17:res.data[0][16],
          t18:res.data[0][17],
          t19:res.data[0][18],
          t20:res.data[0][19],
          t21:res.data[0][20],
          t22:res.data[0][21],
          t23:res.data[0][22],
          status:res.data[0][23],
          t24:res.data[0][24],
          t25:res.data[0][25]
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //判断是否有退回和删除两个按钮的权限
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'MyowndetailsIshide',
        ReporterCode:app.globalData.accountInfo.personID,
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        if(res.data.length==0)//没有这个人员的管理员信息
        {
          console.log('该用户没有按钮权限!')
          that.setData({
            ishide:true
          })
        }else{
          console.log('该用户为超级管理员!')
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  //退回按钮
  btnback:function(e){
    var that=this
    if(that.data.status==0){
      wx: wx.showToast({
        title: '此报工单还未安排维修，无需退回!',
        icon: 'none',
        duration: 2000
      })
    }else if(that.data.status>=4){
      wx: wx.showToast({
        title: '此报工单已完成维修或已删除，不能退回!',
        icon: 'none',
        duration: 2000
      })
    }else if(that.data.status==1||that.data.status==2||that.data.status==3)//状态是已安排，已接单，维修中 可以退回到待安排的状态
    {
      wx.showModal({
        title:'退回',
        content:'确定退回吗？',
        success:function(res){
          if(res.confirm){
            console.log('确认退回');
            wx.request({
              url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
              data: {
                api: 'MyowndetailsBack',
                MainID:that.data.MainID
              },
              method: 'POST',
              header: { 'Content-Type': 'application/x-www-form-urlencoded' },
              success: function (res) {
                // success
                console.log(res);//打印请求返回的结果
                if(res.data=='提交成功'){
                //提醒
                wx: wx.showToast({
                  title: '退回成功，请重新安排任务!',
                  icon: 'none',
                  duration: 2000
                })
                //提交成功后就回到列表界面，免得再次提交影响数据,redirectTo关闭当前页面，延时2秒执行，让别人看清楚提交成功的信息
                //把按钮都失效，防止在这2秒的时间内被人点击
                that.setData({
                  isback:true,
                  isdelete:true
                })
                setTimeout(function(){
                wx.redirectTo({
                  url: '../../../repair/repair-Myown/repair-Myown'
                })
                },2000)
                }else if(res.data=='提交失败'){
                //提醒
                wx: wx.showToast({
                  title: '退回失败!',
                  icon: 'none',
                  duration: 2000
                })
                }
              },
              fail: function (res) {
                // fail
              },
              complete: function (res) {
                // complete
              }
            })
          }else if(res.cancel){
            console.log('取消退回')
          }
        }
      })
    }
  },
  //删除按钮
  btndelete:function(e){
    var that=this
    if(that.data.status==6){
      wx: wx.showToast({
        title: '此报工单已经删除，请勿重复操作!',
        icon: 'none',
        duration: 2000
      })     
    }else{
    wx.showModal({
      title:'删除',
      content:'确定删除吗？误删请联系管理员恢复数据!',
      success:function(res){
        if(res.confirm){
          console.log('确认删除');
          wx.request({
            url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
            data: {
              api: 'MyowndetailsDelete',
              MainID:that.data.MainID
            },
            method: 'POST',
            header: { 'Content-Type': 'application/x-www-form-urlencoded' },
            success: function (res) {
              // success
              console.log(res);//打印请求返回的结果
              if(res.data=='提交成功'){
              //提醒
              wx: wx.showToast({
                title: '报工单删除成功!误删请联系管理员恢复数据!',
                icon: 'none',
                duration: 2000
              })
              //提交成功后就回到列表界面，免得再次提交影响数据,redirectTo关闭当前页面，延时2秒执行，让别人看清楚提交成功的信息
              //把按钮都失效，防止在这2秒的时间内被人点击
              that.setData({
                isback:true,
                isdelete:true
              })
              setTimeout(function(){
              wx.redirectTo({
                url: '../../../repair/repair-Myown/repair-Myown'
              })
              },2000)
              }else if(res.data=='提交失败'){
              //提醒
              wx: wx.showToast({
                title: '删除失败!',
                icon: 'none',
                duration: 2000
              })
              }
            },
            fail: function (res) {
              // fail
            },
            complete: function (res) {
              // complete
            }
          }) 
        }else if(res.cancel){
          console.log('取消删除')
        }
      }
    })
  }
  }
})