// pages/repair/repair-solve/repair-solve-detail/repair-solve-detail.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    MainID:'',
    index:null,
    pickerstring:'请选择故障类别',//选择框的默认文字，如果前面选了这边可以直接读取文字
    picker: [[13,'工种A']],
    t1:'',
    t2:'',
    t3:'',
    t4:'',
    t5:'',
    t6:'',
    t7:'',
    t8:'',
    t9:'',
    t10:'',
    t11:'',
    t12:'',
    t13:'',
    modalName:null,
    textareaAValue:'',//输入框 故障原因分析
    textareaBValue:'',//输入框 维修处理情况
    textareaCValue:'',//输入框 更改配件信息
    textareaDValue:'0',//配件购置时间
    status:'',//状态，用来控制明细界面的按钮开关
    isReceive:false,
    isStart:false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      MainID:options.MainID
    })
    //获取维修工单信息
    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'EquipmentSolve',
        MainID:that.data.MainID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        //判断故障类别有无保存
        if(res.data[0][13]==""||res.data[0][13]==''||res.data[0][13]==null){
          console.log('故障类别还未选择保存')
        }else{
          that.setData({
            pickerstring:res.data[0][13]
          })
        }
        //普通数据绑定
        that.setData({
          status:res.data[0][0],
          t1:res.data[0][1],
          t2:res.data[0][2],
          t3:res.data[0][3],
          t4:res.data[0][4],
          t5:res.data[0][5],
          t6:res.data[0][6],
          t7:res.data[0][7],
          t8:res.data[0][8],
          t9:res.data[0][9],
          t10:res.data[0][10],
          t11:res.data[0][11],
          t12:res.data[0][12],
          textareaAValue:res.data[0][14],
          textareaBValue:res.data[0][15],
          textareaCValue:res.data[0][16],
          textareaDValue:res.data[0][17],
          t13:res.data[0][18]
        })
        //拿状态控制按钮的开关
        if(that.data.status=='2'){
          that.setData({
            isReceive:true
          })
        }
        else if(that.data.status=='3'){
          that.setData({
            isReceive:true,
            isStart:true
          })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //加载故障类别的信息
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'BasicEquipmentFaultType',
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          picker:res.data
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    //加载故障类别的信息
    var that=this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'BasicEquipmentFaultType',
        LoginOrg:app.globalData.accountInfo.org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          picker:res.data
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  //故障类别
  PickerChange(e) {
    console.log(e);
    this.setData({
      index: e.detail.value
    })
  },
  //因为这个明细表是支持随时进来修改数据的，所以这块输入框都不做控制，自由编辑
  //接收按钮，不管数据有没有填上，最开始的步骤---点击接收更改状态为2(已接单)
  btnReceive:function(e){
    var that=this
    wx.showModal({
    title:'接收',
    content:'确认接收吗？',
    success:function(res){
      if(res.confirm){
        console.log('确认接收');
        wx.request({
          url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
          data: {
            api: 'EquipmentSolveReceive',
            MainID:that.data.MainID,
          },
          method: 'POST',
          header: { 'Content-Type': 'application/x-www-form-urlencoded' },
          success: function (res) {
            // success
            console.log(res);//打印请求返回的结果
            if(res.data=='提交成功'){
              //提醒
              wx: wx.showToast({
                title: '接收成功！',
                icon: 'none',
                duration: 2000
              })
              //接收成功就把接收按钮停用，免得影响记录的 接收时间
              that.setData({
                isReceive:true,
                status:'2'
              })
              }else if(res.data=='提交失败'){
              //提醒
              wx: wx.showToast({
                title: '接收失败！',
                icon: 'none',
                duration: 2000
              })
              }
          },
          fail: function (res) {
            // fail
          },
          complete: function (res) {
            // complete
          }
        })
      }else if(res.cancel){
        console.log('取消接收')
      }
    }
  })},
  //开始维修按钮，也是不管数据有没有填上都可以点击，第二个步骤，点击变更状态为3(维修中)
  //大前提是已接收，这边要判断一下
  btnStart:function(e){
    var that=this
    //判断该记录是否是已接收
    if(that.data.status=='2'){
      console.log('确认是否开始维修')
      wx.showModal({
        title:'开始维修',
        content:'确认开始维修吗？',
        success:function(res){
          if(res.confirm){
            console.log('确认开始维修');
            wx.request({
              url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
              data: {
                api: 'EquipmentSolveStart',
                MainID:that.data.MainID,
              },
              method: 'POST',
              header: { 'Content-Type': 'application/x-www-form-urlencoded' },
              success: function (res) {
                // success
                console.log(res);//打印请求返回的结果
                if(res.data=='提交成功'){
                  //提醒
                  wx: wx.showToast({
                    title: '开始维修成功！',
                    icon: 'none',
                    duration: 2000
                  })
                  //接收成功就把开始维修按钮停用，免得影响记录的 开始维修时间
                  that.setData({
                    isStart:true,
                    status:'3'
                  })
                  }else if(res.data=='提交失败'){
                  //提醒
                  wx: wx.showToast({
                    title: '开始维修失败！',
                    icon: 'none',
                    duration: 2000
                  })
                  }
              },
              fail: function (res) {
                // fail
              },
              complete: function (res) {
                // complete
              }
            })
          }else if(res.cancel){
            console.log('取消开始维修')
          }
        }
      })
      }else{
      //提醒
      wx: wx.showToast({
      title: '请先接收本单！',
      icon: 'none',
      duration: 2000
      })
      }
  },
  //保存按钮，大前提是已经开始维修，即状态为3
  btnSave:function(e){
    var that=this
    var EquipmentFaultType;
    //故障类别判断
    if(that.data.index==null){
      if(that.data.pickerstring!='请选择故障类别'){
      that.setData({
        EquipmentFaultType:that.data.pickerstring
      })
    }
    else{
      that.setData({
        EquipmentFaultType:''
      })
    }
    }else{
      that.setData({
        EquipmentFaultType:that.data.picker[0][that.data.index][1]
      })
    }
    //判断该记录是否是已接收
    if(that.data.status=='3'){
      console.log('确认是否保存')
      wx.showModal({
        title:'保存',
        content:'确认保存吗？',
        success:function(res){
          if(res.confirm){
            console.log('确认保存');
            wx.request({
              url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
              data: {
                api: 'EquipmentSolveSave',
                MainID:that.data.MainID,
                EquipmentFaultReason:that.data.textareaAValue,
                RepairTreatment:that.data.textareaBValue,
                PartsChange:that.data.textareaCValue,
                EquipmentFaultType:that.data.EquipmentFaultType,
                PartsChangeTime:that.data.textareaDValue
              },
              method: 'POST',
              header: { 'Content-Type': 'application/x-www-form-urlencoded' },
              success: function (res) {
                // success
                console.log(res);//打印请求返回的结果
                if(res.data=='提交成功'){
                  //提醒
                  wx: wx.showToast({
                    title: '保存成功！',
                    icon: 'none',
                    duration: 2000
                  })
                  }else if(res.data=='提交失败'){
                  //提醒
                  wx: wx.showToast({
                    title: '保存失败！',
                    icon: 'none',
                    duration: 2000
                  })
                  }
              },
              fail: function (res) {
                // fail
              },
              complete: function (res) {
                // complete
              }
            })
          }else if(res.cancel){
            console.log('取消保存')
          }
        }
      })
      }else{
      //提醒
      wx: wx.showToast({
      title: '请先开始维修！',
      icon: 'none',
      duration: 2000
      })
      }
  },
  //故障原因分析
  textareaAInput(e) {
    this.setData({
      textareaAValue: e.detail.value
    })
  },
  //维修处理情况
  textareaBInput(e) {
    this.setData({
      textareaBValue: e.detail.value
    })
  },
  //更改配件信息
  textareaCInput(e) {
    this.setData({
      textareaCValue: e.detail.value
    })
  },
  //配件购置时间
  textareaDInput(e) {
    this.setData({
      textareaDValue: e.detail
    })
  },
  //维修完毕按钮，把状态更改成4(待评价)
  //大前提是已经开始维修了，即状态为3,本单上的4个信息都必填，为空报错
  btnComplete:function(e){
    var that=this
    var EquipmentFaultType;
    if(that.data.index==null&&that.data.pickerstring=='请选择故障类别'){
      //提醒
      wx: wx.showToast({
        title: '故障类别未选择！',
        icon: 'none',
        duration: 2000
        })
    }else if(that.data.textareaDValue==''||that.data.textareaDValue==null||that.data.textareaDValue==0){
      wx: wx.showToast({
        title: '实际维修时间为空或者为零！',
        icon: 'none',
        duration: 2000
        })
    }else if(that.data.textareaAValue==''|| that.data.textareaAValue==null){
      //提醒
      wx: wx.showToast({
        title: '故障原因分析为空！',
        icon: 'none',
        duration: 2000
        })
    }else if(that.data.textareaBValue==''||that.data.textareaBValue==null){
      //提醒
      wx: wx.showToast({
        title: '维修处理情况为空！',
        icon: 'none',
        duration: 2000
        })
    }else if(that.data.textareaCValue==''||that.data.textareaCValue==null){
      //提醒
      wx: wx.showToast({
        title: '更换配件信息为空！',
        icon: 'none',
        duration: 2000
        })
    }else if(that.data.status!='3'){
      //提醒
      wx: wx.showToast({
        title: '请先开始维修！',
        icon: 'none',
        duration: 2000
        })
    }else{
    //因为上面判断的前提条件这些是看输入框为不为空，存在加载不为空，但是人员修改了未保存，然后直接点的维修完毕的情况，所以维修完毕这边要自己做一下内部的保存，不提示 
    //故障类别判断
    if(that.data.index==null){
      if(that.data.pickerstring!='请选择故障类别'){
      that.setData({
        EquipmentFaultType:that.data.pickerstring
      })
    }
    else{
      that.setData({
        EquipmentFaultType:''
      })
    }
    }else{
      that.setData({
        EquipmentFaultType:that.data.picker[0][that.data.index][1]
      })
    }
    //内部自己的保存，不显示交互
    wx.request({
    url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
    data: {
    api: 'EquipmentSolveSave',
    MainID:that.data.MainID,
    EquipmentFaultReason:that.data.textareaAValue,
    RepairTreatment:that.data.textareaBValue,
    PartsChange:that.data.textareaCValue,
    EquipmentFaultType:that.data.EquipmentFaultType,
    PartsChangeTime:that.data.textareaDValue
          },
    method: 'POST',
    header: { 'Content-Type': 'application/x-www-form-urlencoded' },
    success: function (res) {
    // success
    console.log('维修完毕内部保存执行结果：'+res.data);//打印请求返回的结果
              },
    fail: function (res) {
    // fail
              },
    complete: function (res) {
    // complete
              }
    })

    //这边是正常的维修完毕处理逻辑代码
      wx.showModal({
        title:'维修完毕',
        content:'确认完成维修吗？',
        success:function(res){
          if(res.confirm){
            console.log('确认维修完毕');
            wx.request({
              url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
              data: {
                api: 'EquipmentSolveComplete',
                MainID:that.data.MainID,
              },
              method: 'POST',
              header: { 'Content-Type': 'application/x-www-form-urlencoded' },
              success: function (res) {
                // success
                console.log(res);//打印请求返回的结果
                if(res.data=='提交成功'){
                  //提醒
                  wx: wx.showToast({
                    title: '维修完毕提交成功！',
                    icon: 'none',
                    duration: 2000
                  })
                //提交成功后就回到列表界面，免得再次提交影响数据,redirectTo关闭当前页面，延时2秒执行，让别人看清楚提交成功的信息
                //把按钮都失效，防止在这2秒的时间内被人点击
                that.setData({
                  isReceive:true,
                  isStart:true,
                  isSave:true,
                  isComplete:true
                })
                setTimeout(function(){
                  wx.redirectTo({
                    url: '../../../repair/repair-solve/repair-solve'
                  })
                },2000)
                  }else if(res.data=='提交失败'){
                  //提醒
                  wx: wx.showToast({
                    title: '维修完毕提交失败！',
                    icon: 'none',
                    duration: 2000
                  })
                  }
              },
              fail: function (res) {
                // fail
              },
              complete: function (res) {
                // complete
              }
            })
          }else if(res.cancel){
            console.log('取消维修完毕')
          }
        }
      })
    }
  }
})