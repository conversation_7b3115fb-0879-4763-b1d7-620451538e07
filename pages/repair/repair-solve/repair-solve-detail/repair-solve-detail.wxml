<!--pages/repair/repair-solve/repair-solve-detail/repair-solve-detail.wxml-->
    <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">维修工单信息</text>
    </view>
  </view>

    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">报修人姓名:</view>
      <text selectable="true" class="sg-content">{{t1}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">报修人电话:</view>
      <text selectable="true" class="sg-content">{{t2}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">报修时间:</view>
      <text selectable="true" class="sg-content">{{t3}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">设备名称:</view>
      <text selectable="true" class="sg-content">{{t4}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">事业部:</view>
      <text selectable="true" class="sg-content">{{t5}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">车间:</view>
      <text selectable="true" class="sg-content">{{t6}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">班组:</view>
      <text selectable="true" class="sg-content">{{t7}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">故障描述:</view>
      <text selectable="true" class="sg-content">{{t8}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">计划维修时间:</view>
      <text selectable="true" class="sg-content">{{t9}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">安排维修工:</view>
      <text selectable="true" class="sg-content">{{t10}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">派工时间:</view>
      <text selectable="true" class="sg-content">{{t11}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">计划完成时间:</view>
      <text selectable="true" class="sg-content">{{t12}}</text>
    </view>
    <view class="cu-form-group">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);color:#009fa8">开始维修时间:</view>
      <text selectable="true" class="sg-content" style="color:#009fa8">{{t13}}</text>
    </view>

    <view class="cu-bar bg-white">
    <view class="action">
      <text class="cuIcon-title text-green"></text>
      <text class="text-xl text-bold text-black">维修处理</text>
    </view>
  </view>

  	<view class="cu-form-group" style="border-top: 0rpx">
		<view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">故障类别:</view>
		<picker bindchange="PickerChange" value="{{index}}" range="{{picker[0]}}" range-key="{{[1]}}">
			<view class="picker" style="text-align:left;color:gray">
				{{index?picker[0][index][1]:pickerstring}}
			</view>
		</picker>
	</view>

    <view class="cu-form-group" style="border-top: 0rpx">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">实际维修时间:</view>
    </view>
  	<view class="cu-form-group" style="border-top: 0rpx;height:100rpx;justify-content:center">
    <van-stepper value="{{textareaDValue}}" step="0.5" decimal-length="{{ 2 }}" input-width="100rpx" button-size="70rpx" min="0" bind:change="textareaDInput"/>
    <view class="title" style="font-size:28rpx">（小 时）</view>
	</view>

    <view class="cu-form-group" style="border-top: 0rpx">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">故障原因分析:</view>
    </view>
  	<view class="cu-form-group" style="border-top: 0rpx;height:200rpx">
		<textarea maxlength="-1" disabled="{{modalName!=null}}" value="{{textareaAValue}}" bindinput="textareaAInput" placeholder="请填写故障原因分析" style="height:200rpx;border: 1px dashed gray;text-color:gray;padding:5rpx;font-size:28rpx"></textarea>
	</view>

    <view class="cu-form-group" style="border-top: 0rpx">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">维修处理情况:</view>
    </view>
  	<view class="cu-form-group" style="border-top: 0rpx;height:200rpx">
		<textarea maxlength="-1" disabled="{{modalName!=null}}" value="{{textareaBValue}}" bindinput="textareaBInput" placeholder="请填写维修处理情况" style="height:200rpx;border: 1px dashed gray;text-color:gray;padding:5rpx;font-size:28rpx"></textarea>
	</view>

    <view class="cu-form-group" style="border-top: 0rpx">
      <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">更换配件信息:</view>
    </view>
  	<view class="cu-form-group" style="border-top: 0rpx;height:200rpx">
		<textarea maxlength="-1" disabled="{{modalName!=null}}" value="{{textareaCValue}}" bindinput="textareaCInput" placeholder="请填写配件更换信息" style="height:200rpx;border: 1px dashed gray;text-color:gray;padding:5rpx;font-size:28rpx"></textarea>
	</view>

  <view class="padding flex" style="flex-direction:row">
  <button class="send-btn bg-shimge lg" style="font-size:30rpx" bindtap="btnReceive" disabled="{{isReceive}}">接　收</button>
  <button class="send-btn bg-shimge lg" style="font-size:30rpx" bindtap="btnStart" disabled="{{isStart}}">开始维修</button>
</view>

  <view class="padding flex" style="flex-direction:row;padding-top:10rpx">
  <button class="send-btn bg-shimge lg" style="font-size:30rpx" bindtap="btnSave" disabled="{{isSave}}">保　存</button>
  <button class="send-btn bg-shimge lg" style="font-size:30rpx" bindtap="btnComplete" disabled="{{isComplete}}">维修完毕</button>
</view>