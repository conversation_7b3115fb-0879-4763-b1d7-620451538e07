<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索单号、料品" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="showModal" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" data-target="DrawerModalR" type="">筛选</button>
    </view>
  </view>
  <scroll-view scroll-x class="nav" scroll-with-animation scroll-left="{{scrollLeft}}">
    <view class="cu-item {{currentItem==1?'cur text-shimge':''}}" data-current-item="1" bindtap="tapcondition">当日</view>
    <view class="cu-item {{currentItem==2?'cur text-shimge':''}}" data-current-item="2" bindtap="tapcondition">当月</view>
    <view class="cu-item {{currentItem==3?'cur text-shimge':''}}" data-current-item="3" bindtap="tapcondition">上月</view>
  </scroll-view>
</view>

<view class="bg-white"> 
  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="basis-p21 padding-sm">日期</view>
    <view class="basis-p18 padding-sm">单号</view>
    <view class="basis-p20 padding-sm">品名</view>
    <view class="basis-p27 padding-sm">规格</view>
    <view class="basis-p14 padding-sm">数量</view>
  </view>
  <block wx:for="{{dataList}}" wx:for-item="item" wx:key="poLine">
    <view class="flex align-center solids-bottom sg-row" bindtap="taprow" data-vouchtype="{{item.cVouchType}}" data-subid="{{item.subID}}">
      <view class="basis-p21 padding-xs text-multcut3">{{filter.formatTime(item.dMakeDate)}}</view>
      <view class="basis-p18 padding-xs text-multcut3">{{item.cSourceCode}}</view>
      <view class="basis-p20 padding-xs text-multcut3">{{item.cMateName}}</view>
      <view class="basis-p27 padding-xs text-multcut3">{{item.cMateStd}}</view>
      <view class="basis-p14 padding-xs text-multcut3 text-center">{{item.fQuantity}}</view>
    </view>
  </block>
  <view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
  <view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>
</view>

<view class="cu-modal drawer-modal justify-end {{modalName=='DrawerModalR'?'show':''}}" bindtap="hideModal">
  <view class="cu-dialog basis-lg" catchtap style="top:{{CustomBar}}px;height:calc(100vh - {{CustomBar}}px)">
    <view class="cu-form-group">
      <view class="title">开始日期</view>
      <picker mode="date" value="{{dateBegin}}"  bindchange="DateBeginChange">
        <view class="picker">
          {{ filter.formatDate(dateBegin) }}
        </view>
      </picker>
    </view>
    <view class="cu-form-group">
      <view class="title">结束日期</view>
      <picker mode="date" value="{{dateEnd}}"  bindchange="DateEndChange">
        <view class="picker">
          {{ filter.formatDate(dateEnd) }}
        </view>
      </picker>
    </view> 
    <view class="cu-form-group">
      <button class="cu-btn bg-shimge shadow" bindtap="dlgModalOk">确定</button>
      <button class="cu-btn bg-shimge shadow" bindtap="dlgModalCancel">取消</button>
    </view>        
  </view>
</view>