const app = getApp()
import { wuxToast, http, updateAccountExpire, formatDate, dateAdd } from '../../../utils/util.js'

Page({
  data: {
    scrollLeft: 0,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    loadState: 0,//加载状态  1:继续下拉加载更多,2:加载中,3:没有更多了
    isError:false,//加载失败
    currentItem: 1,//当前的筛选项
    dataList:[],//待检验列表
    bottomLineId:null,//底部最后一行LineId,
    bottomId:null,//底部最后一行Id,用于上拉触底事件
    searchKey:'',//搜索关键字
    user:null, //当前用户
    dateBegin:null, //筛选-开始日期
    dateEnd:null, //筛选-结束日期
    dateFrom:null, //查询-开始日期
    dateTo:null   //查询-结束日期    
  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;
    
    this.setFromToDate(this.data.currentItem);

    this.getRequestData(true);
  },
  //设置筛选日期
  setFromToDate:function(type){
    //当日
    if(type ==1 ){
      let now = new Date();
      let dateBegin = formatDate(now) ;
      let dateEnd = formatDate(now) ;

      this.setData({
        dateBegin: dateBegin,
        dateEnd: dateEnd,
        dateFrom: dateBegin,
        dateTo: dateEnd
      });
    }
    //当月
    else if(type == 2){
      let now = new Date();
      let dateBegin = formatDate( new Date(now.getFullYear(),now.getMonth(),1) );
      let dateEnd = dateAdd('m',1,dateBegin);
      dateEnd = formatDate( dateAdd('d',-1,dateEnd) );

      this.setData({
        dateBegin: dateBegin,
        dateEnd: dateEnd,
        dateFrom: dateBegin,
        dateTo: dateEnd
      });
    }
    //上月
    else if(type == 3){
      let now = new Date();
      let dateBegin = new Date(now.getFullYear(),now.getMonth(),1) ;
      dateBegin = formatDate(dateAdd('m',-1,dateBegin));
      let dateEnd = dateAdd('m',1,dateBegin);
      dateEnd = formatDate( dateAdd('d',-1,dateEnd) );

      this.setData({
        dateBegin: dateBegin,
        dateEnd: dateEnd,
        dateFrom: dateBegin,
        dateTo: dateEnd
      });
    }
  },
  //选择查询条件
  tapcondition:function(e){
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    if (e.currentTarget.dataset){
      let cur = e.currentTarget.dataset.currentItem;
      this.setData({ 
        currentItem: cur,
        scrollLeft: (cur - 1) * 40
      });

      this.setFromToDate(this.data.currentItem);

      this.getRequestData(true, this.goTop);
    }
  },  
  //获取后台数据
  getRequestData: function (again, callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    if (again) {
      //重新获取数据
      this.data.bottomId = null;
      this.data.bottomLineId = null;
      this.data.dataList = [];
    }
    else{
      //上拉加载提示
      this.setData({ loadState: 2 });  
    }
    console.log('id:' + this.data.bottomLineId);
    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.personID;
    let type = this.data.currentItem;
    let dateFrom = this.data.dateFrom;
    let dateTo = this.data.dateTo;
    let key = this.data.searchKey;
    let beginDate = this.data.bottomId;
    let beginid = this.data.bottomLineId;
    let url = app.globalData.apiBase + '/api/QTAbnormal?user=' + user + '&org=' + org +'&fromDate=' + dateFrom + '&toDate=' + dateTo + '&key=' + key
            + '&beginDate=' + beginDate + '&beginid=' + beginid  + '&pagesize=' + pagesize;
    console.log(url);

    let suc = rd => {
      //更新数据
      let totalData = that.data.dataList;
      //判断数据大小是否等于每页数大小，相等则认为还有数据，否则认为没有更多数据
      let hasmore = rd.length == pagesize ? 1 : 0;
      if (rd.length > 0) {
        totalData = totalData.concat(rd);
        that.data.bottomId = rd[rd.length - 1].dMakeDate;
        that.data.bottomLineId = rd[rd.length - 1].subID;
      }

      that.setData({
        loadState: hasmore,
        dataList: totalData
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;

      if (callBack)
        callBack(rd);
    };

    let fl = () => {
      //更新数据
      that.data.bottomId = null;
      that.data.bottomLineId = null;
      that.setData({
        isError: true,
        loadState: 0,
        dataList: []
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false; 
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });     
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading){
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else{
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress>0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //搜索框输入完成
  confirmKeyInput: function(e){
    console.log(e.detail);

    this.data.searchKey = e.detail.value;

    this.getRequestData(true, this.goTop);
  },
  //查询按钮
  searchData: function(){

    this.getRequestData(true, this.goTop);
  },
  //滚动条回到顶部
  goTop: function () {
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //搜索文本改变事件
  bindKeyInput:function(e){
    console.log(e.detail);

    this.data.searchKey = e.detail.value;
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData(true);
  },
  //上拉加载更多
  onReachBottom: function () {
    if (this.data.loadState == 0 ||this.data.loadState == 3)
      return;
    this.getRequestData(false);
  },
  //跳转至明细页
  taprow: function (e) {
    console.log(e.currentTarget.dataset);

    let subid = e.currentTarget.dataset.subid;
    let vouchtype = e.currentTarget.dataset.vouchtype;
    if(subid){
      if(vouchtype=='05' || vouchtype=='04' || vouchtype=='06' || vouchtype=='07' || vouchtype=='08' || vouchtype=='09'){
        wx.navigateTo({
          url: 'qt-assrecord/qt-assrecord?vouchtype=' + vouchtype + '&&subid=' + subid
        });
      }
      else if(vouchtype=='01' || vouchtype=='02' || vouchtype=='03'){
        wx.navigateTo({
          url: 'qt-prerecord/qt-prerecord?vouchtype=' + vouchtype + '&&subid=' + subid
        });
      }
      else{
        wx.navigateTo({
          url: 'qt-rawrecord/qt-rawrecord?vouchtype=' + vouchtype + '&&subid=' + subid
        });
      }

    }

  }, 
  //显示弹窗
  showModal: function(e){
    this.setData({
      modalName: e.currentTarget.dataset.target,
      dateBegin: this.data.dateFrom,
      dateEnd: this.data.dateTo,
    })
  },
  //关闭弹窗
  hideModal: function(e) {
    this.setData({
      modalName: null
    })
  },
  //弹窗-确定按钮
  dlgModalOk: function(e){
    this.setData({
      dateFrom: this.data.dateBegin,
      dateTo: this.data.dateEnd,
    })

    this.hideModal();

    this.getRequestData(true, this.goTop);
  },  
  //弹窗-取消按钮
  dlgModalCancel: function(e){

    this.hideModal();
  },  
  //弹窗-开始日期
  DateBeginChange: function(e){
    this.setData({
      dateBegin: e.detail.value
    })
  },
  //弹窗-结束日期
  DateEndChange: function(e){
    this.setData({
      dateEnd: e.detail.value
    })
  },  
})