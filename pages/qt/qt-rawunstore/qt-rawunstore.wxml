<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索单号、供应商、料品" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>
</view>

<view class="bg-white"> 
  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="basis-p18 padding-sm">单号</view>
    <view class="basis-p25 padding-sm">供应商</view>
    <view class="basis-p18 padding-sm">品名</view>
    <view class="basis-p25 padding-sm">规格</view>
    <view class="basis-p14 padding-sm">数量</view>
  </view>
  <block wx:for="{{dataList}}" wx:for-item="item" wx:key="poLine">
    <view class="flex align-center solids-bottom sg-row" bindtap="taprow" data-sourcecode="{{item.cSourceCode}}">
      <view class="basis-p18 padding-xs text-multcut3">{{item.cArrivalCode}}</view>
      <view class="basis-p25 padding-xs text-multcut3">{{item.cVenName}}</view>
      <view class="basis-p18 padding-xs text-multcut3">{{item.cInvName}}</view>
      <view class="basis-p25 padding-xs text-multcut3">{{item.cInvStd}}</view>
      <view class="basis-p14 padding-xs text-multcut3 text-center">{{item.qtyTU}}</view>
    </view>
  </block>
  <view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
  <view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>
</view>
