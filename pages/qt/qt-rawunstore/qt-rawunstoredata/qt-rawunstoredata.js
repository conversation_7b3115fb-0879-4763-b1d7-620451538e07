const app = getApp()
import { wuxToast, http, updateAccountExpire, formatTime } from '../../../../utils/util.js'

Page({
  data: {
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    isError: false,//加载失败
    dataMain: null,//主表
    user: null, //当前用户
    typeIndex: -1, //当前选项
    standLine: [],//检验标准
    sourceCode: null
  },
  onShow: function(){

  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;

    this.data.sourceCode = options.sourceCode;

    this.getRequestData();
  },   
  //获取后台数据
  getRequestData: function (callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();
    wx.showLoading({ title: '加载中...', mask:true});

    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.personID;
    let sourceCode = this.data.sourceCode;     

    let url = app.globalData.apiBase + '/api/GetRawInspectByCode?user=' + user + '&org=' + org + '&code=' + sourceCode ;
    console.log(url);

    let suc = rd => {

      that.setData({
        dataMain: rd,
        standLine: rd.stands,
      });

      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      wx.hideLoading();
      that.data.isLoading = false;
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        dataMain: null,
        standLine: [],
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      wx.hideLoading();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });    
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //滚动条回到顶部
  goTop: function(){
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //下拉刷新
  onPullDownRefresh: function () {

    this.getRequestData();
  },
})