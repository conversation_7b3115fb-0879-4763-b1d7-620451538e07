<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索单号、料品" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>
  <scroll-view scroll-x class="nav" scroll-with-animation scroll-left="{{scrollLeft}}">
    <view class="cu-item {{currentItem==1?'cur text-shimge':''}}" data-current-item="1" bindtap="tapcondition">我的待检</view>
    <view class="cu-item {{currentItem==2?'cur text-shimge':''}}" data-current-item="2" bindtap="tapcondition">全部待检</view>
    <view class="cu-item {{currentItem==3?'cur text-shimge':''}}" data-current-item="3" bindtap="tapcondition">我的保存记录</view>
  </scroll-view>
</view>

<view class="bg-white"> 
  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="basis-p20 padding-sm">单号</view>
    <view class="basis-p17 padding-sm">料号</view>
    <view class="basis-p20 padding-sm">品名</view>
    <view class="basis-p28 padding-sm">规格</view>
    <view class="basis-p15 padding-sm">数量</view>
  </view>
  <block wx:for="{{dataList}}" wx:for-index="idx" wx:for-item="item" wx:key="poLine">
    <view class="flex align-center solids-bottom sg-row" bindtap="taprow" data-idx="{{idx}}" data-docno="{{item.docNo}}">
      <view class="basis-p20 padding-xs text-multcut">{{item.docNo}}</view>
      <view class="basis-p17 padding-xs text-multcut">{{item.cMateCode}}</view>
      <view class="basis-p20 padding-xs text-multcut">{{item.cMateName}}</view>
      <view class="basis-p28 padding-xs text-multcut">{{item.cMateStd}}</view>
      <view class="basis-p15 padding-xs text-multcut text-center">{{item.submitQty-item.qtyJY}}</view>
    </view>
  </block>
  <view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
  <view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>
</view>
