<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>

<view class="bg-white">
  <view class="cust-row">
    <view class="sg-label sg-mwidth3">供应商:</view>
    <text selectable="true" class="sg-content">{{dataMain.cVenName}}</text>
  </view>
  <view class="cust-row">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth2">料号:</view>
      <text selectable="true" class="sg-content">{{dataMain.cInvCode}}</text>
    </view>
    <view class="cust-col">
      <view class="sg-label sg-mwidth2">品名:</view>
      <text selectable="true" class="sg-content">{{dataMain.cInvName}}</text>
    </view>
  </view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth4">规格型号:</view>
    <text selectable="true" class="sg-content">{{dataMain.cInvStd}}</text>
  </view>
  <view class="cust-row">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth4">到货单号:</view>
      <text selectable="true" class="sg-content">{{dataMain.cArrivalCode}}</text>
    </view>
    <view class="cust-col">
      <view class="sg-label sg-mwidth4">到货数量:</view>
      <view class="sg-content">{{dataMain.qtyTU+dataMain.cComUnitName}}</view>   
    </view>
  </view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth4">到货时间:</view>
    <text selectable="true" class="sg-content">{{filter.formatDate(dataMain.arrivedTime)}}</text>
  </view>  
</view>

<view wx:if="{{!isError}}">

  <view wx:if="{{standLine.length>0}}">
    <view class="cu-bar bg-white solid-bottom margin-top-xs" style="min-height: 70rpx;">
      <view class="action">
        <text class="cuIcon-titles text-shimge"></text>
        <text class="text-df text-bold">检验标准</text>
      </view>
    </view>
    <view class="flex align-center bg-grey text-bold text-center text-content">
      <view class="basis-p25 padding-sm">检验项目</view>
      <view class="basis-p55 padding-sm">标准要求</view>
      <view class="basis-p20 padding-sm">不合格</view>
    </view>
    <checkbox-group bindchange="checkboxChange">
      <label class="flex bg-white align-center solids-bottom sg-row" wx:for="{{standLine}}" wx:for-item="item" wx:for-index="idx" wx:key="autoID">
        <view class="basis-p25 padding-xs">{{item.cItemName}}</view>
        <view class="basis-p55 padding-xs">{{item.cRequire}}</view>
        <view class="basis-p20 padding-xs">
          <checkbox wx:if="{{!dataMain.hasSubmit}}" value="{{item.autoID}}" checked="{{item.bUnQualified}}" class="round shimge"></checkbox>
          <text wx:elif="{{item.bUnQualified}}" class="cuIcon-check lg text-shimge" ></text>
        </view>
      </label>
    </checkbox-group> 
  </view>

  <view wx:if="{{dataMain.cArrivalCode}}">
    <view class="cu-bar bg-white solid-bottom margin-top-xs" style="min-height: 70rpx;">
      <view class="action">
        <text class="cuIcon-titles text-shimge"></text>
        <text class="text-df text-bold">交验信息</text>
      </view>
    </view>
    <view class="cu-form-group">
      <view class="title sg-mwidth4">检验标准:</view>
      <input bindtap="onSelectStand" disabled="true" placeholder="{{dataMain.hasSubmit?'':'请选择'}}" value="{{dataMain.standname}}"></input>
      <text wx:if="{{!dataMain.hasSubmit}}" bindtap="onSelectStand" class="cuIcon-right lg text-gray"></text>
    </view>   
    <view class="cu-form-group">
      <view class="title">抽检数量:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.fSamplingQty}}</text>
      <van-stepper wx:else input-width="5em" min="0" value="{{dataMain.fSamplingQty}}" bind:change="onSamplingQtyChange" />
    </view>
    <view class="cu-form-group">
      <view class="title">抽检不良数:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.fFailQty}}</text>
      <van-stepper wx:else input-width="5em" min="0" value="{{dataMain.fFailQty}}" bind:change="onFailQtyChange" />
    </view>
    <view class="cu-form-group">
      <view class="title">处置方式:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{handlePicker.length>0 && handleIndex>=0 ? handlePicker[handleIndex].cItemName : ''}}</text>
      <picker wx:else bindchange="handlePickerChange" value="{{handleIndex}}" range="{{handlePicker}}" range-key="cItemName">
        <view class="picker {{handlePicker.length>0 && handleIndex>=0 ? '' : 'sg-placeholder'}}">
          {{handlePicker.length>0 && handleIndex>=0 ? handlePicker[handleIndex].cItemName : '请选择'}}
        </view>
      </picker>
    </view>    
    <view class="cu-form-group">
      <view class="title">合格收货数:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.fReceiveQty}}</text>
      <van-stepper wx:else input-width="5em" min="0" value="{{dataMain.fReceiveQty}}" bind:change="onReceiveQtyChange" />
    </view>
    <view class="cu-form-group">
      <view class="title">问题点:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.cProblems}}</text>
      <textarea wx:else auto-height maxlength="200" bindinput="inputProblems" value="{{dataMain.cProblems}}" placeholder="请输入问题点" ></textarea>
    </view>      
    <view class="cu-form-group">
      <view class="title">不良分类:</view>
      <view wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{badPicker.length>0 && badIndex>=0 ? badPicker[badIndex] : ''}}</view>
      <picker wx:else disabled="{{dataMain.fFailQty<=0}}" bindchange="badPickerChange" value="{{badIndex}}" range="{{badPicker}}" >
        <view class="picker {{badPicker.length>0 && badIndex>=0 ? '' : 'sg-placeholder'}}">
          {{badPicker.length>0 && badIndex>=0 ? badPicker[badIndex] : '请选择'}}
        </view>
      </picker>
    </view> 
    <view class="cu-form-group">
      <view class="title">故障日期:</view>
      <view wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.dDateGK ? filter.formatDate(dataMain.dDateGK) : ''}}</view>
      <picker wx:else mode="date" value="{{dataMain.dDateGK}}" bindchange="onDateGKChange" >
        <view class="picker {{dataMain.dDateGK ? '' : 'sg-placeholder'}}">
          {{dataMain.dDateGK ? filter.formatDate(dataMain.dDateGK) : '请选择'}}
        </view>
      </picker>
    </view>    
    <view class="cu-form-group">
      <view class="title">赔偿比例(%):</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.fPayPer}}</text>
      <van-stepper wx:else input-width="5em" min="0" value="{{dataMain.fPayPer}}" bind:change="onPayPerChange" />
    </view>     
    <view class="cu-form-group">
      <view class="title">赔偿金额:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.fPayAmount}}</text>
      <van-stepper wx:else input-width="5em" min="0" value="{{dataMain.fPayAmount}}" bind:change="onPayAmountChange" />
    </view> 
    <view class="cu-form-group">
      <view class="title">备注:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.cPayMemo}}</text>
      <textarea wx:else auto-height maxlength="200" bindinput="inputPayMemo" value="{{dataMain.cPayMemo}}" placeholder="请输入备注" ></textarea>
    </view>    
    <view class="cu-form-group">
      <view class="title">是否考核</view>
      <switch disabled="{{dataMain.hasSubmit}}" bindchange="onAssessChange" checked="{{dataMain.bAssess}}"></switch>
    </view>
    <view class="cu-form-group">
      <view class="title">不考核原因:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.cAssessRegion}}</text>
      <textarea wx:else disabled="{{dataMain.bAssess}}" auto-height maxlength="200" bindinput="inputAssessRegion" value="{{dataMain.cAssessRegion}}"  placeholder="{{dataMain.bAssess?'':'请输入不考核原因'}}" ></textarea>
    </view>
  
  </view>
  
</view>
<view class="cu-load bg-red erro" wx:else></view> 

<view class="cu-bar tabbar border foot bg-shimge" disabled="{{isLoading}}" >
  <view class="submit solids-left" bindtap="viewInfo">查看资料</view>
  <view class="submit solids-left" bindtap="SaveData">保存</view>
  <view class="submit solids-left" bindtap="submitDoc">提交</view>
</view> 
