const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../../../utils/util.js'

Page({
  data: {
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    isError:false,//加载失败
    dataList:[],//列表
    user:null, //当前用户
    itemCode:null //查询料号
  },
  onLoad: function (options) {

    this.data.itemCode = options.itemCode;

    this.getRequestData();
  },
  //获取后台数据
  getRequestData: function (callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let url = app.globalData.apiBase + '/api/GetChangeFiles?name=' + this.data.itemCode;
    console.log(url);

    let suc = rd => {

      if(rd && rd.attachments && rd.attachments.length>0){
        that.setData({dataList: rd.attachments});
      }
      else{
        that.setData({dataList: []});

        if(rd.message)
          wuxToast(rd.message);
      }
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;

      if (callBack)
        callBack(rd);
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        dataList: []
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false; 
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });     
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading){
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else{
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress>0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData();
  },
  //打开附件
  taprow: function (e) {
    console.log(e.currentTarget.dataset);

    let path = e.currentTarget.dataset.path;
    if (path){

      this.openFile(app.globalData.apiBase+'/temp',path);
    }
  },
  getFileName:function(path) {
    var name = path;
    var index = path.lastIndexOf('/');
    if(index>=0){
      name = path.substr(index);
    }
    return name;
  },
  //打开文件
  openFile:function(serv,file,callback){
    let f = file.replace(/\\/g,'/');

    console.log('打开文件:',serv+'/'+f)

    let name = this.getFileName(f);

    wx.showLoading({ title: '加载中...', mask:true});

    wx.downloadFile({
      url: serv+'/'+f,
      filePath: wx.env.USER_DATA_PATH + name, //防止IOS中报错：No tempFilePath
      success: function (res) {
        var filePath = res.filePath
        wx.openDocument({
          showMenu: false,
          filePath: filePath,
        })
      },
      complete: function(res){
        wx.hideLoading(); 
      }
    })
  },      

})