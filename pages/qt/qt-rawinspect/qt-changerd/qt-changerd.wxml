<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>

<view class="bg-white"> 
  <view class="flex align-center bg-cyan text-bold text-center text-content">
    <view class="basis-p20 padding-sm">修改时间</view>
    <view class="basis-p30 padding-sm">变更名称</view>
    <view class="basis-p30 padding-sm">附件名称</view>
    <view class="basis-p20 padding-sm">更改类型</view>
  </view>
  <block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="idx" >
    <view class="flex align-center solids-bottom sg-row" bindtap="taprow" data-path="{{item.path}}" >
      <view class="basis-p20 padding-xs sg-wordwrap">{{item.attachmentModifyTime}}</view>
      <view class="basis-p30 padding-xs sg-wordwrap">{{item.changeObjectName}}</view>
      <view class="basis-p30 padding-xs sg-wordwrap">{{item.attachmentName}}</view>
      <view class="basis-p20 padding-xs sg-wordwrap">{{item.changeType}}</view>
    </view>
  </block>
  <view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
</view>
