page{
  padding-bottom: 90rpx;
}

.cu-form-group{
  justify-content:start;
  min-height: 70rpx;
  padding: 1rpx 10rpx;
}

.cu-form-group picker .picker {
	line-height: 60rpx;
	font-size: 32rpx;
  text-align: left;
}

.cu-form-group picker::after{
  line-height: 60rpx;
}

.sg-box-sm{
  min-width: calc(13em + 15rpx);
}

.cu-list.menu-avatar>.cu-item {
	height: 80rpx;
}

.cu-list.menu-avatar>.cu-item+.cu-item {
	border-top: 1rpx solid #eee;
}

.cu-list.menu-avatar>.cu-item .cu-form-group {
	position: absolute;
	left: 0rpx;
}

.basis-p20 {
	flex-basis: 20%;
}
.basis-p25 {
	flex-basis: 25%;
}
.basis-p55 {
	flex-basis: 55%;
}

.lg{
  font-size: 42rpx
}