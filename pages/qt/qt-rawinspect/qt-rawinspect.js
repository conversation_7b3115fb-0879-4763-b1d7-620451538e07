const app = getApp()
import { wuxToast, http, updateAccountExpire, formatTime, formatDate } from '../../../utils/util.js'

Page({
  data: {
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    isError: false,//加载失败
    dataMain: null,//主表
    user: null, //当前用户
    handlePicker: [], //处置方式下拉选项
    handleIndex: -1, //当前选项
    badPicker: ['外观不良','加工问题','漏气','漏加工','性能问题','其他'], //不良分类下拉选项
    badIndex: -1, //当前选项    
    standLine: [],//检验标准
    docno:null, //到货单号
    docid:null //到货行id
  },
  onShow: function(){

  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;

    this.getBasicInfo();

    //跳转链接
    if(options.id){
      this.setData({
        docno: options.docno,
        docid: options.id
      });

      this.getRequestData();
    }

  },   
  //获取处置方式
  getBasicInfo: function(){
    let that = this;
    let org = this.data.user.org;

    let type = 'IncomingHandling'

    let url = app.globalData.apiBase + '/api/QTBasicInfo?type='+type ;
    console.log(url);

    let suc = rd => {

      //合格收货
      if(rd){
        let hd = {cItemCode:'合格收货',cItemName:'合格收货'};
        rd.splice(0,0,hd)
      }
      //设置下拉
      let handleIdx = -1;
      if(that.data.dataMain && rd && rd.length>0){
        for(let m=0; m<rd.length; m++){
          if(rd[m].cItemCode == that.data.dataMain.cHandling){
            handleIdx = m;
            break;
          }
        }
      }

      that.setData({
        handlePicker: rd,
        handleIndex: handleIdx
      });
    };
    http(url, 'GET', null, suc, null);
  },

  //读取临时保存数据 
  getTempData: function(user,type,key,callBack){
    let that = this;
    let url = app.globalData.apiBase + '/api/GetTempInspect?user=' + user + '&type=' + type + '&key=' + key ;
    console.log(url);

    let suc = rd => {
      let data = app.globalData.curinspect;
      if(rd && rd.ins){
        data.fTotalQty = rd.ins.fQuantity;
        data.fFailQty = rd.ins.fUnQual;
        data.fSamplingQty = rd.ins.fSamplingQty;
        data.fReceiveQty = rd.ins.fReceiveQty;
        data.cBadType = rd.ins.cBadType;
        data.cProblems = rd.ins.cProblems;
        data.cHandling = rd.ins.cHandle;
        data.fPayPer = rd.ins.fPayPer;
        data.fPayAmount = rd.ins.fPayAmount;
        data.cAssessRegion = rd.ins.cAssessRegion;
        data.bAssess = rd.ins.bAssess;
        data.cPayMemo = rd.ins.cPayMemo;
        data.dDateGK = rd.ins.dDateGK;

        if(rd.stands){
          for(let i=0;i<rd.stands.length;i++){
            let std = {};
            std.autoID = rd.stands[i].cStandID;
            std.cStandID = rd.stands[i].cStandID;
            std.cStandCode = rd.stands[i].cStandCode;
            std.cStandName = rd.stands[i].cStandName;
            std.iRow = rd.stands[i].iRow;
            std.cItemName = rd.stands[i].cItemName;
            std.cRequire = rd.stands[i].cRequire;
            std.bUnQualified = rd.stands[i].bUnQualified;

            data.stands.push(std);

            //检验标准
            data.standid = rd.stands[i].id;
            data.standname = rd.stands[i].cStandName;
          }
        }

        //全局变量
        app.globalData.curinspect = data;
      }

      //更新检验单数据
      that.updateAssData();

      //默认检验标准
      if(data && !data.standid){
        that.updateStandardByInv(data.cInvCode,data.cCategoryCode);
      }

      if (callBack)
        callBack();
    };

    let fl = () => {
      if (callBack)
        callBack();
    };

    http(url, 'GET', null, suc, fl);
  },   
  //获取后台数据
  getRequestData: function (callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();
    wx.showLoading({ title: '加载中...', mask:true});

    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.personID;   
    let id = this.data.docid;

    let url = app.globalData.apiBase + '/api/QTRawInspect?user=' + user + '&org=' + org + '&id=' + id ;
    console.log(url);

    let suc = rd => {
      //更新数据
      let data = null;
      let docno = null;
      if(rd.length > 0){
        data = rd[0];
        //质量系统字段
        data.fTotalQty = data.qtyTU;//到货总数
        data.fReceiveQty = data.qtyTU;//合格收货数
        data.cHandling = '合格收货';//处置方式
        data.cBadType = null;//不良分类
        data.fSamplingQty = 0;//抽检数量
        data.fFailQty = 0;//抽检不良数
        data.bAssess = true;//是否考核
        data.dDateGK = formatTime(formatDate(new Date()));//故障日期

        data.stands = new Array();//检验标准明细 

        docno = data.cArrivalCode + '-' + data.iArrivalRow;
      }

      //全局变量
      app.globalData.curinspect = data;

      let follow = ()=>{
        //隐藏加载动画,停止下拉刷新
        wx.hideNavigationBarLoading();
        wx.stopPullDownRefresh();
        wx.hideLoading();
        that.data.isLoading = false;

        if (callBack)
          callBack();
      }

      that.getTempData(user,'来料',docno,follow);

    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        dataMain: null,
        standLine: [],
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      wx.hideLoading();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });    
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //滚动条回到顶部
  goTop: function(){
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //下拉刷新
  onPullDownRefresh: function () {

    this.getRequestData();
  },
  //问题点
  inputProblems:function(e){
    console.log('问题点',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.cProblems = e.detail.value;

      this.setData({ dataMain: main });
    }
  },
  //备注
  inputPayMemo:function(e){
    console.log('备注',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.cPayMemo = e.detail.value;

      this.setData({ dataMain: main });
    }
  },
  //不考核原因
  inputAssessRegion:function(e){
    console.log('不考核原因',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.cAssessRegion = e.detail.value;

      this.setData({ dataMain: main });
    }
  },
  //是否考核
  onAssessChange:function(e){
    console.log('不考核原因',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.bAssess = e.detail.value;
      if(main.bAssess)
        main.cAssessRegion = '';

      this.setData({ dataMain: main });
    }
  },
  //选择故障日期
  onDateGKChange: function(e){
    console.log('故障日期',e.detail.value);

    let main = app.globalData.curinspect;
    if(main){
      main.dDateGK = formatTime(e.detail.value);
    }

    this.data.dataMain = main;
    this.setData({
      dataMain: main
    })
  },
  //选择不良分类
  badPickerChange: function(e) {
    console.log('不良分类',e.detail.value);

    let main = app.globalData.curinspect;
    if(main){
      if(e.detail.value>=0 && this.data.badPicker && this.data.badPicker.length>0)
        main.cBadType = this.data.badPicker[e.detail.value];
      else
        main.cBadType = null;
    }

    this.data.dataMain = main;
    this.setData({
      badIndex: e.detail.value
    })
  },  
  //选择处置方式
  handlePickerChange: function(e) {
    console.log('处置方式：',e.detail.value);

    let main = app.globalData.curinspect;
    if(main){
      if(e.detail.value>=0 && this.data.handlePicker && this.data.handlePicker.length>0)
        main.cHandling = this.data.handlePicker[e.detail.value].cItemCode;
      else
        main.cHandling = null;
    }

    //非合格收货,清除合格收货数
    if(main.cHandling!='合格收货'){
      main.fReceiveQty = 0;
    }

    this.setData({
      dataMain: main,
      handleIndex: e.detail.value
    })
  },
  //合格收货数数值变化
  onReceiveQtyChange: function(e){
    console.log('合格收货数',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.fReceiveQty = e.detail;

      this.setData({ dataMain: main });
    }
  },
  //抽检数量数值变化
  onSamplingQtyChange: function(e){
    console.log('抽检数量',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.fSamplingQty = e.detail;

      this.setData({ dataMain: main });
    }
  },  
  //抽检不良数数值变化
  onFailQtyChange: function(e){
    console.log('抽检不良数',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.fFailQty = e.detail;

      this.setData({ dataMain: main });
    }
  }, 
  //赔偿比例数值变化
  onPayPerChange: function(e){
    console.log('赔偿比例',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.fPayPer = e.detail;

      this.setData({ dataMain: main });
    }
  }, 
  //赔偿金额数值变化
  onPayAmountChange: function(e){
    console.log('赔偿金额',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.fPayAmount = e.detail;

      this.setData({ dataMain: main });
    }
  },       
  //更新信息
  updateAssData:function(){

    //全局变量
    let main = app.globalData.curinspect;
    let stands = [];
    if(app.globalData.curinspect && app.globalData.curinspect.stands)
      stands = app.globalData.curinspect.stands;

    //设置下拉选项
    let handleIdx = -1;
    let badIndex = -1;
    if(main){

      //处置方式
      if(this.data.handlePicker && this.data.handlePicker.length>0){
        for(let m=0; m<this.data.handlePicker.length; m++){
          if(this.data.handlePicker[m].cItemCode == main.cHandling){
            handleIdx = m;
            break;
          }
        }
      }

      //不良分类
      if(this.data.badPicker && this.data.badPicker.length>0){
        for(let n=0; n<this.data.badPicker.length; n++){
          if(this.data.badPicker[n] == main.cBadType){
            badIndex = n;
            break;
          }
        }
      }
    }

    console.log('刷新全局变量curinspect',main);

    this.setData({
      handleIndex: handleIdx,
      badIndex: badIndex,
      dataMain: main,
      standLine: stands
    });
  },
  //保存按钮事件
  SaveData(){
    this.saveDoc(true);
  },
  saveDoc: function(isShow){
    if(!this.data.dataMain || !this.data.dataMain.cArrivalCode){
      return;
    }
    if(this.data.dataMain.hasSubmit)
      return;
    wx.showLoading({ title: '正在保存...', mask: true });

    let main = this.data.dataMain;
    let stands = this.data.standLine;    

    main.cMenderCode = this.data.user.code;
    main.cMender = this.data.user.name;
    main.dModifyDate = formatTime(new Date());
    //全局变量
    app.globalData.curinspect = main;
    if(stands)
      app.globalData.curinspect.stands = stands;
    else
      app.globalData.curinspect.stands = [];      

    //保存缓存
    //wx.setStorageSync('inspect_raw',app.globalData.curinspect);
    //console.log('inspect_raw保存',app.globalData.curinspect);
    
    //临时保存至数据库
    let ins = {};
    ins.type = '来料';
    ins.orgCode = main.ownOrg_Code;
    ins.docNo = main.cArrivalCode + '-' + main.iArrivalRow;
    ins.fQuantity = main.fTotalQty;
    ins.fUnQual = main.fFailQty;
    ins.fSamplingQty = main.fSamplingQty;
    ins.fReceiveQty = main.fReceiveQty;
    ins.cBadType = main.cBadType;
    ins.cProblems = main.cProblems;
    ins.cHandle = main.cHandling;
    ins.fPayPer = main.fPayPer;
    ins.fPayAmount  = main.fPayAmount;
    ins.cAssessRegion  = main.cAssessRegion;
    ins.bAssess  = main.bAssess;
    ins.cPayMemo  = main.cPayMemo;
    ins.dDateGK  = main.dDateGK;
    ins.cMenderCode = main.cMenderCode;
    ins.cMender = main.cMender;
    ins.dModifyDate = main.dModifyDate;

    let dto = {};
    dto.ins = ins;

    dto.stands=[];
    for(let i=0; i<stands.length; i++){
      let std = {};
      std.iRow = stands[i].iRow;
      std.cStandID = stands[i].autoID;
      std.cStandCode = stands[i].cStandCode;
      std.cStandName = stands[i].cStandName;
      std.cItemName = stands[i].cItemName;
      std.cRequire = stands[i].cRequire;
      std.bUnQualified = stands[i].bUnQualified;

      dto.stands.push(std);
    }

    let that = this;
    let url = app.globalData.apiBase + '/api/QTBasicInfo';
    console.log(url);

    let suc = rd => {
      
      wx.hideLoading();
      if(rd!='保存成功'){
        wx.showToast({title: rd,icon: 'none',duration: 2000})
      }
      else if(isShow){
        wx.showToast({title: '保存成功',icon: 'success',duration: 1500})
      }
    };

    let fl = () => {
      wx.hideLoading();
      wx.showToast({title: '保存失败',icon: 'none',duration: 2000})
    };

    http(url, 'POST', dto , suc, fl);
  },
  //提交
  submitToSev:function(){
    let main = this.data.dataMain;
    //保存
    this.saveDoc(false);

    wx.showLoading({ title: '正在提交...', mask: true });
    let that = this;
    let url = app.globalData.apiBase + '/api/QTRawInspect';
    console.log(url);

    let suc = rd => {

      if(rd == '提交成功'){
        //提交时间
        main.hasSubmit = true;
        that.setData({dataMain:main});
        //清除缓存
        //wx.setStorageSync('inspect_raw',null);
      }
      
      wx.hideLoading();
      wx.showToast({title: rd,icon: 'none',duration: 2000})
    };

    let fl = () => {
      wx.hideLoading();
      wx.showToast({title: '提交失败',icon: 'none',duration: 2000})
    };

    http(url, 'POST', app.globalData.curinspect , suc, fl);
  },
  //提交按钮事件
  submitDoc: function(e){
    if(!this.data.dataMain || !this.data.dataMain.cArrivalCode){
      return;
    }
    if(this.data.dataMain.hasSubmit)
      return;
    //校验
    let main = this.data.dataMain;
    let stands = this.data.standLine; 
    console.log(main)

    let bUnqua = true;
    for(var i=0;i<stands.length;i++){
      if(stands[i].bUnQualified){
        bUnqua = false;
        break;
      }
    }

    if(!bUnqua && main.cHandling == '合格收货'){
      wuxToast('请调整处置方式');
      return;
    }

    if(main.fReceiveQty > main.qtyTU){
      wuxToast('合格收货数不能超过到货数量,请调整');
      return;
    }
    if(main.fSamplingQty > main.qtyTU){
      wuxToast('抽检数量不能超过到货数量,请调整');
      return;
    }
    if(main.fFailQty > main.fSamplingQty){
      wuxToast('抽检不良数不能超过抽检数量,请调整');
      return;
    }
    if(main.fFailQty>0 && (!main.cProblems || main.cProblems.trim().length==0)){
      wuxToast('问题点不能为空,请调整');
      return;
    }
    if(!main.bAssess && (!main.cAssessRegion || main.cAssessRegion.trim().length==0)){
      wuxToast('不考核原因不能为空,请调整');
      return;
    }

    let that = this;
    wx.showModal({
      title: '提示',
      content: '是否确定提交?请检查数据是否输入无误',
      confirmText: '是',
      cancelText: '否',
      success (res) {
        if (res.confirm) {
          that.submitToSev();
        } 
        else if (res.cancel) {
        }
      }
    });
    
  },
  //选择检验标准
  onSelectStand:function(){
    if(this.data.dataMain && this.data.dataMain.hasSubmit)
      return;

    wx.navigateTo({
      url: '../qt-preinspect/qt-standard/qt-standard'
    });

  },
  //检验标准勾选不合格
  checkboxChange:function(e){
    console.log(e.detail);
    let items = this.data.standLine;
    let values = e.detail.value;

    for (let i = 0; i < items.length; i++) {
      items[i].bUnQualified = false

      for (let j = 0; j < values.length; j++) {
        if (items[i].autoID == values[j]) {
          items[i].bUnQualified = true
          break
        }
      }
    }

    this.setData({ standLine: items });
  },
  //更新检验标准
  updateStandard:function(stand, lines){

    if(!this.data.dataMain || !this.data.dataMain.cArrivalCode){
      return;
    }
    let main = app.globalData.curinspect;

    main.standid = stand.id;
    main.standname = stand.cName;
    main.stands = lines;

    this.setData({
      dataMain: main,
      standLine: main.stands
    });
  },
  //根据料品更新检验标准
  updateStandardByInv:function(invcode, typecode){

    let that = this;
    let org = this.data.user.org;

    let url = app.globalData.apiBase + '/api/GetStandardDefItems?invcode='+invcode+'&typecode='+typecode ;
    console.log(url);

    let suc = rd => {
      let main = app.globalData.curinspect;
      main.standid = null;
      main.standname = null;
      main.stands = [];

      if(rd && rd.length>0){
        main.standid = rd[0].id;
        main.standname = rd[0].cStandName;
        main.stands = rd;
      }

      that.setData({
        dataMain: main,
        standLine: main.stands
      });
    };
    http(url, 'GET', null, suc, null);

  },

  //查看资料事件
  viewInfo: function(){
    if(!this.data.dataMain || !this.data.dataMain.cArrivalCode){
      return;
    }

    //供应商对应的质检员
    var exam = this.data.dataMain.examiner;
    var user = this.data.user.code;
    var hasPower = exam.search(user)>-1?true:false;

    console.log(hasPower?'有图纸权限':'没有图纸权限')

    //功能菜单
    var itemList = ['检验指导书','技术变更单'];
    if(hasPower)
      itemList = ['检验指导书','技术变更单','技术图纸'];

    var that = this;
    //显示操作菜单
    wx.showActionSheet({
      itemList: itemList,
      success (res) {
        console.log('操作菜单',res.tapIndex)

        if(res.tapIndex == 0)
          that.viewQuaBook();        
        else if(res.tapIndex == 1)
          that.viewChangeRd();
        else if(res.tapIndex == 2)
          that.viewDrawing();  
      },
      fail (res) {
        console.log(res.errMsg)
      }
    })
  },
  //技术图纸
  viewDrawing: function(){
    var code = this.data.dataMain.cInvCode;

    if(code){
      wx.showLoading({ title: '加载中...', mask:true});
  
      let that = this;
      let url = app.globalData.apiBase + '/api/GetDrawingFile?name=' + code;
      console.log(url);
  
      let fl = () => {
        wx.hideLoading(); 
      };
      let suc = rd => {

        if(rd && rd.attachments && rd.attachments.length>0)
          that.openFile(app.globalData.apiBase+'/temp',rd.attachments[0].path,fl);
        else{
          wx.hideLoading(); 
          
          if(rd && rd.message)
            wuxToast(rd.message);
        }

      };
  
      http(url, 'GET', null, suc, fl);
    }

  },  
  //技术变更单
  viewChangeRd: function(){
    var code = this.data.dataMain.cInvCode;

    wx.navigateTo({
      url: 'qt-changerd/qt-changerd?itemCode='+code
    });
  },  
  //检验指导书
  viewQuaBook: function(){
    var std = this.data.dataMain.standname;

    if(std){
      wx.showLoading({ title: '加载中...', mask:true});
  
      let that = this;
      let url = app.globalData.apiBase + '/api/GetStandardFile?name=' + std;
      console.log(url);
  
      let fl = () => {
        wx.hideLoading(); 
      };
      let suc = rd => {
        that.openFile(app.globalData.apiBase,rd,fl);
      };
  
      http(url, 'GET', null, suc, fl);
    }
    else{
      wuxToast('请先选择检验标准');
    }
  },
  getFileName:function(path) {
    var name = path;
    var index = path.lastIndexOf('/');
    if(index>=0){
      name = path.substr(index);
    }
    return name;
  },
  //打开文件
  openFile:function(serv,file,callback){
    let f = file.replace(/\\/g,'/');

    console.log('打开文件:',serv+'/'+f)

    let name = this.getFileName(f);

    wx.downloadFile({
      url: serv+'/'+f,
      filePath: wx.env.USER_DATA_PATH + name, //防止IOS中报错：No tempFilePath
      success: function (res) {
        var filePath = res.filePath
        wx.openDocument({
          showMenu: false,
          filePath: filePath,
        })
      },
      complete: function(res){
        if(callback)
          callback();
      }
    })
  },      
})