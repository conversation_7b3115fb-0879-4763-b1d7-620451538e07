const app = getApp()
import { wuxToast, http, updateAccountExpire, formatTime } from '../../../utils/util.js'

Page({
  data: {
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    isError: false,//加载失败
    dataMain: null,//主表
    dataLine: [],//明细表
    searchKey: '',//搜索关键字
    user: null, //当前用户
    pipePicker: [], //流水线下拉选项
    pipeIndex: -1, //当前选项
    handlePicker: [], //处置方式下拉选项
    handleIndex: -1, //当前选项
    typePicker: [], //检验类型下拉选项
    typeIndex: -1, //当前选项    
  },
  onShow: function(){

  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;

    this.getBasicInfo();
    this.getVouchType();
  },
  //获取检验类型
  getVouchType: function(){
    let that = this;
    let org = this.data.user.org;

    let url = app.globalData.apiBase + '/api/QTVouchType?doctype=ass' ;
    console.log(url);

    let suc = rd => {

      //设置下拉
      let typeIdx = -1;
      if(that.data.dataMain && rd && rd.length>0){
        for(let m=0; m<rd.length; m++){
          if(rd[m].code == that.data.dataMain.cVouchType){
            typeIdx = m;
            break;
          }
        }
      }

      that.setData({
        typePicker: rd,
        typeIndex: typeIdx
      });
    };
    http(url, 'GET', null, suc, null);
  },  
  //获取处置方式
  getBasicInfo: function(){
    let that = this;
    let org = this.data.user.org;

    let url = app.globalData.apiBase + '/api/QTBasicInfo?type=ProductHandle' ;
    console.log(url);

    let suc = rd => {

      //设置下拉
      let handleIdx = -1;
      if(that.data.dataMain && rd && rd.length>0){
        for(let m=0; m<rd.length; m++){
          if(rd[m].cItemCode == that.data.dataMain.cHandle){
            handleIdx = m;
            break;
          }
        }
      }

      that.setData({
        handlePicker: rd,
        handleIndex: handleIdx
      });
    };
    http(url, 'GET', null, suc, null);
  },
  //获取流水线档案
  getPipeInfo: function(dept,callBack){
    let that = this;
    let org = this.data.user.org;

    let url = app.globalData.apiBase + '/api/QTPipeline?org=' + org + '&dept='+dept ;
    console.log(url);

    let suc = rd => {
      //设置下拉
      let pipeIdx = -1;
      if(that.data.dataMain && rd && rd.length>0){
        for(let n=0; n<rd.length; n++){
          if(rd[n].cLineCode == that.data.dataMain.cPipelineCode){
            pipeIdx = n;
            break;
          }
        }
      }

      that.setData({
        pipePicker: rd,
        pipeIndex: pipeIdx
      });

      if (callBack)
        callBack();
    };

    let fl = () => {
      //更新数据
      that.setData({
        pipePicker: [],
        pipeIndex: -1
      }); 

      if (callBack)
        callBack();
    };

    http(url, 'GET', null, suc, fl);
  },  
  //读取临时保存数据 
  getTempData: function(user,type,key,callBack){
    let that = this;
    let url = app.globalData.apiBase + '/api/GetTempInspect?user=' + user + '&type=' + type + '&key=' + key ;
    console.log(url);

    let suc = rd => {
      let data = app.globalData.curinspect;
      if(rd && rd.ins){
        data.cHandle = rd.ins.cHandle;
        data.cPipelineCode = rd.ins.cPipelineCode;
        data.cPipelineName = rd.ins.cPipelineName;     
        data.fQuantity = rd.ins.fQuantity;
        data.cVouchType = rd.ins.cVouchType;
        data.fUnQual = rd.ins.fUnQual;
        data.cMenderCode = rd.ins.cMenderCode;
        data.cMender = rd.ins.cMender;
        data.dModifyDate = rd.ins.dModifyDate;
        //合格率计算
        if(data.fQuantity)
          data.fPercentPass = 1-data.fUnQual/data.fQuantity;
        else
          data.fPercentPass = 1;

        if(rd.stands){
          for(let i=0;i<rd.stands.length;i++){
            let std = {};
            std.cStandCode = rd.stands[i].cStandCode;
            std.cStandName = rd.stands[i].cStandName;
            std.iRow = rd.stands[i].iRow;
            std.cItemName = rd.stands[i].cItemName;
            std.cRequire = rd.stands[i].cRequire;
            std.bUnQualified = rd.stands[i].bUnQualified;

            data.stands.push(std);
          }
        }

        if(rd.lines){
          for(let i=0;i<rd.lines.length;i++){
            let ln = {};
            ln.cVouchType = data.cVouchType;
            ln.cMateCode = data.cMateCode;
            ln.cMateName = data.cMateName;
            ln.cMateStd = data.cMateStd;
            ln.cMenderCode = data.cMenderCode;
            ln.cMender = data.cMender;
            ln.dModifyDate = data.dModifyDate;
            ln.cItemCode = rd.lines[i].cItemCode;
            ln.cItemName = rd.lines[i].cItemName;
            ln.nQty = rd.lines[i].nQty;
            ln.fPayPer = rd.lines[i].fPayPer;
            ln.fPayAmount = rd.lines[i].fPayAmount;
            ln.cReason = rd.lines[i].cReason;
            ln.cOperatorName = rd.lines[i].cOperatorName;
            ln.cProcess = rd.lines[i].cProcess;
            ln.cItemQty = rd.lines[i].cItemQty;

            data.lines.push(ln);
          }
        }
        
        //全局变量
        app.globalData.curinspect = data;
      }

      //更新检验单数据
      that.updateAssData();

      if(data){
        //获取流水线
        that.getPipeInfo(data.deptCode);
      }
      else{
        that.setData({ pipePicker: []}); 
      }

      if (callBack)
        callBack();
    };

    let fl = () => {
      if (callBack)
        callBack();
    };

    http(url, 'GET', null, suc, fl);
  }, 
  //获取后台数据
  getRequestData: function (key, callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();
    wx.showLoading({ title: '加载中...', mask:true});

    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.personID;   

    let url = app.globalData.apiBase + '/api/QTWorkOrder?user=' + user + '&org=' + org + '&key=' + key ;
    console.log(url);

    let suc = rd => {
      //更新数据
      let data = null;
      let docno = null;
      if(rd.length > 0){
        data = rd[0];
        //质量系统字段
        //data.fQuantity = 0;//交验总数
        data.fUnQual = 0;//不合格数
        data.fPercentPass = 1;//合格率,传质量系统需乘100
        data.cHandle = null;//处置方式
        data.cPipelineCode = null;//流水线编码
        data.cPipelineName = null;//流水线名称

        data.lines = new Array();//不合格项目明细 

        docno = data.docNo;
      }

      //全局变量
      app.globalData.curinspect = data;
      

      let follow = ()=>{
        //隐藏加载动画,停止下拉刷新
        wx.hideNavigationBarLoading();
        wx.stopPullDownRefresh();
        wx.hideLoading();
        that.data.isLoading = false;

        if (callBack)
          callBack();
      }

      that.getTempData(user,'组装',docno,follow);
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        dataMain: null,
        dataLine: [],
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      wx.hideLoading();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });    
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //扫码按钮
  scanCode: function () {
    let that = this;

    wx.scanCode({
      onlyFromCamera: true,
      success (res) {
        console.log(res)
        that.setData({ searchKey: res.result});

        that.getRequestData(res.result, that.goTop);
      }
    });

  },
  //搜索框输入完成
  confirmKeyInput: function (e) {
    console.log(e.detail);
    this.data.searchKey = e.detail.value;

    this.getRequestData(this.data.searchKey, this.goTop);
  },
  //滚动条回到顶部
  goTop: function(){
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //搜索文本改变事件
  bindKeyInput: function (e) {
    console.log(e.detail);
    
    this.data.searchKey = e.detail.value;
  },
  //下拉刷新
  onPullDownRefresh: function () {
    let key = this.data.searchKey;
    console.log(key);

    this.getRequestData(key);
  },
  //选择流水线
  pipePickerChange: function(e) {
    console.log('流水线：',e.detail.value);

    let main = app.globalData.curinspect;
    if(main){
      if(this.data.pipePicker && this.data.pipePicker.length>0){
        main.cPipelineCode = this.data.pipePicker[e.detail.value].cLineCode;
        main.cPipelineName = this.data.pipePicker[e.detail.value].cLineName;
      }
      else{
        main.cPipelineCode = null;
        main.cPipelineName = null;
      }
    }

    this.data.dataMain = main;
    this.setData({
      pipeIndex: e.detail.value
    })
  },
  //选择处置方式
  handlePickerChange: function(e) {
    console.log('处置方式：',e.detail.value);

    let main = app.globalData.curinspect;
    if(main){
      if(e.detail.value>=0 && this.data.handlePicker && this.data.handlePicker.length>0)
        main.cHandle = this.data.handlePicker[e.detail.value].cItemCode;
      else
        main.cHandle = null;
    }

    this.data.dataMain = main;
    this.setData({
      handleIndex: e.detail.value
    })
  },
  //选择检验类型
  typePickerChange: function(e) {
    console.log('检验类型',e.detail.value);

    let main = app.globalData.curinspect;
    if(main){
      if(e.detail.value>=0 && this.data.typePicker && this.data.typePicker.length>0)
        main.cVouchType = this.data.typePicker[e.detail.value].code;
      else
        main.cVouchType = null;
    }

    this.data.dataMain = main;
    this.setData({
      typeIndex: e.detail.value
    })
  },
  //交验总数数值变化
  onQtyTotalChange: function(e){
    console.log('交验总数：',e.detail);

    let main = app.globalData.curinspect;
    if(main){
      main.fQuantity = e.detail;
      //合格率计算
      if(main.fQuantity && main.fUnQual)
        main.fPercentPass = 1-main.fUnQual/main.fQuantity;
      else
        main.fPercentPass = 1;

      this.setData({ dataMain: main });
    }
  },
  //选择不合格项目
  addUnQuaItem: function(e){
    if(this.data.dataMain && this.data.dataMain.hasSubmit)
      return;

    let doc = app.globalData.curinspect;

    console.log('全局变量curinspect跳转',doc)

    if(doc){
      wx.navigateTo({
        url: 'qt-assunqitem/qt-assunqitem?docno=' + doc.docNo
      });
    }
  },
  //更新信息
  updateAssData:function(){

    //全局变量
    let main = app.globalData.curinspect;
    let lines = [];
    if(app.globalData.curinspect && app.globalData.curinspect.lines)
      lines = app.globalData.curinspect.lines;
    
    //设置下拉选项
    let pipeIdx = -1;
    let handleIdx = -1;
    let typeIdx = -1;
    if(main){
      if(main.fUnQual<=0){
        main.cHandle = '';
      }
      //处置方式
      if(this.data.handlePicker && this.data.handlePicker.length>0){
        for(let m=0; m<this.data.handlePicker.length; m++){
          if(this.data.handlePicker[m].cItemCode == main.cHandle){
            handleIdx = m;
            break;
          }
        }
      }
      //流水线
      if(this.data.pipePicker && this.data.pipePicker.length>0){
        for(let n=0; n<this.data.pipePicker.length; n++){
          if(this.data.pipePicker[n].cLineCode == main.cPipelineCode){
            pipeIdx = n;
            break;
          }
        }
      }
      //检验类型
      if(this.data.typePicker && this.data.typePicker.length>0){
        for(let t=0; t<this.data.typePicker.length; t++){
          if(this.data.typePicker[t].code == main.cVouchType){
            typeIdx = t;
            break;
          }
        }
      }
    }

    console.log('刷新全局变量curinspect',main);

    this.setData({
      typeIndex: typeIdx,
      pipeIndex: pipeIdx,
      handleIndex: handleIdx,
      dataMain: main,
      dataLine: lines
    });
  },
  //不合格项目数量变化
  onQtyChange: function(e){
    console.log('不合格项目',e.currentTarget.dataset.itemIndex,e.detail);

    console.log('不合格项目',e.currentTarget.dataset.itemIndex,e.detail);

    let idx = e.currentTarget.dataset.itemIndex;
    let qty = e.detail;

    //全局变量
    let main = app.globalData.curinspect;
    let lines = app.globalData.curinspect.lines;

    lines[idx].nQty = qty;
    //不合格总数
    let qtyUnQua = 0;
    for(let i=0; i<lines.length; i++){
      qtyUnQua = qtyUnQua + lines[i].nQty;
    }
    main.fUnQual = qtyUnQua;
    //合格率计算
    if(main.fUnQual && main.fQuantity)
      main.fPercentPass = 1-main.fUnQual/main.fQuantity;
    else
      main.fPercentPass = 1;
    
    //处置方式
    let handleIdx = this.data.handleIndex;
    if(main.fUnQual<=0){
      handleIdx = -1;
      main.cHandle = "";
    }


    this.setData({ 
      dataMain: main,
      dataLine: lines,
      handleIndex: handleIdx
    });
  },
  //保存按钮事件
  savaData:function() {
    this.saveDoc(true);
  },
  saveDoc: function(isShow){
    if(!this.data.dataMain || !this.data.dataMain.docNo){
      return;
    }
    if(this.data.dataMain.hasSubmit)
      return;
    wx.showLoading({ title: '正在保存...', mask: true });

    let main = this.data.dataMain;
    let lines = this.data.dataLine;

    main.cMenderCode = this.data.user.code;
    main.cMender = this.data.user.name;
    main.dModifyDate = formatTime(new Date());
    //全局变量
    app.globalData.curinspect = main;
    if(lines)
      app.globalData.curinspect.lines = lines;
    else
      app.globalData.curinspect.lines = [];

    //保存缓存
    //wx.setStorageSync('inspect_ass',app.globalData.curinspect);
    //console.log('inspect_ass保存',app.globalData.curinspect);

    //临时保存至数据库
    let ins = {};
    ins.type = '组装';
    ins.orgCode = main.orgCode;
    ins.docNo = main.docNo;
    ins.cVouchType = main.cVouchType;
    ins.fQuantity = main.fQuantity;
    ins.fInduWaste = main.fInduWaste;
    ins.fMateWaste = main.fMateWaste;
    ins.fUnQual = main.fUnQual;
    ins.cPipelineCode = main.cPipelineCode;
    ins.cPipelineName = main.cPipelineName;
    ins.cHandle = main.cHandle;
    ins.cMenderCode = main.cMenderCode;
    ins.cMender = main.cMender;
    ins.dModifyDate = main.dModifyDate;

    let dto = {};
    dto.ins = ins;

    dto.stands=[];

    dto.lines=[];
    for(let i=0; i<lines.length; i++){
      let ln = {};
      ln.cItemCode = lines[i].cItemCode;
      ln.cItemName = lines[i].cItemName;
      ln.nQty = lines[i].nQty;
      ln.nQtyIndu = lines[i].nQtyIndu;
      ln.nQtyMate = lines[i].nQtyMate;
      ln.fPayPer = lines[i].fPayPer;
      ln.fPayAmount = lines[i].fPayAmount;
      ln.cItemQty = lines[i].cItemQty;
      ln.cReason = lines[i].cReason;
      ln.cOperatorName = lines[i].cOperatorName;
      ln.cProcess = lines[i].cProcess;

      dto.lines.push(ln);
    }

    let that = this;
    let url = app.globalData.apiBase + '/api/QTBasicInfo';
    console.log(url);

    let suc = rd => {
      
      wx.hideLoading();
      if(rd!='保存成功'){
        wx.showToast({title: rd,icon: 'none',duration: 2000})
      }
      else if(isShow){
        wx.showToast({title: '保存成功',icon: 'success',duration: 1500})
      }
    };

    let fl = () => {
      wx.hideLoading();
      wx.showToast({title: '保存失败',icon: 'none',duration: 2000})
    };

    http(url, 'POST', dto , suc, fl);

  },
  //提交
  submitToSev:function(){
    let main = this.data.dataMain;
    let lines = this.data.dataLine;
    //保存
    this.saveDoc(false);

    wx.showLoading({ title: '正在提交...', mask: true });
    let that = this;
    let url = app.globalData.apiBase + '/api/QTAssInspect';
    console.log(url);

    let suc = rd => {

      if(rd == '提交成功'){
        //提交时间
        main.hasSubmit = true;
        that.setData({dataMain:main});
        //清除缓存
        //wx.setStorageSync('inspect_ass',null);
      }
      
      wx.hideLoading();
      wx.showToast({title: rd,icon: 'none',duration: 2000})
    };

    let fl = () => {
      wx.hideLoading();
      wx.showToast({title: '提交失败',icon: 'none',duration: 2000})
    };

    http(url, 'POST', app.globalData.curinspect , suc, fl);
  },
  //提交按钮事件
  submitDoc: function(e){
    if(!this.data.dataMain || !this.data.dataMain.docNo){
      return;
    }
    if(this.data.dataMain.hasSubmit)
      return;
    //校验
    let main = this.data.dataMain;
    let lines = this.data.dataLine;
    console.log(main)
    if(!main.fQuantity){
      wuxToast('交验总数不能为空,请调整');
      return;
    }
    if(main.fQuantity > main.productQty){
      wuxToast('交验总数不能超过生产订单的计划数量,请调整');
      return;
    }
    if(main.fUnQual > main.fQuantity){
      wuxToast('不合格数不能超过交验总数,请调整');
      return;
    }
    if(!main.cVouchType){
      wuxToast('检验类型不能为空,请调整');
      return;
    }    
    if(!main.cPipelineCode){
      wuxToast('流水线不能为空,请调整');
      return;
    }
    for(let i=0;i<lines.length;i++){
      if(lines[i].nQty <=0){
        wuxToast('不合格项目明细的数量必须大于0,请调整');
        return;
      }
      else if(lines[i].cVouchType !=main.cVouchType){
        wuxToast('存在多种类型的不合格项目,请调整');
        return;
      }
    }

    let that = this;
    wx.showModal({
      title: '提示',
      content: '是否确定提交?请检查数据是否输入无误',
      confirmText: '是',
      cancelText: '否',
      success (res) {
        if (res.confirm) {
          that.submitToSev();
        } 
        else if (res.cancel) {
        }
      }
    });
    
  },
  //编辑按钮事件
  editDoc: function(e){
    if(this.data.dataMain && this.data.dataMain.hasSubmit)
      return;

    console.log(e.currentTarget.dataset);

    let docno = this.data.dataMain.docNo;
    let itemtype = this.data.dataMain.cVouchType;
  
    let item = e.currentTarget.dataset.itemIndex;
    let itemcode = this.data.dataLine[item].cItemCode;
    let itemname = this.data.dataLine[item].cItemName;
  
    wx.navigateTo({
      url: 'qt-assunqdetail/qt-assunqdetail?docno=' + docno + '&itemtype=' + itemtype
                           + '&itemcode=' + itemcode + '&itemname=' + itemname
    });

  },
  //删除按钮事件
  deleteDoc: function(e){
    if(this.data.dataMain && this.data.dataMain.hasSubmit)
      return;    
    console.log(e.currentTarget.dataset);

    let idx = e.currentTarget.dataset.itemIndex;
    let main = this.data.dataMain;
    let lines = this.data.dataLine;

    lines.splice(idx,1);

    //不合格总数
    let qtyUnQua = 0;
    for(let i=0; i<lines.length; i++){
      qtyUnQua = qtyUnQua + lines[i].nQty;
    }
    main.fUnQual = qtyUnQua;
    //合格率计算
    if(main.fUnQual && main.fQuantity)
      main.fPercentPass = 1-main.fUnQual/main.fQuantity;
    else
      main.fPercentPass = 1;
    
    //处置方式
    let handleIdx = this.data.handleIndex;
    if(main.fUnQual<=0){
      handleIdx = -1;
      main.cHandle = '';
    }

    this.setData({ 
      dataMain: main,
      dataLine: lines,
      handleIndex: handleIdx
    });
  },
  // ListTouch触摸开始
  ListTouchStart(e) {
    if(this.data.dataMain && this.data.dataMain.hasSubmit)
      return;  

    this.setData({
      ListTouchStart: e.touches[0].pageX
    })
  },

  // ListTouch计算方向
  ListTouchMove(e) {
    if(this.data.dataMain && this.data.dataMain.hasSubmit)
      return;  

    this.setData({
      ListTouchDirection: e.touches[0].pageX - this.data.ListTouchStart > 0 ? 'right' : 'left'
    })
  },

  // ListTouch计算滚动
  ListTouchEnd(e) {
    if(this.data.dataMain && this.data.dataMain.hasSubmit)
      return; 

    if (this.data.ListTouchDirection =='left'){
      this.setData({
        modalName: e.currentTarget.dataset.target
      })
    } else {
      this.setData({
        modalName: null
      })
    }
    this.setData({
      ListTouchDirection: null
    })
  },
  
})