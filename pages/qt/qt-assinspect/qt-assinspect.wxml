<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="输入工单" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="scanCode" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">扫码</button>
    </view>
  </view>
</view>

<view class="bg-white">
  <view class="cust-row">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth4">生产订单:</view>
      <text selectable="true" class="sg-content">{{dataMain.moDocNo}}</text>
    </view>
    <view class="cust-col">
      <view class="sg-label sg-mwidth4">客户简称:</view>
      <text selectable="true" class="sg-content">{{dataMain.moCust}}</text>
    </view>
  </view>
  <view class="cust-row">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth2">车间:</view>
      <text selectable="true" class="sg-content">{{dataMain.deptName}}</text>
    </view>
    <view class="cust-col">
      <view class="sg-label sg-mwidth4">计划数量:</view>
      <view class="sg-content">{{dataMain.productQty}}</view>   
    </view>
  </view>
  <view class="cust-row">
    <view class="cust-col sg-box-sm">
      <view class="sg-label sg-mwidth4">产品编码:</view>
      <text selectable="true" class="sg-content">{{dataMain.cMateCode}}</text>
    </view>
    <view class="cust-col">
      <view class="sg-label sg-mwidth4">产品名称:</view>
      <text selectable="true" class="sg-content">{{dataMain.cMateName}}</text>
    </view>
  </view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth4">规格型号:</view>
    <text selectable="true" class="sg-content">{{dataMain.cMateStd}}</text>
  </view>
</view>

<view wx:if="{{!isError}}">

  <view wx:if="{{dataMain.docNo}}">
    <view class="cu-bar bg-white solid-bottom margin-top-xs" style="min-height: 70rpx;">
      <view class="action">
        <text class="cuIcon-titles text-shimge"></text>
        <text class="text-df text-bold">交验信息</text>
      </view>
    </view>
    <view class="cu-form-group">
      <view class="title">检验类型:</view>
      <view wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{typePicker.length>0 && typeIndex>=0 ? typePicker[typeIndex].text : ''}}</view>
      <picker wx:else disabled="{{dataLine && dataLine.length>0}}" bindchange="typePickerChange" value="{{typeIndex}}" range="{{typePicker}}" range-key="text">
        <view class="picker {{typePicker.length>0 && typeIndex>=0 ? '' : 'sg-placeholder'}}">
        {{typePicker.length>0 && typeIndex>=0 ? typePicker[typeIndex].text : '请选择'}}
        </view>
      </picker>
    </view>    
    <view class="cu-form-group">
      <view class="title">流水线:</view>
      <view wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{pipePicker.length>0 && pipeIndex>=0 ? pipePicker[pipeIndex].cLineName : ''}}</view>
      <picker wx:else bindchange="pipePickerChange" value="{{pipeIndex}}" range="{{pipePicker}}" range-key="cLineName">
        <view class="picker {{pipePicker.length>0 && pipeIndex>=0 ? '' : 'sg-placeholder'}}">
        {{pipePicker.length>0 && pipeIndex>=0 ? pipePicker[pipeIndex].cLineName : '请选择'}}
        </view>
      </picker>
    </view>
    <view class="cu-form-group">
      <view class="title">交验总数:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{dataMain.fQuantity}}</text>
      <van-stepper wx:else input-width="5em" min="0" value="{{dataMain.fQuantity}}" bind:change="onQtyTotalChange" />
    </view>
    <view class="cu-form-group">
      <view class="title">不合格数:</view>
      <text class="sg-content">{{dataMain.fUnQual}}</text>
    </view>    
    <view class="cu-form-group">
      <view class="title">合格率:</view>
      <text class="sg-content">{{ filter.formatPercent(dataMain.fPercentPass,2) }}</text>
    </view>    
    <view class="cu-form-group">
      <view class="title">处置方式:</view>
      <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{handlePicker.length>0 && handleIndex>=0 ? handlePicker[handleIndex].cItemName : ''}}</text>
      <picker wx:else disabled="{{dataMain.fUnQual<=0}}" bindchange="handlePickerChange" value="{{handleIndex}}" range="{{handlePicker}}" range-key="cItemName">
        <view class="picker {{handlePicker.length>0 && handleIndex>=0 ? '' : 'sg-placeholder'}}">
          {{handlePicker.length>0 && handleIndex>=0 ? handlePicker[handleIndex].cItemName : '请选择'}}
        </view>
      </picker>
    </view>   

    <view class="cu-bar bg-white margin-top-xs solid-bottom" style="min-height: 70rpx;">
      <view class="action">
        <text class="cuIcon-titles text-shimge"></text>
        <text class="text-df text-bold">不合格项目信息</text>
      </view>
      <view class="action">
        <button wx:if="{{!dataMain.hasSubmit}}" class="cu-btn sm bg-shimge shadow" bindtap="addUnQuaItem">选择</button>
      </view>    
    </view>
    <view class="cu-list menu-avatar">
      <view class="cu-item {{modalName=='move-box-'+ index?'move-cur':''}}" wx:for="{{dataLine}}" wx:for-item="item" wx:key="itemCode" bindtouchstart="ListTouchStart" bindtouchmove="ListTouchMove" bindtouchend="ListTouchEnd" data-target="move-box-{{index}}">
        <view class="cu-form-group">
          <view class="title">{{item.cItemName}}:</view>
          <text wx:if="{{dataMain.hasSubmit}}" class="sg-content">{{item.nQty}}</text>
          <van-stepper wx:else input-width="3em" min="0" value="{{item.nQty}}" data-item-index="{{index}}" bind:change="onQtyChange" />
        </view>  
        <view class="move">
          <view class="bg-green" data-item-index="{{index}}" bindtap="editDoc">编辑</view>
          <view class="bg-red" data-item-index="{{index}}" bindtap="deleteDoc">删除</view>
        </view>      
      </view>
    </view>
  </view>
  
</view>
<view class="cu-load bg-red erro" wx:else></view> 

<view class="cu-bar tabbar border foot bg-shimge" disabled="{{isLoading}}" >
  <view class="submit solids-left" bindtap="savaData">保存</view>
  <view class="submit solids-left" bindtap="submitDoc">提交</view>
</view> 
