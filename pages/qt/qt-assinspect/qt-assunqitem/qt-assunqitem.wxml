<wxs module="filter" src="../../../../utils/filter.wxs"></wxs>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="输入项目" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>
</view>

<van-tree-select items="{{items}}" height=" {{(ScreenHeight-95-90)}}rpx" main-active-index="{{ mainActiveIndex }}" bind:click-nav="onClickNav">
  <view slot="content" class="cu-list menu-avatar">
    <block wx:for="{{dataList}}" wx:for-index="idx" wx:for-item="item" wx:key="cItemCode">
      <view class="cu-item">
        <view class="textcontent">
          <view class="text-black text-bold">
            <text class="text-cut">{{item.cItemName}}</text>
          </view>
          <view class="text-gray text-sm flex">
            <text class="text-cut">{{item.cMemo}}</text>
          </view>
        </view>
        <view class="action text-shimge" data-item-index="{{idx}}" bindtap="viewDetails">
          <text>详情</text>
          <text class="cuIcon-right"></text>
        </view>
      </view>
    </block>
  </view>
</van-tree-select>

<view class="cu-bar bg-shimge foot">
  <view class="action">
     不合格数: {{totalUnQua}}
  </view>
  <view class="action" bindtap="sureItem"> 
    <view class="padding-left-lg padding-right-sm solids-left" style="line-height: 90rpx">确定</view>
  </view>
</view>