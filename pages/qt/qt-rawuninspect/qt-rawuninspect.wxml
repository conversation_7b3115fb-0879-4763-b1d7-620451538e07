<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索单号、供应商、料品" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="showModal" class="cu-btn bg-shimge shadow-blur round"  disabled="{{isLoading}}" data-target="DrawerModalR" type="">筛选</button>
    </view>
  </view>
  <scroll-view scroll-x class="nav" scroll-with-animation scroll-left="{{scrollLeft}}">
    <view class="cu-item {{currentItem==1?'cur text-shimge':''}}" data-current-item="1" bindtap="tapcondition">我的待检</view>
    <view class="cu-item {{currentItem==2?'cur text-shimge':''}}" data-current-item="2" bindtap="tapcondition">全部待检</view>
    <view class="cu-item {{currentItem==3?'cur text-shimge':''}}" data-current-item="3" bindtap="tapcondition">我的保存记录</view>
  </scroll-view>
</view>

<view class="bg-white"> 
  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="basis-p25 padding-sm">供应商</view>
    <view class="basis-p18 padding-sm">料号</view>
    <view class="basis-p18 padding-sm">品名</view>
    <view class="basis-p25 padding-sm">规格</view>
    <view class="basis-p14 padding-sm">数量</view>
  </view>
  <block wx:for="{{dataList}}" wx:for-item="item" wx:key="poLine">
    <view class="flex align-center solids-bottom sg-row" bindtap="taprow" data-id="{{item.id}}" data-docno="{{item.cArrivalCode}}">
      <view class="{{item.gradeSupp?'':'text-orange'}} basis-p25 padding-xs text-multcut">{{item.cVenName}}</view>
      <view class="basis-p18 padding-xs text-multcut">{{item.cInvCode}}</view>
      <view class="basis-p18 padding-xs text-multcut">{{item.cInvName}}</view>
      <view class="basis-p25 padding-xs text-multcut">{{item.cInvStd}}</view>
      <view class="basis-p14 padding-xs text-multcut text-center">{{item.qtyTU}}</view>
    </view>
  </block>
  <view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
  <view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>
</view>


<view class="cu-modal drawer-modal justify-end {{modalName=='DrawerModalR'?'show':''}}" bindtap="hideModal">
  <view class="cu-dialog basis-lg" catchtap style="top:{{CustomBar}}px;height:calc(100vh - {{CustomBar}}px)">
    <view class="cu-form-group">
      <view class="title">开始日期</view>
      <picker mode="date" value="{{dateBegin}}"  bindchange="DateBeginChange">
        <view class="picker">
          {{ dateBegin?filter.formatDate(dateBegin):'请选择' }}
        </view>
      </picker>
    </view>
    <view class="cu-form-group">
      <view class="title">结束日期</view>
      <picker mode="date" value="{{dateEnd}}"  bindchange="DateEndChange">
        <view class="picker">
          {{ dateEnd?filter.formatDate(dateEnd):'请选择' }}
        </view>
      </picker>
    </view> 
    <view class="cu-form-group">
      <button class="cu-btn bg-shimge shadow" bindtap="dlgModalOk">确定</button>
      <button class="cu-btn bg-shimge shadow" bindtap="dlgModalCancel">取消</button>
    </view>        
  </view>
</view>