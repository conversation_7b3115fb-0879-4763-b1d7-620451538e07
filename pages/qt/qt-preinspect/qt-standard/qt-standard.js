const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../../../utils/util.js'

Page({
  data: {
    scrollLeft: 0,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    loadState: 0,//加载状态  1:继续下拉加载更多,2:加载中,3:没有更多了
    isError:false,//加载失败
    dataList:[],//标准列表
    bottomId:'',//底部最后一行Id,用于上拉触底事件
    searchKey:'',//搜索关键字
    user:null //当前用户
  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;
    
    this.getRequestData(true);
  },
  //获取后台数据
  getRequestData: function (again, callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    if (again) {
      //重新获取数据
      this.data.bottomId = '';
      this.data.dataList = [];
    }
    else{
      //上拉加载提示
      this.setData({ loadState: 2 });  
    }
    console.log('id:' + this.data.bottomId);
    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.code;
    let key = this.data.searchKey;
    let begin = this.data.bottomId;
    let url = app.globalData.apiBase + '/api/QTInsStandard?key=' + key
            + '&begin=' + begin + '&pagesize=' + pagesize;
    console.log(url);

    let suc = rd => {
      //更新数据
      let totalData = that.data.dataList;
      //判断数据大小是否等于每页数大小，相等则认为还有数据，否则认为没有更多数据
      let hasmore = rd.length == pagesize ? 1 : 0;
      if (rd.length > 0) {
        totalData = totalData.concat(rd);
        that.data.bottomId = rd[rd.length - 1].cNo;
      }

      that.setData({
        loadState: hasmore,
        dataList: totalData
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;

      if (callBack)
        callBack(rd);
    };

    let fl = () => {
      //更新数据
      that.data.bottomId = '';
      that.setData({
        isError: true,
        loadState: 0,
        dataList: []
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false; 
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });     
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading){
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else{
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress>0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //搜索框输入完成
  confirmKeyInput: function(e){
    console.log(e.detail);

    this.data.searchKey = e.detail.value;

    this.getRequestData(true, this.goTop);
  },
  //查询按钮
  searchData: function(){

    this.getRequestData(true, this.goTop);
  },
  //滚动条回到顶部
  goTop: function () {
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //搜索文本改变事件
  bindKeyInput:function(e){
    console.log(e.detail);

    this.data.searchKey = e.detail.value;
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData(true);
  },
  //上拉加载更多
  onReachBottom: function () {
    if (this.data.loadState == 0 ||this.data.loadState == 3)
      return;
    this.getRequestData(false);
  },
  //选择
  tapselect: function (e) {
    let value = e.currentTarget.dataset.itemValue;
    let list = this.data.dataList;
    //勾选
    for (let i = 0; i < list.length; i++) {
      list[i].checked = list[i].cNo == value;
    } 

    this.setData({dataList:list});
  },
  //获取检验标准
  getStandardLine: function(type,typeitem,callBack){
    let that = this;
    let org = this.data.user.org;

    let url = app.globalData.apiBase + '/api/QTStandardItems?type='+type ;
    console.log(url);

    let suc = rd => {
      if(rd){
        for(let i=0;i<rd.length;i++){
          rd[i].cStandCode = typeitem.cNo;
          rd[i].cStandName = typeitem.cName;
          rd[i].cStandID = rd[i].autoID;
        }
      }
      if(callBack)
        callBack(rd);
    };
    http(url, 'GET', null, suc, null);
  },
  //确定
  sureselect: function(){
    let idx = -1;
    let list = this.data.dataList;
    for (let i = 0; i < list.length; i++) {
      if(list[i].checked){
        idx = i;
        break;
      }
    }
    //返回当前选择
    if(idx>=0){

      let back = rd => {
        //获取上一页,刷新不合格项目
        let pages = getCurrentPages(); //获取当前页面栈
        let prevPage = pages[pages.length - 2]; //上一页
        
        prevPage.updateStandard(list[idx], rd);
        //返回上一页
        wx.navigateBack({delta: 1});
      };

      this.getStandardLine(list[idx].id,list[idx], back);
    }

  },
})