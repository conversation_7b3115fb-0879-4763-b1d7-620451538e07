<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索标准" value="{{searchKey}}" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>
</view>

<view class="bg-white"> 
  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="basis-p100 padding-sm">检验标准</view>
  </view>
  <block wx:for="{{dataList}}" wx:for-item="item" wx:key="cNo">
    <view class="flex align-center padding-lr solids-bottom sg-row" data-item-value="{{item.cNo}}" bindtap="tapselect">
      <view class="basis-p100 padding-xs text-multcut">{{item.cName}}</view>
      <text class="cuIcon-check lg text-shimge" wx:if="{{item.checked}}"></text>
    </view>
  </block>
  <view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>
</view>

<view class="cu-bar tabbar border foot bg-shimge" disabled="{{isLoading}}" >
  <view class="submit" bindtap="sureselect">确定</view>
</view> 