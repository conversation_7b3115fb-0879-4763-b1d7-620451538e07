const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../../../utils/util.js'

Page({
  data: {
    searchKey: '',//搜索关键字
    ScreenHeight: app.globalData.ScreenHeight, //屏幕高度
    mainActiveIndex: 0,
    items:[ ], //分类
    dataList:[], //不合格项目列表
    totalUnQua:0, //不合格数
    docNo:null, //工单号
    docType:null //不合格类型,判断用
  },
  onLoad: function (options) {
  
    this.data.user = app.globalData.accountInfo;

    //工单号
    this.data.docNo = options.docno;

    this.loadType();
  },
  //加载分类
  loadType:function(){
    wx.showNavigationBarLoading();

    let that = this;
    let org = this.data.user.org;

    let url = app.globalData.apiBase + '/api/QTVouchType?doctype=pre' ;
    console.log(url);

    let suc = rd => {
      that.setData({ items: rd });
      that.loadData();

      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
    };

    let fl = () => {
      that.setData({ items: [] });

      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      }); 
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载不合格项目
  loadData:function(){
    //读取全局变量
    let cur = app.globalData.curinspect;

    console.log('选择不合格项目',cur.cVouchType);
    //设置对应的不合格类型
    let index = 0;
    if(cur && cur.cVouchType){
      for(let i=0; i<this.data.items.length; i++){
        if(this.data.items[i].code == cur.cVouchType){
          index = i;
          break;
        }
      }
    }
    this.setData({
      mainActiveIndex: index,
      totalUnQua: cur.fUnQual,
    });

    let type = this.data.items[this.data.mainActiveIndex].code;
    this.getBasicInfo(type,'');
  },
  //搜索文本改变事件
  bindKeyInput:function(e){
    console.log(e.detail);

    this.data.searchKey = e.detail.value;
  },
  //搜索框输入完成
  confirmKeyInput: function(e){
    console.log(e.detail);

    this.data.searchKey = e.detail.value;

    this.searchData();
  },
  //搜索
  searchData:function(){
    let key = this.data.searchKey;

    let cur = this.data.mainActiveIndex;
    let type = this.data.items[cur].code;
    this.getBasicInfo(type,key);
  },
  //获取基础档案
  getBasicInfo: function(kind,key){

    wx.showNavigationBarLoading();
    wx.showLoading({ title: '加载中...', mask:true});

    let that = this;
    let org = this.data.user.org;

    let url = app.globalData.apiBase + '/api/QTUnQualItem?kind='+kind + '&key='+key ;
    console.log(url);

    let suc = rd => {
      that.data.dataList = rd;
      that.updateAllItemInfo();

      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.hideLoading();
    };

    let fl = () => {
      that.setData({ dataList: [] });

      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.hideLoading();
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      }); 
    };

    http(url, 'GET', null, suc, fl);
 },

 //更新已选择的不合格项目信息
 updateAllItemInfo:function(){
  let qty = 0;
  let type = null;
  let dataList = this.data.dataList;
  //读取全局变量
  let main = app.globalData.curinspect;
  let lines = [];
  if(app.globalData.curinspect.lines)
    lines = app.globalData.curinspect.lines;

  //转换为字典类型
  let rdmap = new Map();
  for (let m in lines) {
    rdmap.set(lines[m].cItemCode, lines[m]);
    qty = qty + lines[m].nQty;
    type = lines[m].cVouchType;
  }
  //备注
  for(let i=0; i<dataList.length; i++){
    let code = dataList[i].cItemCode;
    if( rdmap.has(code) ){
      let line = rdmap.get(code);
      let cMemo = '';
      if(type == '05')
        cMemo = '不合格数:' + line.nQty;
      else{
        if(line.nQtyIndu)
          cMemo = '工废:' + line.nQtyIndu;
        if(line.nQtyMate){
          if(cMemo.length > 0)
            cMemo = cMemo + ';'
          cMemo = cMemo + '料废:' + line.nQtyMate;  
        }       
      }
      if(line.fPayPer)
        cMemo = cMemo + ';赔偿比例:' + line.fPayPer + '%';
      if(line.fPayAmount)
        cMemo = cMemo + ';赔偿金额:' + line.fPayAmount;

      dataList[i].cMemo = cMemo;
    }
    else{
      dataList[i].cMemo = '';
    }
  }
  
  this.setData( {dataList: dataList, totalUnQua: qty, docType: type} );

  console.log('变量dataList',dataList)

 },
 //分类选择点击事件
 onClickNav( {detail} ) {

  let idx = detail.index || 0;
  let cur = this.data.mainActiveIndex;

  console.log(this.data.items[idx].text)

  if(idx != cur){
    this.setData({ mainActiveIndex: idx,dataList:[] });

    let type = this.data.items[idx].code;
    this.getBasicInfo(type,'');
  }
 },
 //点击详情
 viewDetails: function(e){
  console.log(e.currentTarget.dataset);

  let docno = this.data.docNo;
  let idx = this.data.mainActiveIndex;
  let itemtype = this.data.items[idx].code;

  let item = e.currentTarget.dataset.itemIndex;
  let itemcode = this.data.dataList[item].cItemCode;
  let itemname = this.data.dataList[item].cItemName;

  //判断不合格类型
  if(this.data.docType && this.data.docType!=itemtype){
    wx.showToast({
      title: '不能选择多种类型的不合格项目,请重新选择',
      icon: 'none',
      duration: 3000
    });
    return;
  }

  wx.navigateTo({
    url: '../qt-preunqdetail/qt-preunqdetail?docno=' + docno + '&itemtype=' + itemtype
                         + '&itemcode=' + itemcode + '&itemname=' + itemname
  });
 },
 //确定按钮事件
 sureItem:function(){
    //获取上一页,刷新不合格项目
    let pages = getCurrentPages(); //获取当前页面栈
    let prevPage = pages[pages.length - 2]; //上一页
    
    prevPage.updateAssData();

    //返回上一页
    wx.navigateBack({delta: 1});
 },
})