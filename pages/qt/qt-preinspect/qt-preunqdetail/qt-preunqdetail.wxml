<wxs module="filter" src="../../../../utils/filter.wxs"></wxs>
<view >
  <view class="cust-row">
    <view class="sg-label sg-mwidth4">产品名称:</view>
    <text selectable="true" class="sg-content">{{itemdetail.cMateName}}</text>  
  </view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth4">规格型号:</view>
    <text selectable="true" class="sg-content">{{itemdetail.cMateStd}}</text>  
  </view>         
  <view class="cust-row" style="border-bottom: 1rpx solid #eee;">
    <view class="sg-label sg-mwidth5">不合格项目:</view>
    <text selectable="true" class="sg-content">{{itemdetail.cItemName}}</text>  
  </view>
	<view wx:if="{{itemdetail.cVouchType!='05'}}" class="cu-form-group">
		<view class="title sg-mwidth4">料废数量:</view>
    <van-stepper input-width="5em" min="0" value="{{itemdetail.nQtyMate}}" bind:change="onChangeQtyMate" />
	</view>
	<view wx:if="{{itemdetail.cVouchType!='05'}}" class="cu-form-group">
		<view class="title sg-mwidth4">工废数量:</view>
    <van-stepper input-width="5em" min="0" value="{{itemdetail.nQtyIndu}}" bind:change="onChangeQtyIndu" />
	</view>                
	<view wx:if="{{itemdetail.cVouchType=='05'}}" class="cu-form-group">
		<view class="title sg-mwidth4">不合格数:</view>
    <van-stepper input-width="5em" min="0" value="{{itemdetail.nQty}}" bind:change="onChangeQty" />
	</view>
	<view class="cu-form-group">
		<view class="title sg-mwidth5">赔偿比例(%):</view>
    <van-stepper input-width="5em" min="0" value="{{itemdetail.fPayPer}}" bind:change="onChangePayPer" />
	</view>
	<view class="cu-form-group">
		<view class="title sg-mwidth4">赔偿金额:</view>
    <van-stepper input-width="5em" min="0" value="{{itemdetail.fPayAmount}}" bind:change="onChangePayAmount" />
	</view>
	<view class="cu-form-group">
		<view class="title sg-mwidth5">不合格原因:</view>
		<textarea auto-height maxlength="200" bindinput="inputReason" value="{{itemdetail.cReason}}" placeholder="请输入不合格原因" ></textarea>
	</view>
	<view class="cu-form-group">
		<view class="title sg-mwidth2">工序:</view>
		<textarea auto-height maxlength="50" bindinput="inputProcess" value="{{itemdetail.cProcess}}" placeholder="请输入工序" ></textarea>
	</view>   
	<view class="cu-form-group">
		<view class="title sg-mwidth3">加工者:</view>
		<textarea auto-height maxlength="50" bindinput="inputOperatorName" value="{{itemdetail.cOperatorName}}" placeholder="请输入加工者" ></textarea>
	</view>    
	<view wx:if="{{itemdetail.cVouchType!='05'}}" class="cu-form-group">
		<view class="title sg-mwidth5">外包供应商:</view>
		<textarea auto-height maxlength="100" bindinput="inputSuppName" value="{{itemdetail.cSuppName}}" placeholder="请输入供应商" ></textarea>
	</view>
  <view class="cust-row" style="border-top: 1rpx solid #eee;">
    <view class="sg-label sg-mwidth3">修改人:</view>
    <text selectable="true" class="sg-content">{{itemdetail.cMender}}</text>  
  </view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth4">修改时间:</view>
    <text selectable="true" class="sg-content">{{filter.formatTime(itemdetail.dModifyDate)}}</text>  
  </view>  
  <view class="cu-bar foot bg-shimge" bindtap="onSave">
    <view class="content text-bold">确 定</view>
  </view> 
</view>
