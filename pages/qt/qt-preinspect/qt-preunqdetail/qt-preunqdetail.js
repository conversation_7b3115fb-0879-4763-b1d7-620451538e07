
const app = getApp()
import { wuxToast, http, formatDate, formatTime, updateAccountExpire } from '../../../../utils/util.js'

Page({
  data: {
    user: null, //当前用户
    itemdetail:{}, //不合格详情
    docNo:null, //工单号
    itemCode:null,//不合格项目
    itemName:null,
    itemType:null
  },
  onLoad: function (options) {
 
    this.data.user = app.globalData.accountInfo;
    
    //工单号 不合格项目
    this.data.docNo = options.docno;
    this.data.itemCode = options.itemcode;
    this.data.itemName = options.itemname;
    this.data.itemType = options.itemtype;

    //读取全局变量
    let main = app.globalData.curinspect;
    let lines = [];
    if(app.globalData.curinspect.lines)
      lines = app.globalData.curinspect.lines;
    //不合格项目
    let rd = null;
    for (let m in lines) {
      if(lines[m].cItemCode == this.data.itemCode){
        rd = lines[m];
        break;
      }
    }

    let data = null;
    if(rd){
      //变量深拷贝赋值
      data = JSON.parse(JSON.stringify(rd));
    }
    else{
      //构造
      data = 
      {
        cItemCode: this.data.itemCode,
        cItemName: this.data.itemName,
        cVouchType: this.data.itemType,
        cMateCode: main.cMateCode,
        cMateName: main.cMateName,
        cMateStd: main.cMateStd,
        nQty: 0,
        nQtyIndu: 0,
        nQtyMate: 0,
        cMenderCode: this.data.user.code,
        cMender: this.data.user.name,
        dModifyDate: formatTime(new Date())
      };
    }
    
    console.log('初始变量itemdetail',data)

    this.setData({itemdetail: data});
  }, 
  //料废数量
  onChangeQtyMate(e) {
    console.log('料废数量',e.detail);

    this.data.itemdetail.nQtyMate = e.detail;
    this.data.itemdetail.nQty = this.data.itemdetail.nQtyMate + this.data.itemdetail.nQtyIndu;

    this.setData({itemdetail: this.data.itemdetail});
  },
  //工废数量
  onChangeQtyIndu(e) {
    console.log('工废数量',e.detail);

    this.data.itemdetail.nQtyIndu = e.detail;
    this.data.itemdetail.nQty = this.data.itemdetail.nQtyMate + this.data.itemdetail.nQtyIndu;    

    this.setData({itemdetail: this.data.itemdetail});
  },  
  //不合格数
  onChangeQty(e) {
    console.log('不合格数',e.detail);

    this.data.itemdetail.nQty = e.detail;

    this.setData({itemdetail: this.data.itemdetail});
  },
  //赔偿比例
  onChangePayPer(e) {
    console.log('赔偿比例',e.detail);

    this.data.itemdetail.fPayPer = e.detail;

    this.setData({itemdetail: this.data.itemdetail});
  },
  //赔偿金额
  onChangePayAmount(e) {
    console.log('赔偿金额',e.detail);

    this.data.itemdetail.fPayAmount = e.detail;

    this.setData({itemdetail: this.data.itemdetail});
  },
  //不合格原因  
  inputReason: function (e) {
    console.log('不合格原因',e.detail.value);

    this.data.itemdetail.cReason = e.detail.value;

    this.setData({itemdetail: this.data.itemdetail});
  },
  //加工者  
  inputOperatorName: function (e) {
    console.log('加工者',e.detail.value);

    this.data.itemdetail.cOperatorName = e.detail.value;

    this.setData({itemdetail: this.data.itemdetail});
  },
  //供应商  
  inputSuppName: function (e) {
    console.log('外包供应商',e.detail.value);

    this.data.itemdetail.cSuppName = e.detail.value;

    this.setData({itemdetail: this.data.itemdetail});
  },  
  //工序  
  inputProcess: function (e) {
    console.log('工序',e.detail.value);

    this.data.itemdetail.cProcess = e.detail.value;

    this.setData({itemdetail: this.data.itemdetail});
  },
  //保存确定事件 
  onSave:function(){
    //console.log(this.data.itemdetail);

    if(!this.data.itemdetail.cItemCode){
      wx.showToast({
        title: '不合格项目不能为空',
        icon: 'none',
        duration: 3000
      });
      return;
    }
    let that = this;

    that.saveItem();
  },
  //取字符串的结尾
  getLastStr:function(str){
    var arr =  str.split('/');
    if(arr && arr.length>0)
      return arr[arr.length-1];
    else
      return '';
  },
  //保存
  saveItem:function(){

    wx.showLoading({ title: '正在处理中...', mask: true });

    //全局变量
    let main = app.globalData.curinspect;
    let lines = [];
    if(app.globalData.curinspect.lines)
      lines = app.globalData.curinspect.lines;

    main.cMenderCode = this.data.user.code;
    main.cMender = this.data.user.name;
    main.dModifyDate = formatTime(new Date());

    //删除原列表中的不合格项目
    for(let i=lines.length-1; i>=0; i--){
      if(lines[i].cItemCode == this.data.itemdetail.cItemCode){
        lines.splice(i,1);
        break;
      }
    }
    //增加不合格项目明细
    //深拷贝
    let l = JSON.parse(JSON.stringify(this.data.itemdetail));
    lines.push(l);
    main.cVouchType = this.data.itemdetail.cVouchType;
  
    //不合格总数
    let qtyUnQua = 0;
    let qtyIndu = 0;
    let qtyMate = 0;
    for(let i=0; i<lines.length; i++){
      qtyIndu = qtyIndu + lines[i].nQtyIndu;
      qtyMate = qtyMate + lines[i].nQtyMate;      
      qtyUnQua = qtyUnQua + lines[i].nQty;
    }
    main.fInduWaste = qtyIndu;
    main.fMateWaste = qtyMate;
    main.fUnQual = qtyUnQua;
    //合格率计算
    if(main.fUnQual && main.fQuantity)
      main.fPercentPass = 1-main.fUnQual/main.fQuantity;
    else
      main.fPercentPass = 1;

    //排序
    lines = lines.sort(function(m, n){ return m.cItemCode<n.cItemCode ?-1:1 }); 

    //wx.setStorageSync('inspect_pre',main);
    //console.log('inspect_pre保存',main);

    //获取上一页,刷新备注
    let pages = getCurrentPages(); //获取当前页面栈
    let prevPage = pages[pages.length - 2]; //上一页

    if(this.getLastStr(prevPage.route) == 'qt-preunqitem')
      prevPage.updateAllItemInfo();
    else if(this.getLastStr(prevPage.route) == 'qt-preinspect')
      prevPage.updateAssData();

    //获取上上页
    let prevPage2 = pages[pages.length - 3];
    if(prevPage2 && this.getLastStr(prevPage2.route) == 'qt-preinspect')
      prevPage2.updateAssData();

    wx.hideLoading();

    //wx.showToast({title: '保存成功',icon: 'success',duration: 1500})

    ////返回上一页
    //wx.navigateBack({delta: 1});
  }, 
})