
const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../../utils/util.js'

Page({
  data: {

  },
  //加载事件
  onLoad: function (options) {
    this.setData({
      account: options.account
    });

    console.log(this.data.account)
  },
  //按钮事件
  formSubmit: function(e) {
    console.log(e.detail.value);

    let that = this;
    let account = this.data.account;
    let oldpwd = e.detail.value.oldpwd;
    let newpwd = e.detail.value.newpwd;
    let surepwd = e.detail.value.surepwd;

    if(newpwd!=surepwd){
      wuxToast('新密码和确认密码不一致,请调整');
      return;
    }

    wx.showLoading({title: '处理中',mask: true});
    //修改密码
    let url = app.globalData.apiBase + '/api/WKPassword';
    let data = {
      userAccount: '01\\'+account,  //内部系统编码为01
      oldPassword: oldpwd,
      newPassword: newpwd,
      surePassword: surepwd,
    };

    let suc = rd => {
      wx.hideLoading();
      
      if(rd=='设置成功'){
        //返回上一页
        wx.navigateBack({delta: 1})
      }
      else{
        wuxToast(rd);
      }
    };

    let fl = () =>{
      wx.hideLoading();
      wuxToast('登录失败,请检查网络是否正常');
    };

    http(url, 'POST', data, suc, fl);
  },  
})
