
//获取应用实例
const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../utils/util.js'

Page({
  data: {
    rePwd:true //记住密码
  },
  //登录按钮事件
  formSubmit: function(e) {
    console.log(e.detail.value);

    let that = this;
    let usercode = e.detail.value.code;
    let password = e.detail.value.password;

    //重置帐号信息
    app.globalData.accountInfo = null;
    wx.setStorageSync('accountInfo', app.globalData.accountInfo);

    if (!usercode){
      wuxToast('请输入手机号码');
      return;
    }

    wx.showLoading({title: '登录中',mask: true});

    //登录校验
    let url = app.globalData.apiBase + '/api/WKLogin';
    let data = {
      userAccount: '01\\'+usercode,  //内部系统编码为01
      code: usercode,
      password: password
    };

    let suc = rd => {
      //记住登录账号
      wx.setStorageSync('worker', usercode);
      wx.setStorageSync('workerpwd', null);

      if (rd.userName) {
        //记住密码
        if(that.data.rePwd)
          wx.setStorageSync('workerpwd', password);

        //获取用户权限
        that.getAccountInfo(rd.userName, password);
      }
      else {
        wx.hideLoading();
        wuxToast(rd.message ? rd.message : '登录失败');
      }
    };

    let fl = () =>{
      wx.hideLoading();
      wuxToast('登录失败,请检查网络是否正常');
    };

    http(url, 'POST', data, suc, fl);
  },
  //获取用户权限后登录
  getAccountInfo: function(user,pwd){

    //获取权限
    let url = app.globalData.apiBase + '/api/Login?type=worker&org=&user=' + user ;
    console.log(url);
    let suc = rd =>{

      if (rd.org && rd.menuList && rd.menuList.length > 0){
        app.globalData.accountInfo = rd;
        //设置当前登录组织
        app.globalData.accountInfo.org = rd.org;
        //记住当前用户信息
        wx.setStorageSync('accountInfo', app.globalData.accountInfo);

        wx.hideLoading();
        wx.reLaunch({ url: '../index/index' });
      }
      else{
        wx.hideLoading();
        wuxToast('登录失败,无该员工信息');
      }
    };

    let fl = () => {
      wx.hideLoading();
      wuxToast('登录失败');
    };

    http(url, 'GET', null, suc, fl);
  },
  
  //加载事件
  onLoad: function () {
    //登录账号
    let account = wx.getStorageSync('worker');
    let pwd = wx.getStorageSync('workerpwd');
    let re = true;

    if(pwd == null || pwd == undefined){
      pwd = '';
      re = false;
    }

    this.setData({
      account: account,
      accountPwd: pwd,
      rePwd: re
    });

  },
  //转发分享功能
  onShareAppMessage: function () {

  },
  //设置密码
  onPassword: function(){
    if(this.data.account){
      wx.navigateTo({
        url: 'wkpassword/wkpassword?account='+this.data.account
      });
    }
  },
  //文本改变事件
  bindKeyInput:function(e){

    this.data.account = e.detail.value;
  },
 //文本输入完成
 confirmKeyInput: function(e){
  console.log(e.detail);

  this.data.account = e.detail.value;
 },
 //记住密码 
 checkboxChange:function(e){
   console.log(e.detail.value);

   let values = e.detail.value;
   let rePwd = values!=null && values.length>0;
   this.setData({rePwd: rePwd});
 },   
})
