const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../../../utils/util.js'

Page({
  data: {
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    isError: false,//加载失败    
    executeLine: {},//生产订单异常主表
    id: null, //订单主键
    user: null //当前用户
  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;
    this.data.id = options.id;

    this.getRequestData(this.data.id);
  },

  //获取后台数据
  getRequestData: function (id) {
    if(!id)
      return;
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    //隐藏加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let org = this.data.user.org;
    let user = this.data.user.code;
    let url = app.globalData.apiBase + '/api/MODelivery/' + id + '/' + user + '/' + org;

    console.log(url);

    let suc = rd => {
      //更新数据
      let rows = [];
      if (rd.length > 0) {
        rows = rd;
      }
      that.setData({
        executeLine: rows
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        executeLine: []
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });
    };

    http(url, 'GET', null, suc, fl);
  },  
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  }, 
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData(this.data.id);
  },  
})