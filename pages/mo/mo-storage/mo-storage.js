// pages/mo/mo-storage/mo-storage.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    Check301:false,
    Check303:false,
    Check304:false,
    Check206:false,
    Check208:false,
    CheckOrg:'',//最后组织
    loadState:0,//加载状态  1:加载中,2:没有更多了
    pagenum:0,
    pagesize:50,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    inputvalue:'',//搜索字段
    postList:{},//数组信息
    SQty:'(0台)'
  },
  //查询按钮及input框确认
  searchData:function(){
    //查询按钮先失效，防止多次点击
    this.setData({
      postList: {},
      loadState:0,
      pagenum: 0,//分页记录数
      pagesize: 50,//分页大小
      loadProgress:0,
      SQty:'(0台)'
    })
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条
    this.GetCheckOrg();//获取事业部

    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'Storage',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        inputvalue:that.data.inputvalue,
        Org:that.data.CheckOrg
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '没有数据！',
            icon: 'none',
            duration: 2000
          })
          that.setData({
          isLoading:false //按钮启用
          })
        }else{
        that.setData({
          postList:res.data
        })
    //获取总计台数
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'StorageCount',
        inputvalue:that.data.inputvalue,
        Org:that.data.CheckOrg
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          SQty:res.data[0][0],
          isLoading:false //按钮启用
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //获取总计台数结束
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.searchData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    //查询按钮先失效，防止多次点击
    this.setData({
      postList: {},
      loadState:0,
      pagenum: 0,//分页记录数
      pagesize: 50,//分页大小
      loadProgress:0,
      SQty:'(0台)'
    })
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条
    //隐藏加载动画,停止下拉刷新
    wx.hideNavigationBarLoading();
    wx.stopPullDownRefresh();

    this.GetCheckOrg();//获取事业部

    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'Storage',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        inputvalue:that.data.inputvalue,
        Org:that.data.CheckOrg
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '没有数据！',
            icon: 'none',
            duration: 2000
          })
          that.setData({
          isLoading:false //按钮启用
          })
        }else{
        that.setData({
          postList:res.data
        })
    //获取总计台数
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'StorageCount',
        inputvalue:that.data.inputvalue,
        Org:that.data.CheckOrg
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          SQty:res.data[0][0],
          isLoading:false //按钮启用
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //获取总计台数结束
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('加载更多');
    this.setData({
      loadState:1,
      pagenum: this.data.pagenum+1,
      loadProgress:0,
      SQty:'(0台)'
    })
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条
    this.GetCheckOrg();//获取事业部

    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'Storage',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        inputvalue:that.data.inputvalue,
        Org:that.data.CheckOrg
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '暂无更多数据!',
            icon: 'none',
            duration: 2000
          })
          that.setData({
            loadState:2,
            pagenum:that.data.pagenum-1,
            isLoading:false //按钮启用
          })
        }else{
          wx: wx.showToast({
            title: '数据加载完毕!',
            icon: 'none',
            duration: 2000
          }),
        that.setData({
          postList:that.data.postList.concat(res.data),
          loadState:0,
          isLoading:false //按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //获取总计台数
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'StorageCount',
        inputvalue:that.data.inputvalue,
        Org:that.data.CheckOrg
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          that.setData({
            SQty:'(0台)',
          })
        }else{
          that.setData({
            SQty:res.data[0][0],
          })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
    //获取总计台数结束
  },
  bindKeyInput:function(e){
    this.setData({
      inputvalue:e.detail.value
    })
  },
  //打开侧边栏
  orgtap:function(e){
    this.setData({
      modalName: e.currentTarget.dataset.target
    })
  },
  //隐藏侧边栏
  hideModal(e) {
    this.setData({
      modalName: null
    })
  },
  //潜水泵
  Switch301:function(e){
    console.log('潜水泵选择:'+e.detail.value)
    this.setData({
      Check301:e.detail.value
    })
  },
  //商用泵
  Switch303:function(e){
    console.log('商用泵选择:'+e.detail.value)
    this.setData({
      Check303:e.detail.value
    })
  },
  //陆上泵
  Switch304:function(e){
    console.log('陆上泵选择:'+e.detail.value)
    this.setData({
      Check304:e.detail.value
    })
  },
  //新界江苏
  Switch206:function(e){
    console.log('新界江苏选择:'+e.detail.value)
    this.setData({
      Check206:e.detail.value
    })
  },
  //老百姓
  Switch208:function(e){
    console.log('老百姓选择:'+e.detail.value)
    this.setData({
      Check208:e.detail.value
    })
  },
  true:function(){
  },
  //获得事业部的勾选项
  GetCheckOrg:function(){
    this.setData({
      CheckOrg:''//每次查询的时候都要把这个字段清空
    })
    if(JSON.stringify(this.data.Check301)=='true'){
      this.setData({
        CheckOrg:this.data.CheckOrg+'1001703290110519,'
      })
    } if(JSON.stringify(this.data.Check303)=='true'){
      this.setData({
        CheckOrg:this.data.CheckOrg+'1001703290110977,'
      })
    } if(JSON.stringify(this.data.Check304)=='true'){
      this.setData({
        CheckOrg:this.data.CheckOrg+'1001703290111206,'
      })
    } if(JSON.stringify(this.data.Check206)=='true'){
      this.setData({
        CheckOrg:this.data.CheckOrg+'1001808210086540,'
      })
    } if(JSON.stringify(this.data.Check208)=='true'){
      this.setData({
        CheckOrg:this.data.CheckOrg+'1002004041482963,'
      })
    }
    if(this.data.CheckOrg==''){
      this.setData({
        CheckOrg:'\'\'' //这边是为空的时候传''过去，免得服务端报语法错误
      })
    }else{
    this.setData({
      CheckOrg:this.data.CheckOrg.substring(0,this.data.CheckOrg.length-1)
    })
  }
    console.log(this.data.CheckOrg)
  },
  //加载进度,状态控制
  loadProgress() {
      let loading = this.data.isLoading;
      let progress = this.data.loadProgress;
      if (loading) {
        progress = progress + 10;
        if (progress > 100 && progress < 110)
          progress = 100;
        else if (progress >= 110)
          progress = 10;
      }
      else {
        progress = 0;
      }
  
      this.setData({
        loadProgress: progress,
        isLoading: loading
      });
  
      if (progress > 0 && progress <= 100) {
        setTimeout(() => {
          this.loadProgress();
        }, 100)
      }
    }
})