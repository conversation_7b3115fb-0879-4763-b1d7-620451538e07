<!--pages/mo/mo-storage/mo-storage.wxml-->
<import src="mo-storage-item/mo-storage-item.wxml" />
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search bg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索生产订单号、料号、规格" confirm-type="search" bindinput="bindKeyInput" bindconfirm="searchData" value="{{inputvalue}}"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
    <text class="cuIcon-list lg" style="padding:25rpx;font-size:40rpx" bindtap="orgtap" data-target="DrawerModalR"></text>
  </view>

<view class="cu-modal drawer-modal justify-end {{modalName=='DrawerModalR'?'show':''}}" bindtap="hideModal">
  <view class="cu-dialog basis-lg" catchtap style="top:{{CustomBar}}px;height:calc(100vh - {{CustomBar}}px)">
    <view class="cu-list menu text-left">
      <view class="cu-item">
      <view class="content padding-tb-sm">
        <view><text class="margin-right-xs"></text>潜水泵事业部</view>
      </view>
      <view class="action">
        <switch class="sm" bindchange="Switch301" checked="{{Check301}}"></switch>
      </view>
    </view>
    <view class="cu-item">
      <view class="content padding-tb-sm">
        <view><text class="margin-right-xs"></text>陆上泵事业部</view>
      </view>
      <view class="action">
        <switch class="sm" bindchange="Switch304" checked="{{Check304}}"></switch>
      </view>
    </view>
    <view class="cu-item">
      <view class="content padding-tb-sm">
        <view><text class="margin-right-xs"></text>商用泵事业部</view>
      </view>
      <view class="action">
        <switch class="sm" bindchange="Switch303" checked="{{Check303}}"></switch>
      </view>
    </view>
    <view class="cu-item">
      <view class="content padding-tb-sm">
        <view><text class="margin-right-xs"></text>新界泵业(江苏)</view>
      </view>
      <view class="action">
        <switch class="sm" bindchange="Switch206" checked="{{Check206}}"></switch>
      </view>
    </view>
    <view class="cu-item">
      <view class="content padding-tb-sm">
        <view><text class="margin-right-xs"></text>老百姓泵业</view>
      </view>
      <view class="action">
        <switch class="sm" bindchange="Switch208" checked="{{Check208}}"></switch>
      </view>
    </view>
    </view>
  </view>
</view>

  <view class="title_wrap">
<view class="titleA">车间待入库{{SQty}}</view>
</view>

  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="padding-sm" style="flex-basis:23%;padding: 5rpx;">生产订单</view>
    <view class="padding-sm" style="flex-basis:22%;padding: 5rpx;">料号</view>
    <view class="padding-sm" style="flex-basis:23%;padding: 5rpx;">规格</view>
    <view class="padding-sm" style="flex-basis:10%;padding: 5rpx;">数量</view>
    <view class="padding-sm" style="flex-basis:22%;padding: 5rpx;font-size:25rpx">扫码下线时间</view>
  </view>
</view>

<view class="bg-white"> 
    <block wx:for="{{postList}}" wx:for-item="item" wx:for-index="idx">
    <view data-index="{{item}}" wx:if="{{idx%2==0}}">
      <template is="storageitem" data="{{item}}" />
    </view>
    <view data-index="{{item}}" class="block" wx:else>
      <template is="storageitem" data="{{item}}" />
    </view>
  </block>

  <view class="cu-load {{(loadState==1?'loading':(loadState==2?'over':''))}}"></view>
  </view>
