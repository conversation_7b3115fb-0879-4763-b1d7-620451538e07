// pages/mo/mo-cycletimeZuz/mo-cycletimeZuz.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    scrollLeft: 0,
    pagenum:0,
    pagesize:50,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    loadState: 0,//加载状态  1:加载中,2:没有更多了
    currentItem: 1,//当前的筛选项
    executeList: {},//生产订单异常列表
    searchKey:'',//搜索关键字
    user: null //当前用户
  },
  //查询按钮及input框确认
  searchData:function(){
    //查询按钮先失效，防止多次点击
    this.setData({
      executeList: {},
      loadState:0,
      pagenum: 0,//分页记录数
      pagesize: 50,//分页大小
      loadProgress:0,
    })
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    var that = this
    let org = this.data.user.org;
    let type = this.data.currentItem;
    let key = this.data.searchKey;
    console.log('org:'+org+',type:'+type+',key:'+key);
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'mocycletimeZuz',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        org:org,
        type:type,
        key:key
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '没有数据！',
            icon: 'none',
            duration: 2000
          })
          that.setData({
            isLoading:false//按钮启用
          })
        }else{
        that.setData({
          executeList:res.data,
          isLoading:false//按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    this.data.user = app.globalData.accountInfo;
    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    var that = this
    let org = this.data.user.org;
    let type = this.data.currentItem;
    let key = this.data.searchKey;
    console.log('org:'+org+',type:'+type+',key:'+key);
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'mocycletimeZuz',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        org:org,
        type:type,
        key:key
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '没有数据！',
            icon: 'none',
            duration: 2000
          })
          that.setData({
            isLoading:false//按钮启用
          })
        }else{
        that.setData({
          executeList:res.data,
          isLoading:false//按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    //查询按钮先失效，防止多次点击
    this.setData({
      executeList: {},
      loadState:0,
      pagenum: 0,//分页记录数
      pagesize: 50,//分页大小
      loadProgress:0,
    })
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    var that = this
    let org = this.data.user.org;
    let type = this.data.currentItem;
    let key = this.data.searchKey;
    console.log('org:'+org+',type:'+type+',key:'+key);
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'mocycletimeZuz',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        org:org,
        type:type,
        key:key
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '没有数据！',
            icon: 'none',
            duration: 2000
          })
          that.setData({
            isLoading:false//按钮启用
          })
        }else{
        that.setData({
          executeList:res.data,
          isLoading:false//按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('加载更多');
    this.setData({
      loadState:1,
      pagenum: this.data.pagenum+1,
      loadProgress:0,
    })
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    var that = this
    let org = this.data.user.org;
    let type = this.data.currentItem;
    let key = this.data.searchKey;
    console.log('org:'+org+',type:'+type+',key:'+key);
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'mocycletimeZuz',
        pagenum:that.data.pagenum,
        pagesize:that.data.pagesize,
        org:org,
        type:type,
        key:key
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '暂无更多数据',
            icon: 'none',
            duration: 1000
          }),
          that.setData({
            loadState:2,
            pagenum:that.data.pagenum-1,
            isLoading:false//按钮启用
          })
        }else{
          wx: wx.showToast({
            title: '数据加载完毕',
            icon: 'none',
            duration: 1000
          }),
        that.setData({
          executeList:that.data.executeList.concat(res.data),
          loadState:0,
          isLoading:false//按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },
  bindKeyInput:function(e){
    this.setData({
      searchKey:e.detail.value
    })
  },
  //选择查询条件
  tapcondition: function (e) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //每次重新点击后，都需要重置分页的数据
    this.setData({
      executeList: {},
      loadState:0,
      pagenum: 0,//分页记录数
      pagesize: 50,//分页大小
      loadProgress:0,
    })
    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    if (e.currentTarget.dataset) {
      let cur = e.currentTarget.dataset.currentItem;
      this.setData({
        currentItem: cur,
        scrollLeft: (cur - 1) * 40
      });
      var that = this
      let org = this.data.user.org;
      let type = this.data.currentItem;
      let key = this.data.searchKey;
      console.log('org:'+org+',type:'+type+',key:'+key);
      wx.request({
        url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
        data: {
          api: 'mocycletimeZuz',
          pagenum:that.data.pagenum,
          pagesize:that.data.pagesize,
          org:org,
          type:type,
          key:key
        },
        method: 'POST',
        header: { 'Content-Type': 'application/x-www-form-urlencoded' },
        success: function (res) {
          // success
          console.log(res);//打印请求返回的结果
          if(res.data.length==0){
            wx: wx.showToast({
              title: '没有数据！',
              icon: 'none',
              duration: 2000
            })
            that.setData({
              isLoading:false//按钮启用
            })
          }else{
          that.setData({
            executeList:res.data,
            isLoading:false//按钮启用
          })
          }
        },
        fail: function (res) {
          // fail
        },
        complete: function (res) {
          // complete
        }
      })
    }
  },
    //加载进度,状态控制
    loadProgress() {
      let loading = this.data.isLoading;
      let progress = this.data.loadProgress;
      if (loading) {
        progress = progress + 10;
        if (progress > 100 && progress < 110)
          progress = 100;
        else if (progress >= 110)
          progress = 10;
      }
      else {
        progress = 0;
      }
  
      this.setData({
        loadProgress: progress,
        isLoading: loading
      });
  
      if (progress > 0 && progress <= 100) {
        setTimeout(() => {
          this.loadProgress();
        }, 100)
      }
    },
    taprow(event) {
      let post = event.currentTarget.dataset;
      console.log(post.index[0]);
      wx.navigateTo({
        url: 'mo-cycletimeZuz-detail/mo-cycletimeZuz-detail?Docno=' + post.index[0],
      })
    }
})