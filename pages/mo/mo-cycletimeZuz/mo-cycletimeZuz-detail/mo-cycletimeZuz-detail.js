// pages/mo/mo-cycletimeZuz/mo-cycletimeZuz-detail/mo-cycletimeZuz-detail.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    Docno:'',
    user:null,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    t1:'',
    t2:'',
    t3:'',
    t4:'',
    t5:'',
    t6:'',
    t7:'',
    t8:'',
    t9:'',
    t10:'',
    t11:'',
    t12:'',
    t13:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.data.user = app.globalData.accountInfo;
    this.data.isLoading = true;
    this.loadProgress();//最上方进度条

    this.setData({
      Docno:options.Docno
    })
    //获取维修工单信息
    var that = this
    let org = this.data.user.org;
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'mocycletimeZuzDocno',
        Docno:that.data.Docno,
        org:org
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        that.setData({
          t1:res.data[0][0],
          t2:res.data[0][1],
          t3:res.data[0][2],
          t4:res.data[0][3],
          t5:res.data[0][4],
          t6:res.data[0][5],
          t7:res.data[0][6],
          t8:res.data[0][7],
          t9:res.data[0][8],
          t10:res.data[0][9],
          t11:res.data[0][10],
          t12:res.data[0][11],
          t13:res.data[0][12],
          isLoading:false
        })
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },
    //加载进度,状态控制
    loadProgress() {
      let loading = this.data.isLoading;
      let progress = this.data.loadProgress;
      if (loading) {
        progress = progress + 10;
        if (progress > 100 && progress < 110)
          progress = 100;
        else if (progress >= 110)
          progress = 10;
      }
      else {
        progress = 0;
      }
  
      this.setData({
        loadProgress: progress,
        isLoading: loading
      });
  
      if (progress > 0 && progress <= 100) {
        setTimeout(() => {
          this.loadProgress();
        }, 100)
      }
    },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  }
})