<!--pages/mo/mo-storagescan/mo-storagescan.wxml-->
<import src="mo-storagescan-item/mo-storagescan-item.wxml" />
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>

  <view class="cu-bar search bg-white">
    <view class="action">
    <button bindtap="btnscan" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">扫码</button>
  </view>
  <view class="titleA">车间待入库扫描</view>
  <view class="action">
    <button bindtap="btnOK" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">提交</button>
    </view>
  </view>

  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="padding-sm" style="flex-basis:25%;padding: 18rpx;">生产订单号</view>
    <view class="padding-sm" style="flex-basis:22%;padding: 18rpx;">料号</view>
    <view class="padding-sm" style="flex-basis:33%;padding: 18rpx;">规格</view>
    <view class="padding-sm" style="flex-basis:20%;padding: 18rpx;">数量</view>
  </view>

  <view class="bg-white"> 
    <block wx:for="{{postList}}" wx:for-item="item" wx:for-index="idx">
    <view bindtap="onTapToDetail" data-index="{{item}}" wx:if="{{idx%2==0}}">
      <template is="stscanitem" data="{{item}}" />
    </view>
    <view bindtap="onTapToDetail" data-index="{{item}}" class="block" wx:else>
      <template is="stscanitem" data="{{item}}" />
    </view>
  </block>
    </view>
