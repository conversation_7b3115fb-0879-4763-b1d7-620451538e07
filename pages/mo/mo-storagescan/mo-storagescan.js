// pages/mo/mo-storagescan/mo-storagescan.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    barcode:'',
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    postList:{} //数组信息
  },

  //扫码
  btnscan:function(){
    this.setData({
      barcode:'',
      postList:{},//扫码先清空列表数据
      loadProgress:0,
    })
    var that=this
    //微信自带扫码功能
    wx.scanCode({
      onlyFromCamera: true,
      success (res) {
        console.log(res)
        //赋值
        that.setData({ barcode: res.result});
        //调用一下方法
        that.searchData();
      }
    });

  },

  searchData:function(){
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    if(this.data.barcode.indexOf('http')>=0){//表示扫到的二维码是网页形式的
      var index=this.data.barcode.replace(/\*/g,'').lastIndexOf("sn=")
      this.setData({
        barcode:this.data.barcode.replace(/\*/g,'').substring(index+3,this.data.barcode.replace(/\*/g,'').length)
      })
    }else{//表示其他三种情况的二维码
      var index=this.data.barcode.replace(/\*/g,'').lastIndexOf("@")
      this.setData({
        barcode:this.data.barcode.replace(/\*/g,'').substring(index+1,this.data.barcode.replace(/\*/g,'').length)
      })
    }

    var that = this
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'StorageScan',
        BarCode:that.data.barcode,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){
          wx: wx.showToast({
            title: '此条码无法获取数据,请检查!',
            icon: 'none',
            duration: 2000
          })
        that.setData({
          isLoading:false //按钮启用
        })
        }else{
        that.setData({
          postList:res.data,
          isLoading:false //按钮启用
        })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  },

  //提交
  btnOK:function(){
    if(this.data.postList==undefined||JSON.stringify(this.data.postList)=='{}'||this.data.barcode==''){
      wx: wx.showToast({
        title: '没有数据无法提交!',
        icon: 'none',
        duration: 2000
      })
    }else{
      console.log(this.data.barcode);
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;//按钮失效
    this.loadProgress();//最上方进度条

    var that = this

    wx.request({
        url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
        data: {
          api: 'StorageScanInsert',
          BarCode:that.data.barcode,
          ScanPerson:app.globalData.accountInfo.name,
        },
        method: 'POST',
        header: { 'Content-Type': 'application/x-www-form-urlencoded' },
        success: function (res) {
          // success
          console.log(res);//打印请求返回的结果
          if(res.data=='提交成功'){
            wx: wx.showToast({
              title: '提交成功!',
              icon: 'none',
              duration: 2000
            })
          that.setData({
            isLoading:false, //按钮启用
            barcode:'',
            postList:{}
          })
          }else if(res.data=='提交失败'){
            wx: wx.showToast({
              title: '提交失败,条码信息已存在,请联系管理员!',
              icon: 'none',
              duration: 4000
            })  
            that.setData({
              isLoading:false //按钮启用
            })
          }
        },
        fail: function (res) {
          // fail
        },
        complete: function (res) {
          // complete
        }
      })
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
})