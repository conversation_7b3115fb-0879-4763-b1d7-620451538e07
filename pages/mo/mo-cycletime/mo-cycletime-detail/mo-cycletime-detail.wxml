<wxs module="filter" src="../../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view wx:if="{{!isError}}">
  <block wx:for="{{executeLine}}" wx:for-item="item" wx:for-index="idx" wx:key="id">
    <view class="cust-row">
      <view class="sg-label sg-mwidth4">生产订单:</view>
      <text selectable="true" class="sg-content">{{item.docNo}}</text>
    </view>
    <view class="cust-row">
      <view class="sg-label sg-mwidth4">客户简称:</view>
      <text selectable="true"  class="sg-content">{{item.cust_Name}}</text>
    </view>
    <view class="cust-row">
      <view class="sg-label sg-mwidth4">销售订单:</view>
      <text selectable="true"  class="sg-content">{{item.soCode}}</text>
    </view> 
    <view class="cust-row">
      <view class="sg-label sg-mwidth4">生产周期:</view>
      <text selectable="true" class="sg-content">{{item.cycleTime}}</text>
    </view>
    <view class="cust-row">
      <view class="sg-label sg-mwidth2">产线:</view>
      <text selectable="true" class="sg-content">{{item.productLineName}}</text>
    </view>
    <view class="cust-row" wx:if="{{item.actualRcvTime}}">
      <view class="sg-label sg-mwidth5">最后入库日:</view>
      <text selectable="true" class="sg-content">{{filter.formatDate(item.actualRcvTime)}}</text>
    </view>     
    <view class="cust-row">
      <view class="sg-label sg-mwidth5">计划开工日:</view>
      <text selectable="true" class="sg-content">{{filter.formatDate(item.startDate)}}</text>
    </view> 
    <view class="cust-row">
      <view class="sg-label sg-mwidth5">计划完工日:</view>
      <text selectable="true" class="sg-content">{{filter.formatDate(item.completeDate)}}</text>
    </view>    
    <view class="cust-row">
      <view class="sg-label sg-mwidth2">料号:</view>
      <text selectable="true" class="sg-content">{{item.item_Code}}</text>
    </view> 
    <view class="cust-row">
      <view class="sg-label sg-mwidth2">品名:</view>
      <text selectable="true" class="sg-content">{{item.item_Name}}</text>
    </view>  
    <view class="cust-row">
      <view class="sg-label sg-mwidth2">规格:</view>
      <text selectable="true" class="sg-content">{{item.item_SPECS}}</text>
    </view>
    <view class="cust-row">
      <view class="sg-label sg-mwidth4">生产数量:</view>
      <view class="sg-content">{{item.productQty}}</view>
    </view> 
    <view class="cust-row" wx:if="{{item.unRcvQty}}">
      <view class="sg-label sg-mwidth4">未入库数:</view>
      <view class="sg-content">{{item.unRcvQty}}</view>
    </view>
    <view class="cust-row" wx:if="{{item.delayQty}}">
      <view class="sg-label sg-mwidth4">延期数量:</view>
      <view class="sg-content">{{item.delayQty}}</view>
    </view>                   
  </block>  
</view>
<view class="cu-load bg-red erro" wx:else></view>
