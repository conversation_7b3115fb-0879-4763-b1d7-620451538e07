<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索单号、产品、客户" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>
  <scroll-view scroll-x class="nav" scroll-with-animation scroll-left="{{scrollLeft}}">
    <view class="cu-item {{currentItem==1?'cur text-shimge':''}}" data-current-item="1" bindtap="tapcondition">当日应入库</view>
    <view class="cu-item {{currentItem==2?'cur text-shimge':''}}" data-current-item="2" bindtap="tapcondition">当月未入库</view>
    <view class="cu-item {{currentItem==3?'cur text-shimge':''}}" data-current-item="3" bindtap="tapcondition">当月延期入库</view>
  </scroll-view> 
</view>

<view class="bg-white"> 
  <view class="flex align-center bg-shimge text-bold text-center text-content">
    <view class="basis-p18 padding-sm">单号</view>
    <view class="basis-p20 padding-sm">料号</view>
    <view class="basis-p22 padding-sm">品名</view>
    <view class="basis-p27 padding-sm">规格</view>
    <view class="basis-p13 padding-sm">数量</view>
  </view>
  <block wx:for="{{executeList}}" wx:for-item="item" wx:key="id">
    <view class="flex align-center solids-bottom sg-row" bindtap="taprow" data-id="{{item.id}}">
      <view class="basis-p18 padding-xs text-multcut">{{item.docNo}}</view>
      <view class="basis-p20 padding-xs text-multcut">{{item.item_Code}}</view>
      <view class="basis-p22 padding-xs text-multcut">{{item.item_Name}}</view>
      <view class="basis-p27 padding-xs text-multcut">{{item.item_SPECS}}</view>
      <view class="basis-p13 padding-xs text-multcut text-center">{{item.qty}}</view>
    </view>
  </block>
  <view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
  <view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>
</view>

