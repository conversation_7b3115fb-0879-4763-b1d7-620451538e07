const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../../utils/util.js'

Page({
  data: {
    scrollLeft: 0,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    loadState: 0,//加载状态  1:继续下拉加载更多,2:加载中,3:没有更多了
    isError: false,//加载失败
    currentItem: 1,//当前的筛选项
    executeList: [],//生产订单异常列表
    bottomId: 0,//底部最后一行Id,用于上拉触底事件
    searchKey:'',//搜索关键字
    user: null //当前用户
  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;

    this.getRequestData(true);
  },
  //选择查询条件
  tapcondition: function (e) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    if (e.currentTarget.dataset) {
      let cur = e.currentTarget.dataset.currentItem;
      this.setData({
        currentItem: cur,
        scrollLeft: (cur - 1) * 40
      });

      this.getRequestData(true, this.goTop);
    }
  },
  //获取后台数据
  getRequestData: function (again, callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    if (again) {
      //重新获取数据
      this.data.bottomId = 0;
      this.data.executeList = [];
    }
    else{
      //上拉加载提示
      this.setData({ loadState: 2 });  
    }
    console.log('id:' + this.data.bottomId);
    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.code;    
    let type = this.data.currentItem;
    let key = this.data.searchKey;
    let begin = this.data.bottomId;
    let url = app.globalData.apiBase + '/api/MOCycleTime?user=' + user + '&org=' + org + '&type=' + type + '&key=' + key
            + '&begin=' + begin + '&pagesize=' + pagesize;
    console.log(url);

    let suc = rd => {
      //更新数据
      let totalData = that.data.executeList;
      //判断数据大小是否等于每页数大小，相等则认为还有数据，否则认为没有更多数据
      let hasmore = rd.length == pagesize ? 1 : 0;
      if (rd.length > 0) {
        totalData = totalData.concat(rd);
        that.data.bottomId = rd[rd.length - 1].id;
      }

      console.log(hasmore);

      that.setData({
        loadState: hasmore,
        executeList: totalData
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;

      if (callBack)
        callBack(rd);
    };

    let fl = () => {
      //更新数据
      that.data.bottomId = 0;
      that.setData({
        isError: true,
        loadState: 0,
        executeList: []
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });    
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //搜索框输入完成
  confirmKeyInput: function(e){
    console.log(e.detail);
    this.data.searchKey = e.detail.value;

    this.getRequestData(true, this.goTop);
  },
  //查询按钮
  searchData: function () {
    this.getRequestData(true, this.goTop);
  },
  //滚动条回到顶部
  goTop: function(){
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //搜索文本改变事件
  bindKeyInput: function (e) {
    console.log(e.detail);

    this.data.searchKey = e.detail.value;
  },
  //跳转至明细页
  taprow:function(e){
    console.log(e.currentTarget.dataset);

    let id = e.currentTarget.dataset.id;
    if (id){
      wx.navigateTo({
        url: 'mo-cycletime-detail/mo-cycletime-detail?id=' + id
      });
    }
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData(true);
  },
  //上拉加载更多
  onReachBottom: function () {
    if (this.data.loadState == 0 || this.data.loadState == 3)
      return;
    this.getRequestData(false);
  }
})