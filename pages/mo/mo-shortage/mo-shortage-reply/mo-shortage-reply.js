const app = getApp()
import { wuxToast, http, formatDate, formatTime, updateAccountExpire } from '../../../../utils/util.js'

Page({
  data: {
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    isError: false,//加载失败 
    dataMain: {},//责任人回复主表
    guid: null,//唯一主键,用于多次保存执行更新操作
    id:null,//对应缺料明细的主键
    user: null, //当前用户
    showCalendar: false,

  },
  onLoad: function (options) {


    this.data.user = app.globalData.accountInfo;

    var cur = app.globalData.curReply;
    if (cur){
      var main = {
        simu: cur.simu,
        mo: cur.mo,
        moCode: cur.moCode,
        preStartDate: cur.planStartDate,
        preEndDate: cur.preEndDate,
        itemCode: cur.item_Code,
        itemName: cur.item_Name,
        itemSPECS: cur.item_SPECS,
        createdBy: this.data.user.name,
        createdOn: formatTime(new Date())
      };
      this.data.dataMain = main;
      this.data.id = cur.id;

      this.getRequestData(main.mo, main.itemCode);
    }
  },
  //获取后台数据
  getRequestData: function (mo, itemCode) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    //隐藏加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let org = this.data.user.org;
    let user = this.data.user.code;
    let url = app.globalData.apiBase + '/api/MOShortageReply?user=' + user + '&org=' + org + '&mo=' + mo + '&item=' + itemCode;

    console.log(url);

    let suc = rd => {
      //更新数据
      var main = that.data.dataMain;
      var guid = that.data.guid;
      if (guid == null && rd){
        guid = rd.pkid;
      }
      main.pkid = guid;
      if(rd && rd.id>0){
        main.planDate = rd.planDate;
        main.reply = rd.reply;
        main.reason = rd.reason;
        main.measure = rd.measure;
        main.createdBy = rd.createdBy;
        main.createdOn = rd.createdOn;
      }
      that.setData({
        dataMain: main
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //输入框改变事件
  dateChange: function(e) {
    let main = this.data.dataMain;
    main.planDate = formatTime(e.detail.value);
    this.setData({ dataMain: main });
  },
  replyInput: function (e) {
    let main = this.data.dataMain;
    main.reply = e.detail.value;
    this.setData({ dataMain: main });
  },
  reasonInput: function (e) {
    let main = this.data.dataMain;
    main.reason = e.detail.value;
    this.setData({ dataMain: main });
  },
  measureInput: function (e) {
    let main = this.data.dataMain;
    main.measure = e.detail.value;
    this.setData({ dataMain: main });

  },
  //保存
  saveReply: function (e){

    let datePlan = this.data.dataMain.preStartDate;
    let date = this.data.dataMain.planDate;
    if(!date){
      wuxToast('计划到货时间不能为空');
      return
    }
    if(date && datePlan && datePlan<date){
      wuxToast('回复的计划到货时间超期，请与计划协调开工时间');
      return
    }

    let that = this;
    let url = app.globalData.apiBase + '/api/MOShortageReply';

    console.log(url);
    console.log(this.data.dataMain);

    wx.showLoading({ title: '正在保存...', mask: true });

    let suc = rd => {
      //获取上一页,修改计划到货时间
      let pages = getCurrentPages(); //获取当前页面栈
      let prevPage = pages[pages.length - 2]; //上一页
      let id = that.data.id;
      let date = that.data.dataMain.planDate;
      if(id)
        prevPage.changePlanDate(id, date);

      wx.hideLoading();
      wuxToast(rd);
    };

    let fl = () => {
      wx.hideLoading();
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
          else
            wuxToast('保存失败');
        }
      });
    };

    http(url, 'POST', this.data.dataMain , suc, fl);
  },
  //下拉刷新
  onPullDownRefresh: function () {
    //var main = this.data.dataMain;

    //this.getRequestData(main.mo, main.itemCode);
  },
  //选择日历
  onShowCalendar:function(){
    this.setData({ showCalendar: true });
  },
  onCloseCalendar:function(){
    this.setData({ showCalendar: false });
  },
  onConfirmCalendar:function(e){
    console.log(e);

    let main = this.data.dataMain;
    main.planDate = formatTime( e.detail);

    this.setData({ dataMain: main, showCalendar: false });
  },
})