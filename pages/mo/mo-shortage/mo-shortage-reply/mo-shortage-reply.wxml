<wxs module="filter" src="../../../../utils/filter.wxs"></wxs>
<van-calendar show="{{showCalendar}}" color="#009fa8" round="false" row-height="38" position="top" bind:close="onCloseCalendar" bind:confirm="onConfirmCalendar" />

<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view wx:if="{{!isError}}"> 
  <view class="cust-row">
    <view class="sg-label sg-mwidth2">料号:</view>
    <text selectable="true" class="sg-content">{{dataMain.itemCode}}</text>  
  </view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth2">品名:</view>
    <text selectable="true" class="sg-content">{{dataMain.itemName}}</text>  
  </view>    
  <view class="cust-row">
    <view class="sg-label sg-mwidth2">规格:</view>
    <text selectable="true" class="sg-content">{{dataMain.itemSPECS}}</text>
  </view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth4">计划开工:</view>
    <text selectable="true" class="sg-content">{{filter.formatDate(dataMain.preStartDate)}}</text>
  </view>
  <view class="cust-row margin-bottom-xs">
    <view class="sg-label sg-mwidth4">计划完工:</view>
    <text selectable="true" class="sg-content">{{filter.formatDate(dataMain.preEndDate)}}</text>
  </view>  
	<view class="cu-form-group">
		<view class="title sg-mwidth5">计划到货时间:</view>
    <input disabled="true" placeholder="{{showCalendar?'':'请选择计划到货时间'}}" value="{{filter.formatDate(dataMain.planDate)}}"></input>
    <text bindtap="onShowCalendar" class="cuIcon-time" ></text>
	</view>             
	<view class="cu-form-group">
		<view class="title sg-mwidth5">责任人回复:</view>
    <view wx:if="{{showCalendar}}"><text>{{dataMain.reply}}</text></view>
		<textarea wx:else auto-height maxlength="500" bindinput="replyInput" value="{{dataMain.reply}}" placeholder="{{showCalendar?'':'请输入责任人回复'}}" ></textarea>
	</view>
	<view class="cu-form-group">
		<view class="title sg-mwidth4">异常原因:</view>
    <view wx:if="{{showCalendar}}"><text>{{dataMain.reason}}</text></view>
		<textarea wx:else auto-height maxlength="500" bindinput="reasonInput" value="{{dataMain.reason}}" placeholder="{{showCalendar?'':'请输入异常原因'}}" ></textarea>
	</view>  
	<view class="cu-form-group">
		<view class="title sg-mwidth4">响应措施:</view>
    <view wx:if="{{showCalendar}}"><text>{{dataMain.measure}}</text></view>
		<textarea wx:else auto-height maxlength="500" bindinput="measureInput" value="{{dataMain.measure}}" placeholder="{{showCalendar?'':'请输入响应措施'}}" ></textarea>
	</view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth3">回复人:</view>
    <text selectable="true" class="sg-content">{{dataMain.createdBy}}</text>  
  </view>
  <view class="cust-row">
    <view class="sg-label sg-mwidth4">回复时间:</view>
    <text selectable="true" class="sg-content">{{filter.formatTime(dataMain.createdOn)}}</text>  
  </view>  
  <view class="cu-bar foot bg-shimge" bindtap="saveReply">
    <view class="content text-bold">保 存</view>
  </view> 
</view>
<view class="cu-load bg-red erro" wx:else></view>
