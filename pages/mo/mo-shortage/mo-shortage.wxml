<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search sg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索单号、料品、客户、产线" confirm-type="search" bindinput="bindKeyInput" bindconfirm="confirmKeyInput"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>
  <scroll-view scroll-x class="nav" scroll-with-animation scroll-left="{{scrollLeft}}">
    <view class="cu-item solids-right" data-current-item="0" bindtap="tapcondition">
      {{typeSelect.displayName}}<text class="cuIcon-{{isSelectPop?'triangleupfill':'triangledownfill'}} lg"></text>
    </view>
    <view class="cu-item {{currentItem==1?'cur text-shimge':''}}" data-current-item="1" bindtap="tapcondition">已开工</view>
    <view class="cu-item {{currentItem==2?'cur text-shimge':''}}" data-current-item="2" bindtap="tapcondition">当日开工</view>
    <view class="cu-item {{currentItem==3?'cur text-shimge':''}}" data-current-item="3" bindtap="tapcondition">明日开工</view>
    <view class="cu-item {{currentItem==4?'cur text-shimge':''}}" data-current-item="4" bindtap="tapcondition">后日开工</view>
    <view class="cu-item {{currentItem==6?'cur text-shimge':''}}" data-current-item="6" bindtap="tapcondition">大后日开工</view>
    <view class="cu-item {{currentItem==5?'cur text-shimge':''}}" data-current-item="5" bindtap="tapcondition">预排订单</view>
  </scroll-view>
  <scroll-view class="sg-select" scroll-y wx:if="{{isSelectPop}}">
      <block wx:for="{{typeSelect.items}}" wx:for-item="item" wx:key="value">
        <view class="cu-form-group" data-value="{{item.value}}" bindtap="tapselect">
          <view class="title {{item.checked?'text-shimge':''}}">{{item.name}}</view>
          <text class="cuIcon-check lg text-shimge" wx:if="{{item.checked}}"></text>
        </view>
      </block>
  </scroll-view>
  <view class="sg-divider"></view>
  <view class="cu-bar sg-white" style="min-height:80rpx">
    <view class="action">
      <scroll-view scroll-x class="tag-nav" scroll-with-animation scroll-left="{{scrollLeft2}}">
        <view class="cu-tag radius {{typeSelect.index==0 && dataItem==1?'bg-current':'bg-gray'}}" data-current-item2="1" bindtap="tapconditionType">
          {{qtSelect.displayName}}<text class="cuIcon-{{dataItem==1 && isSelectPop2?'triangleupfill':'triangledownfill'}} lg"></text>
        </view>
        <view class="cu-tag radius {{typeSelect.index==0 && dataItem==2?'bg-current':'bg-gray'}}" data-current-item2="2" bindtap="tapconditionType">
          {{mzSelect.displayName}}<text class="cuIcon-{{dataItem==2 && isSelectPop2?'triangleupfill':'triangledownfill'}} lg"></text>
        </view>
      </scroll-view>
    </view>
    <view class="action">
      <view class="sg-note" wx:if="{{typeSelect.index==0 && dataMain.sumQty}}">生产数{{dataMain.sumQty}}</view>
      <view class="sg-note" wx:if="{{typeSelect.index==0 }}">{{dataMain.type+' '+filter.formatPercent(dataMain.rate,2)}}</view>
    </view>
  </view>
  <scroll-view class="sg-select" scroll-y wx:if="{{isSelectPop2}}">
      <block wx:for="{{popSelect.items}}" wx:for-item="item" wx:key="value">
        <view class="cu-form-group" data-value="{{item.value}}" bindtap="tapPopSelect">
          <view class="title {{item.checked?'text-shimge':''}}">{{item.name}}</view>
          <text class="cuIcon-check lg text-shimge" wx:if="{{item.checked}}"></text>
        </view>
      </block>
  </scroll-view>
</view>

<view class="bg-white">
  <block wx:if="{{dataMain.item!='产成品'}}">
    <view class="flex align-center bg-shimge text-bold text-center text-content">
      <view class="basis-p20 padding-sm">料号</view>
      <view class="basis-p21 padding-sm">品名</view>
      <view class="basis-p46 padding-sm">规格</view>
      <view class="basis-p13 padding-sm">数量</view>
    </view>
    <block wx:for="{{dataList}}" wx:for-item="item" wx:key="mo">
      <view class="flex align-center solids-bottom sg-row">
        <view class="basis-p20 padding-xs text-multcut">{{item.item_Code}}</view>
        <view class="basis-p21 padding-xs text-multcut">{{item.item_Name}}</view>
        <view class="basis-p46 padding-xs text-multcut">{{item.item_SPECS}}</view>
        <view class="basis-p13 padding-xs text-multcut text-center">{{item.unSuitQty}}</view>
      </view>
    </block>
  </block>
  <block wx:else>
    <view class="flex align-center bg-shimge text-bold text-center text-content">
      <view class="basis-p18 padding-sm">单号</view>
      <view class="basis-p20 padding-sm">料号</view>
      <view class="basis-p22 padding-sm">品名</view>
      <view class="basis-p26 padding-sm">规格</view>
      <view class="basis-p14 padding-sm">{{dataMain.type=='齐套率'?'数量':'比率'}}</view>
    </view>
    <block wx:for="{{dataList}}" wx:for-item="item" wx:key="mo">
      <view class="flex {{item.docColour==1?'text-red':''}} align-center solids-bottom sg-row" bindtap="taprow" data-id="{{item.id}}" data-mo="{{item.mo}}">
        <view class="basis-p18 padding-xs text-multcut">{{item.moCode}}</view>
        <view class="basis-p20 padding-xs text-multcut">{{item.item_Code}}</view>
        <view class="basis-p22 padding-xs text-multcut">{{item.item_Name}}</view>
        <view class="basis-p26 padding-xs text-multcut">{{item.item_SPECS}}</view>
        <view class="basis-p14 padding-xs text-multcut text-center">{{dataMain.type=='齐套率'?item.unSuitQty:filter.formatPercent(item.unSuitQty,0)}}</view>
      </view>
    </block>
  </block>

  <view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
  <view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>
</view>
