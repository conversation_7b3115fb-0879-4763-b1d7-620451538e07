const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../../utils/util.js'

Page({
  data: {
    typeSelect: {
      index: 0,
      displayName:'产成品',
      items:[
        { name: '产成品', value: 0, checked: true },
        { name: '半成品', value: 1 },
        { name: '原材料', value: 2 }
    ]},
    qtSelect: {
      index: 0,
      displayName: '齐套率',
      items: [
        { name: '全部', value: 0, checked: true, displayName: '齐套率',qt:3 },
        { name: '齐套', value: 1, displayName: '齐套', qt: 1 },
        { name: '未齐套', value: 2, displayName: '未齐套', qt: 2 }
    ]},
    mzSelect: {
      index: 0,
      displayName: '满足率',
      items: [
        { name: '全部', value: 0, checked: true, displayName: '满足率', qt: 13 },
        { name: '满足', value: 1, displayName: '满足', qt: 11 },
        { name: '未满足', value: 2, displayName: '未满足', qt: 12 }
    ]},  
    isSelectPop: false,//下拉选择列表(产成品,半成品,原材料)
    isSelectPop2: false,//下拉选择列表
    scrollLeft: 0,
    scrollLeft2: 0,
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    loadState: 0,//加载状态  1:继续下拉加载更多,2:加载中,3:没有更多了
    isError: false,//加载失败
    popSelect: null,  //下拉选择列表(齐套率,满足率)  
    dataItem: 1, //当前的统计项 齐套率，满足率，点检
    currentItem: 2,//当前的筛选项
    currentItemQT: 3,//参数类型--半成品4/原材料5,齐套率(1,2,3),满足率(11,12,13),点检21
    dataList: [],//生产齐套异常列表
    dataMain: { item: '产成品', type: '齐套率', rate: null, sumQty: null },//齐套率，满足率，点检率
    bottomId: 0,//底部最后一行Id,用于上拉触底事件
    bottomDt: null,
    searchKey: '',//搜索关键字
    user: null //当前用户
  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;

    this.getRequestData(true);
  },  
  //选择查询条件
  tapcondition: function (e) {
    //类型下拉选择列表
    let pop = this.data.isSelectPop;

    if (e.currentTarget.dataset) {
      let cur = e.currentTarget.dataset.currentItem;
      if (cur == 0){
        this.setData({
          scrollLeft: 0,
          isSelectPop: !pop,
          isSelectPop2: false
        });
      }
      else{
        //避免加载数据时又去服务器取数据
        if (this.data.isLoading)
          return;

        this.setData({
          currentItem: cur,
          scrollLeft: cur>=3 ? cur*20 : 0,
          isSelectPop: false,
          isSelectPop2: false
        });

        this.getRequestData(true, this.goTop);
      }

    }
  },
  //类型下拉选择点击事件
  tapselect: function (e) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //当前的类型(产成品/半成品/原材料)
    let index = e.currentTarget.dataset.value;
    let tySelect = this.data.typeSelect;
    tySelect.index = index;
    tySelect.displayName = tySelect.items[index].name;
    for (let i = 0; i < tySelect.items.length; i++) {
      tySelect.items[i].checked = tySelect.items[i].value == index;
    } 
    this.setData({typeSelect: tySelect});
    //改变currentItemQT值
    if (index == 1)
      this.data.currentItemQT = 4; //半成品
    else if (index == 2)
      this.data.currentItemQT = 5; //原材料
    else{
      if (this.data.dataItem == 1)
        this.data.currentItemQT = this.data.qtSelect.items[this.data.qtSelect.index].qt;//齐套率
      else if (this.data.dataItem == 2)
        this.data.currentItemQT = this.data.mzSelect.items[this.data.mzSelect.index].qt;//满足率
      else if (this.data.dataItem == 3)
        this.data.currentItemQT = 21; //点检
    }
    //延迟隐藏下拉列表
    setTimeout(() => {
      this.setData({ isSelectPop: false });
    }, 200);

    this.data.dataMain.item = this.data.typeSelect.displayName;

    this.getRequestData(true, this.goTop);
  },
  //选择齐套率,满足率,点检率
  tapconditionType: function (e) {
    //非产成品
    if (this.data.typeSelect.index != 0)
      return;

    let pop = this.data.isSelectPop2;
    if (e.currentTarget.dataset) {
      let cur = e.currentTarget.dataset.currentItem2;
      let tag = this.data.dataItem;

      if (cur != 3 && (cur == tag)) {
        let sl = this.data.popSelect;
        if (cur == 1)
          sl = this.data.qtSelect;//齐套率
        else if (cur == 2)
          sl = this.data.mzSelect;//满足率

        this.setData({
          dataItem: cur,
          scrollLeft2: (cur-1) * 30,
          popSelect: sl,
          isSelectPop2: !pop,
          isSelectPop: false,
        });
      }
      else {
        //避免加载数据时又去服务器取数据
        if (this.data.isLoading)
          return;

        this.setData({
          dataItem: cur,
          scrollLeft2: (cur - 1) * 30,
          isSelectPop2: false,
          isSelectPop: false,
        });

        if (this.data.dataItem == 1){
          this.data.currentItemQT = this.data.qtSelect.items[this.data.qtSelect.index].qt;//齐套率
          this.data.dataMain.type = '齐套率';
          this.data.dataMain.rate = null;
          this.data.dataMain.sumQty = null;
        }
        else if (this.data.dataItem == 2){
          this.data.currentItemQT = this.data.mzSelect.items[this.data.mzSelect.index].qt;//满足率
          this.data.dataMain.type ='满足率' ;
          this.data.dataMain.rate = null;
          this.data.dataMain.sumQty = null;
        }
        else if (this.data.dataItem == 3){
          this.data.currentItemQT = 21; //点检
          this.data.dataMain.type = '点检率';
          this.data.dataMain.rate = null;
          this.data.dataMain.sumQty = null;
        }

        this.getRequestData(true, this.goTop);
      }
    }

  },
  //下拉(齐套率,满足率)选择点击事件
  tapPopSelect: function (e) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //改变下拉列表的当前值
    let index = e.currentTarget.dataset.value;
    let popSelect = this.data.popSelect;
    popSelect.index = index;
    popSelect.displayName = popSelect.items[index].displayName;
    for (let i = 0; i < popSelect.items.length; i++) {
      popSelect.items[i].checked = popSelect.items[i].value == index;
    }
    this.setData({ popSelect: popSelect });
    //改变currentItemQT值
    let da = this.data.dataItem;
    if(da == 1){
      this.data.dataMain.type = '齐套率' ;
      this.data.dataMain.rate = null;
      this.data.dataMain.sumQty = null;

      this.data.currentItemQT = popSelect.items[index].qt;
      this.setData({ qtSelect: popSelect });
    }
    else if (da == 2){
      this.data.dataMain.type = '满足率';
      this.data.dataMain.rate = null;
      this.data.dataMain.sumQty = null;

      this.data.currentItemQT = popSelect.items[index].qt;
      this.setData({ mzSelect: popSelect });
    }

    //延迟隐藏下拉列表
    setTimeout(() => {
      this.setData({ isSelectPop2: false });
    }, 200);

    this.getRequestData(true, this.goTop);
  },
  //获取后台数据
  getRequestData: function (again, callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    if (again) {
      //重新获取数据
      this.data.bottomId = 0;
      this.data.bottomDt = null;
      this.data.dataList = [];
      //this.data.dataMain = {};
    }
    else{
      //上拉加载提示
      this.setData({ loadState: 2 });  
    }
    console.log('id:' + this.data.bottomId);
    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = app.globalData.pageSize;
    let org = this.data.user.org;
    let user = this.data.user.code;   
    let type = this.data.currentItem;
    let qt = this.data.currentItemQT;
    let key = this.data.searchKey;
    let begin = this.data.bottomId;
    let beginDt = this.data.bottomDt;
    let url = app.globalData.apiBase + '/api/MOShortage?user=' + user + '&org=' + org + '&type=' + type + '&qt=' + qt + '&key=' + key
            + '&begin=' + begin + '&begindt=' + beginDt + '&pagesize=' + pagesize;
    console.log(url);

    let suc = rd => {
      //更新数据
      let totalData = that.data.dataList;
      let main = that.data.dataMain;
      //判断数据大小是否等于每页数大小，相等则认为还有数据，否则认为没有更多数据
      let hasmore = rd.length == pagesize ? 1 : 0;
      if (rd.length > 0) {
        totalData = totalData.concat(rd);
        if(main.type)
          main.rate = rd[0].suntRate; //齐套率,满足率,点检率
        main.sumQty = rd[0].sumQty;  
        that.data.bottomId = rd[rd.length - 1].mo;//用于触底上拉,取排序字段值
        that.data.bottomDt = rd[rd.length - 1].planStartDate;
      }
      else if (again && main.type)
        main.rate = null;
      
      if(rd.length == 0)
        main.sumQty = null

      that.setData({
        loadState: hasmore,
        dataList: totalData,
        dataMain: main
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
      //执行回调函数
      if (callBack)
        callBack(rd);
    };

    let fl = () => {
      //更新数据
      let main = that.data.dataMain;
      main.type = '';
      main.rate = null;
      main.sumQty = null;

      that.data.bottomId = 0;
      that.data.bottomDt = null;
      that.setData({
        isError: true,
        loadState: 0,
        dataList: [],
        dataMain: main
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      }); 
    };
    
    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  }, 
  //搜索框输入完成
  confirmKeyInput: function(e){
    console.log(e.detail);
    this.data.searchKey = e.detail.value;

    this.getRequestData(true, this.goTop);
  },
  //查询按钮
  searchData: function () {
    this.getRequestData(true, this.goTop);
  },
  //滚动条回到顶部
  goTop: function () {
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //搜索文本改变事件
  bindKeyInput: function (e) {
    console.log(e.detail);
    
    this.data.searchKey = e.detail.value;
  },
  //跳转至明细页
  taprow: function (e) {
    console.log(e.currentTarget.dataset);

    let id = e.currentTarget.dataset.id;
    let mo = e.currentTarget.dataset.mo;
    if (id && mo) {
      wx.navigateTo({
        url: 'mo-shortage-detail/mo-shortage-detail?id=' + id + '-' + mo
      });
    }
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData(true);
  },
  //上拉加载更多
  onReachBottom: function () {
    if (this.data.loadState == 0 || this.data.loadState == 3)
      return;
    this.getRequestData(false);
  }
})