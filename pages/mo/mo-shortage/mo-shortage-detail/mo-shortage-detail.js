const app = getApp()
import { wuxToast, http, formatTime, updateAccountExpire } from '../../../../utils/util.js'

Page({
  data: {
    userType: null,//用户业务员类型
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    isError: false,//加载失败       
    dataMain: {},//齐套异常主表
    dataLine: [],//齐套异常明细
    id: null, //主键
    user: null //当前用户    
  },
  onLoad: function (options) {

    this.data.user = app.globalData.accountInfo;

    this.setData({
      userType: this.data.user.userType
    });

    this.data.id = options.id;
    this.getRequestData(this.data.id);
  },
  //获取后台数据
  getRequestData: function (id) {
    if (!id)
      return;
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    //隐藏加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let org = this.data.user.org;
    let user = this.data.user.code;
    let url = app.globalData.apiBase + '/api/MOShortage/' + id + '/' + user + '/' + org;

    console.log(url);
    
    let suc = rd => {
      //更新数据
      let main = {};
      let rows = [];
      if (rd.length > 0) {
        rows = rd;
        //创建主表
        let line = rd[0];
        main = {
          orgCode: line.orgCode,
          simuDoc: line.simuDoc,
          modifiedOn: line.modifiedOn,
          mo: line.mo,
          moCode: line.moCode,
          productQty: line.productQty,
          unSuitQty: line.unSuitQty,
          totalRcvQty: line.totalRcvQty,
          soCode: line.soCode,
          soLineNo: line.soLineNo,
          cust_Name: line.cust_Name,
          planStartDate: line.planStartDate,
          preEndDate: line.preEndDate,
          actualStartDate: line.actualStartDate,
          actualCompleteDate: line.actualCompleteDate,
          productLineName: line.productLineName
        };
      }
      that.setData({
        dataMain: main,
        dataLine: rows
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        dataMain: {},
        dataLine: []
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });
    };
 
    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading) {
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else {
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress > 0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData(this.data.id);
  },
  //关联采购未到货信息
  tapPurchase: function (e) {
    console.log(e.currentTarget.dataset);

    app.globalData.curPuItem = {};
    //用于传递参数
    let index = e.currentTarget.dataset.currentIndex;
    if (index >= 0 && this.data.dataLine && this.data.dataLine[index]) {
      let cur = this.data.dataLine[index];
      app.globalData.curPuItem = {
        orgCode: cur.orgCode,
        moCode: cur.moCode,
        planStartDate: cur.planStartDate,
        preEndDate: cur.preEndDate,
        item_Code: cur.item_Code,
        item_Name: cur.item_Name,
        item_SPECS: cur.item_SPECS,
        scarceQty: cur.scarceQty
      };

      wx.navigateTo({
        url: '../mo-shortage-purchase/mo-shortage-purchase'
      });
    }

  },
  //责任人回复
  tapreply: function(e){
    console.log(e.currentTarget.dataset);

    app.globalData.curReply = {};
    //用于传递参数
    let index = e.currentTarget.dataset.currentIndex;
    if (index >= 0 && this.data.dataLine && this.data.dataLine[index]){
      let cur = this.data.dataLine[index];
      app.globalData.curReply = {
        id: cur.id,
        simuDoc: cur.simuDoc,
        simu: cur.simu,
        mo: cur.mo,
        moCode: cur.moCode,
        planStartDate: cur.planStartDate,
        preEndDate: cur.preEndDate,
        item_Code: cur.item_Code,
        item_Name: cur.item_Name,
        item_SPECS: cur.item_SPECS
      };

      wx.navigateTo({
        url: '../mo-shortage-reply/mo-shortage-reply'
      });
    }
  },
  //改变计划到货时间
  changePlanDate: function(id,date){
    var line = this.data.dataLine;
    for (let i = 0; i < line.length; i++){
      let item = line[i];
      if(item && item.id == id){
        item.replyPlanDate = date;
        break;
      }
    }
    this.setData({ dataLine: line});
  },
})