<wxs module="filter" src="../../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view wx:if="{{!isError}}">
  <view wx:if="{{dataLine.length>0}}">
    <view class="cust-row">
      <view class="cust-col sg-box-sm">
        <view class="sg-label sg-mwidth4">齐套分析:</view>
        <text selectable="true" class="sg-content">{{dataMain.simuDoc}}</text>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth4">计算时间:</view>
        <text selectable="true" class="sg-content">{{dataMain.modifiedOn}}</text>   
      </view>
    </view>
    <view class="cust-row">
      <view class="cust-col sg-box-sm">
        <view class="sg-label sg-mwidth4">生产订单:</view>
        <text selectable="true" class="sg-content">{{dataMain.moCode}}</text>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth2">产线:</view>
        <text selectable="true" class="sg-content">{{dataMain.productLineName}}</text>   
      </view>
    </view>
    <view class="cust-row" wx:if="{{dataMain.cust_Name}}">
      <view class="sg-label sg-mwidth4">客户简称:</view>
      <text selectable="true" class="sg-content">{{dataMain.cust_Name}}</text>
    </view>  
    <view class="cust-row" wx:if="{{dataMain.soCode}}">
      <view class="sg-label sg-mwidth4">销售订单:</view>
      <text selectable="true" class="sg-content">{{dataMain.soCode}}</text>
    </view>  
    <view class="cust-row">
      <view class="cust-col sg-box-sm">
        <view class="sg-label sg-mwidth4">计划开工:</view>
        <text selectable="true" class="sg-content">{{filter.formatDate(dataMain.planStartDate)}}</text>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth4">计划完工:</view>
        <text selectable="true" class="sg-content">{{filter.formatDate(dataMain.preEndDate)}}</text>   
      </view>
    </view>
    <view class="cust-row">
      <view class="cust-col sg-box-sm">
        <view class="sg-label sg-mwidth4">生产数量:</view>
        <view class="sg-content">{{dataMain.productQty}}</view>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth4">未齐套数:</view>
        <view class="sg-content">{{dataMain.unSuitQty}}</view>   
      </view>
    </view> 
    <view class="cust-row">
      <view class="sg-label sg-mwidth4">累计入库:</view>
      <view class="sg-content">{{dataMain.totalRcvQty}}</view>
    </view>                  
  </view>
  <block wx:for="{{dataLine}}" wx:for-item="item" wx:for-index="idx" wx:key="id">
    <view class="cust-row margin-top-xs">
      <view class="cust-col sg-box">
        <view class="sg-seqnum">{{idx+1}}.</view>
        <view class="sg-label sg-mwidth2">料号:</view>
        <view bindtap="tapPurchase" data-current-index="{{idx}}">
          <text selectable="true" class="sg-content sg-linkheight">{{item.item_Code}}</text>
          <text wx:if="{{item.scarceQty>0}}" class="cuIcon-infofill text-shimge sg-link sg-linkheight"></text>
        </view>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth3">缺料数:</view>
        <view class="sg-content">{{item.scarceQty+item.issueUOM_Name}}</view>   
      </view> 
    </view>
    <view class="cust-row">
      <view class="sg-label-seq sg-mwidth2">品名:</view>
      <text selectable="true" class="sg-content">{{item.item_Name}}</text>
    </view>
    <view class="cust-row">
      <view class="sg-label-seq sg-mwidth2">规格:</view>
      <text selectable="true" class="sg-content">{{item.item_SPECS}}</text>
    </view>
    <view class="cu-form-group" wx:if="{{item.scarceQty>0}}">
      <view class="cu-bar sg-white" style="min-height:60rpx">
        <view class="action">
          <text class="sg-label-seq">计划到货时间:</text>
          <text class="{{item.replyPlanDate>=item.planStartDate ? 'sg-content-red':'sg-content'}}">{{filter.formatDate(item.replyPlanDate)}}</text>
        </view>
        <view class="action">
          <view class="text-shimge text-bold padding-lr-sm" bindtap="tapreply" data-current-index="{{idx}}">
            <text class="cuIcon-message"></text>责任人回复
          </view>
        </view>
      </view>
    </view>
  </block>  
</view>
<view class="cu-load bg-red erro" wx:else></view>  