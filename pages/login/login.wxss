.sg-logo{
  margin-top: 60rpx;
  display: flex;
  align-items: center;
}

.sg-logo image{
  width: 100%;
  height: 250rpx;
}

.cu-form-group .title>text[class*="cuIcon-"] {
  font-size: 50rpx;
}

.cu-form-group input {
	font-size: 32rpx;
  line-height: 60rpx;
  height: 60rpx;
}

.cu-form-group picker .picker {
	line-height: 60rpx;
	font-size: 32rpx;
  text-align: left;
}

.cu-form-group picker::after{
  line-height: 60rpx;
}

.sg-divider{
  font-size: 28rpx;
  color: #808080;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
  width:100%;
  align-self: center;
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.sg-divider:before{
  content:""; 
  margin-right: 50rpx;
  height:1px; 
  border: none;
  border-top:1px solid #aaaaaa;
  flex: 1 1 0rpx;
}
.sg-divider::after{
  content:""; 
  margin-left: 50rpx;
  height:1px; 
  border: none;
  border-top:1px solid #aaaaaa;
  flex: 1 1 0rpx;
}