
//获取应用实例
const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../utils/util.js'

Page({
  data: {
    StatusBar: app.globalData.StatusBar,
    CustomBar: app.globalData.CustomBar,
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    //登录组织
    orgIndex: 0,
    orgPicker: [],
    rePwd:true //记住密码
  },
  //选择组织事件
  orgPickerChange(e) {
    console.log(e.detail.value);

    this.setData({
      orgIndex: e.detail.value
    })
  },
  //账号框提交事件
  confirmAccount: function () {
    let orgPicker = wx.getStorageSync('orgPicker');
    if (orgPicker && orgPicker.length > 0) 
      return;
    this.getOrgData();
  },
  //普工账号登录
  wklogin: function(){

    wx.reLaunch({ url: '../wklogin/wklogin' });
  },
  //登录按钮事件
  formSubmit: function(e) {
    console.log(e.detail.value);

    let that = this;
    let usercode = e.detail.value.code;
    let userpwd = e.detail.value.password;
    let org = this.data.orgPicker.length>0 && this.data.orgIndex>=0 ? this.data.orgPicker[this.data.orgIndex].code : null;
    //重置帐号信息
    app.globalData.accountInfo = null;
    wx.setStorageSync('accountInfo', app.globalData.accountInfo);

    if (!usercode){
      wuxToast('请输入帐号');
      return;
    }
    if (!org){
      wuxToast('请选择组织');
      return;
    }

    wx.showLoading({title: '登录中',mask: true});
    //登录校验
    let url = app.globalData.apiBase + '/api/Login';
    let data = {
      userAccount: '01\\'+usercode,  //内部系统编码为01
      code: usercode,
      password: userpwd
    };

    let suc = rd => {
      //记住登录账号,组织
      wx.setStorageSync('account', usercode);
      wx.setStorageSync('org', org);
      if (this.data.orgPicker.length > 0){
        wx.setStorageSync('orgPicker', this.data.orgPicker);
        wx.setStorageSync('orgIndex', this.data.orgIndex);
      }
      wx.setStorageSync('accountpwd', null);

      if (rd.userName) {
        //记住密码
        if(that.data.rePwd)
          wx.setStorageSync('accountpwd', userpwd); 

        //获取用户权限
        that.getAccountInfo(rd.userName, org);
      }
      else {
        wx.hideLoading();
        wuxToast(rd.message ? rd.message : '登录失败');
      }
    };

    let fl = () =>{
      wx.hideLoading();
      wuxToast('登录失败,请检查网络是否正常');
    };

    http(url, 'POST', data, suc, fl);
  },
  //获取用户权限后登录
  getAccountInfo: function(user,org){

    //获取权限
    let url = app.globalData.apiBase + '/api/Login?type=&user=' + user + '&org=' + org;
    console.log(url);
    let suc = rd =>{
      if (rd.org && rd.menuList && rd.menuList.length > 0){
        app.globalData.accountInfo = rd;
        //设置当前登录组织
        app.globalData.accountInfo.org = rd.org;
        //记住当前用户信息
        wx.setStorageSync('accountInfo', app.globalData.accountInfo);

        wx.hideLoading();
        wx.reLaunch({ url: '../index/index' });
      }
      else if (!rd.org){
        wx.hideLoading();
        wuxToast('登录失败,无当前组织权限');
      }
      else{
        wx.hideLoading();
        wuxToast('登录失败,无功能菜单权限');
      }
    };

    let fl = () => {
      wx.hideLoading();
      wuxToast('登录失败');
    };

    http(url, 'GET', null, suc, fl);
  },
  //获取组织数据
  getOrgData: function(){
    let that = this;
    let url = app.globalData.apiBase + '/api/Org';
    console.log(url);
    let suc = rd => {
      let org = wx.getStorageSync('org');
      let index = 0;
      //当前组织在数组中的index
      if (rd.length > 0) {
        for (let i = 0; i < rd.length; i++) {
          if (rd[i].code == org) {
            index = i;
            break;
          }
        }
      }

      that.setData({
        orgPicker: rd,
        orgIndex: index
      });
    };
    http(url, 'GET', null, suc, null);
  },
  //预置基础数据
  presetData: function(){
    //登录账号
    let account = wx.getStorageSync('account');
    let pwd = wx.getStorageSync('accountpwd');
    let re = true;

    if(pwd == null || pwd == undefined){
      pwd = '';
      re = false;
    }

    this.setData({
      account: account,
      accountPwd: pwd,
      rePwd: re
    });
    //登录组织
    let orgPicker = wx.getStorageSync('orgPicker');
    let orgIndex = wx.getStorageSync('orgIndex');
    if (orgPicker && orgPicker.length>0){
      this.setData({
        orgPicker: orgPicker,
        orgIndex: orgIndex
      });
    }

    this.getOrgData();
  },
  onShow:function () {

    this.presetData();
  },
  //加载事件
  onLoad: function () {
    
    // //微信用户
    // if (app.globalData.userInfo) {
    //   this.setData({
    //     userInfo: app.globalData.userInfo,
    //     hasUserInfo: true
    //   })
    // } else if (this.data.canIUse){
    //   // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
    //   // 所以此处加入 callback 以防止这种情况
    //   app.userInfoReadyCallback = res => {
    //     this.setData({
    //       userInfo: res.userInfo,
    //       hasUserInfo: true
    //     })
    //   }
    // } else {
    //   // 在没有 open-type=getUserInfo 版本的兼容处理
    //   wx.getUserInfo({
    //     success: res => {
    //       app.globalData.userInfo = res.userInfo
    //       this.setData({
    //         userInfo: res.userInfo,
    //         hasUserInfo: true
    //       })
    //     }
    //   })
    // }
  },
  getUserInfo: function(e) {
    console.log(e)
    app.globalData.userInfo = e.detail.userInfo
    this.setData({
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  },
  //转发分享功能
  onShareAppMessage: function () {

  },
 //记住密码 
 checkboxChange:function(e){
  console.log(e.detail.value);

  let values = e.detail.value;
  let rePwd = values!=null && values.length>0;
  this.setData({rePwd: rePwd});
},   
})
