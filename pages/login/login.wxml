<form bindsubmit="formSubmit">
  <view class="flex flex-direction justify-center">
    <view class="sg-logo">
      <image mode="aspectFit" src="/images/shimge.png"/>
    </view>
    <view class="padding">
      <view class="cu-form-group">
        <view class="title"><text class="cuIcon-people"/></view>
        <input placeholder="帐号" placeholder-class="sg-placeholder" value="{{account}}"  bindconfirm="confirmAccount" maxlength="50" cursor-spacing="150rpx" name="code"/>
      </view>
      <view class="cu-form-group">
        <view class="title"><text class="cuIcon-lock"/></view>
        <input password="true" placeholder="密码" placeholder-class="sg-placeholder" value="{{accountPwd}}" maxlength="50" cursor-spacing="150rpx" name="password"/>
      </view>
      <view class="cu-form-group">
        <view class="title"><text class="cuIcon-location"/></view>
        <picker bindchange="orgPickerChange" value="{{orgIndex}}" range="{{orgPicker}}" range-key="name">
          <view class="picker {{orgPicker.length>0 && orgIndex>=0 ? '' : 'sg-placeholder'}}">
            {{orgPicker.length>0 && orgIndex>=0 ? orgPicker[orgIndex].name : '组织'}}
          </view>
        </picker>
      </view>
      <view class="cu-form-group ">
        <checkbox-group bindchange="checkboxChange">
          <label class="checkbox text-lg">
            <checkbox class="shimge padding-left-xl" value="pwd" checked="{{rePwd}}"/> 记住密码
          </label>
        </checkbox-group> 
      </view>       
    </view>
    <view class="padding-lr flex flex-direction">
      <button class="cu-btn lg bg-shimge" form-type="submit" >登 录</button>
      <view class="sg-divider">其他账号登录</view>
      <button bindtap="wklogin" class="cu-btn lines-shimge lg">我是一线员工</button>
    </view>
    <view class="flex justify-center text-red margin-top-lg">
      企业内部人员使用，需管理员关联ERP和OA账号
    </view>
  </view>
</form>
