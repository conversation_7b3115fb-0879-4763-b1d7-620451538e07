// pages/finance/finance-Scan/finance-Scan.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    message:'请扫描发票二维码并确认信息后提交数据',
    Receipt:'',//扫码得到的完整数据，用户不可见
    ReceiptCode:'',//发票代码
    ReceiptNumber:'',//发票号码
    ReceiptDate:'',//开票日期
    ReceiptMoney:'',//发票金额
    ReceiptCheckCode:'',//发票校验码
    textareaAValue:'',//发票备注
    textareaBValue:'',//开票客户
    textareaCValue:'',//发票内容
    isOK:false,
    isNormal:0 //判断扫描的发票是否是规范的 0 规范 1 不规范（强制要求填写备注）
  },
  //备注信息收集
  textareaAInput(e) {
    this.setData({
      textareaAValue: e.detail.value
    })
  },
  //开票客户
  textareaBinput(e){
    this.setData({
      textareaBValue: e.detail.value
    })
  },
  //发票内容
  textareaCinput(e){
    this.setData({
      textareaCValue: e.detail.value
    })
  },
  //扫码功能把设备编号带过来
  btnscan:function(e){
  var that=this
  //微信自带扫码功能
  wx.scanCode({
    onlyFromCamera: true,
    success (res) {
      console.log(res)
      //赋值
      that.setData({ Receipt: res.result});
      //调用一下方法
      that.ReceiptScan();
    }
  });
},
//二维码扫描完成后传参数，按,号拆分数据
ReceiptScan:function(e){
  var that=this;
  //执行此方法把数据都清空一下
  that.setData({
    ReceiptCode:'',//发票代码
    ReceiptNumber:'',//发票号码
    ReceiptDate:'',//开票日期
    ReceiptMoney:'',//发票金额
    ReceiptCheckCode:'',//发票校验码
    textareaAValue:'',//发票备注
    textareaBValue:'',//开票客户
    textareaCValue:'',//发票内容
    isNormal:0,
    message:'请扫描发票二维码并确认信息后提交数据'
  })
  if(that.data.Receipt==''||that.data.Receipt==null){
    wx: wx.showToast({
      title: '二维码解析无数据，请检查!',
      icon: 'none',
      duration: 4000
    })
  }else{
  //传入扫码数据，接收转换回来的数据
  wx.request({
    url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
    data: {
      api: 'ReceiptChange',
      Receipt:that.data.Receipt,
    },
    method: 'POST',
    header: { 'Content-Type': 'application/x-www-form-urlencoded' },
    success: function (res) {
      // success
      console.log(res);//打印请求返回的结果
      //因为发票二维码返回的值现在是固定的几个，所以如果res.data.length少于8个，那就不是正规的，要人检查一下
      //因为最后面的字段 随机加密信息 在有些发票上不存在，所以这边第二版把个数限制改成7个
      if(res.data.length<7){
        wx: wx.showToast({
          title: '扫描成功，但该发票二维码信息不规范，请在下方备注发票类型后提交!',
          icon: 'none',
          duration: 4000
        })
        that.setData({
          message:'二维码信息不规范，请备注发票类型后提交',
          isNormal:1
        })
        }else{
          if(res.data[4][0].split(".")[0].length>10){ //区域链二维码规则有变 纳税人识别码和开票金额互换了 这边防止以后还出现这种情况 对本来开票金额的位置做长度校验 不通过的转到让步写入 纳税人是18位 金额怎么也不会超过10位数吧
            wx: wx.showToast({
              title: '扫描成功，但该发票二维码信息不规范，请在下方备注发票类型后提交!',
              icon: 'none',
              duration: 4000
            })
            that.setData({
              message:'二维码信息不规范，请备注发票类型后提交',
              isNormal:1
            })
          }else{
            wx: wx.showToast({
              title: '扫描成功，信息确认完毕后请提交!',
              icon: 'none',
              duration: 4000
            })
            that.setData({
              ReceiptCode:res.data[2][0],
              ReceiptNumber:res.data[3][0],
              ReceiptDate:res.data[5][0],
              ReceiptMoney:res.data[4][0],
              ReceiptCheckCode:res.data[6][0]
            })
          }
      }
    },
    fail: function (res) {
      // fail
    },
    complete: function (res) {
      // complete
    }
  })
  }
},
//数据提交按钮，先检验成功后再插入数据
btnsend:function(e){
  var that=this;
  that.setData({
    isOK:true
  })
  if(that.data.Receipt==''||that.data.Receipt==null){
    wx: wx.showToast({
      title: '请先扫码获取数据再提交!',
      icon: 'none',
      duration: 4000
    })
    that.setData({
      isOK:false
    })
  }else if(that.data.isNormal==1 &&(that.data.textareaAValue==''|| that.data.textareaAValue==null)){
    wx: wx.showToast({
      title: '二维码信息不规范，请备注发票类型后提交!',
      icon: 'none',
      duration: 4000
    })
    that.setData({
      isOK:false
    })
  }else{
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'ReceiptSearch',
        Receipt:that.data.Receipt,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){//没有返回数据的话说明这单发票不存在，可以插入
          console.log('可以提交')
          console.log(that.data.ReceiptMoney)
          wx.request({
            url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
            data: {
              api: 'ReceiptInsert',
              Receipt:that.data.Receipt,
              ReceiptCode:that.data.ReceiptCode,
              ReceiptNumber:that.data.ReceiptNumber,
              ReceiptDate:that.data.ReceiptDate,
              ReceiptMoney:that.data.ReceiptMoney,
              ReceiptCheckCode:that.data.ReceiptCheckCode,
              Memo:that.data.textareaAValue,
              ReceiptCustomer:that.data.textareaBValue,
              ReceiptUse:that.data.textareaCValue,
              ScanPerson:app.globalData.accountInfo.name,
              ScanPersonOrg:app.globalData.accountInfo.org
            },
            method: 'POST',
            header: { 'Content-Type': 'application/x-www-form-urlencoded' },
            success: function (res) {
              // success
              console.log(res);//打印请求返回的结果
              if(res.data=='提交成功'){
                wx: wx.showToast({
                  title: '发票信息记录成功!',
                  icon: 'none',
                  duration: 4000
                })
                //插入完成后把变量都重置
                that.setData({
                  Receipt:'',//扫码得到的完整数据，用户不可见
                  ReceiptCode:'',//发票代码
                  ReceiptNumber:'',//发票号码
                  ReceiptDate:'',//开票日期
                  ReceiptMoney:'',//发票金额
                  ReceiptCheckCode:'',//发票校验码
                  textareaAValue:'',//发票备注
                  textareaBValue:'',//开票客户
                  textareaCValue:'',//发票内容
                  isOK:false,
                  isNormal:0,
                  message:'请扫描发票二维码并确认信息后提交数据'
                })
              }
              else if(res.data=='提交失败'){
                wx: wx.showToast({
                  title: '发票信息提交失败!',
                  icon: 'none',
                  duration: 4000
                })
                that.setData({
                  isOK:false
                })               
              }
            },
            fail: function (res) {
              // fail
            },
            complete: function (res) {
              // complete
            }
          })
          }else{//有的话说明当前发票已存在
          wx: wx.showToast({
            title: '提交失败,当前发票已记录!',
            icon: 'none',
            duration: 4000
          })
          that.setData({
            isOK:false
          })
        }
      },
      fail: function (res) {
        // fail
      },
      complete: function (res) {
        // complete
      }
    })
  }
},
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  }
})