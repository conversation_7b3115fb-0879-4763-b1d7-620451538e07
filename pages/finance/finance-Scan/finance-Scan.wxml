<!--pages/finance/finance-Scan/finance-Scan.wxml-->
  <view class="cu-bar bg-white">
  <view class="action">
    <text class="cuIcon-title text-green"></text>
    <text class="text-xl text-bold text-black">发票扫描</text>
  </view>
  <view class="action">
    <button bindtap="btnscan" class="cu-btn bg-shimge shadow-blur round" type="">扫码</button>
  </view>
  </view>

<view class="cu-form-group" style="border-top: 0rpx">
<view class="padding-xs flex align-center">
<view class="cuIcon-title text-blue"/>
<view class="sg-label sg-mwidth4" style="font-size:33rpx">{{message}}</view>
</view>
</view>

  <view class="cu-bar bg-white">
  <view class="action">
    <text class="cuIcon-title text-green"></text>
    <text class="text-xl text-bold text-black">发票基本信息</text>
  </view>
  <view class="action">
  <button bindtap="btnsend" class="cu-btn bg-shimge shadow-blur round" type="" disabled="{{isOK}}">数据提交</button>
  </view>
  </view>

<view class="cu-form-group">
  <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx)">发票代码:</view>
  <text selectable="true" class="sg-content">{{ReceiptCode}}</text>
</view>
<view class="cu-form-group">
  <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx)">发票号码:</view>
  <text selectable="true" class="sg-content">{{ReceiptNumber}}</text>
</view>
<view class="cu-form-group">
  <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx)">开票日期:</view>
  <text selectable="true" class="sg-content">{{ReceiptDate}}</text>
</view>
<view class="cu-form-group">
  <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx)">发票金额:</view>
  <text selectable="true" class="sg-content">{{ReceiptMoney}}</text>
</view>
<view class="cu-form-group">
  <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx)">发票校验码:</view>
  <text selectable="true" class="sg-content">{{ReceiptCheckCode}}</text>
</view>

<view class="cu-form-group">
  <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx)">开票客户:</view>
  <input selectable="true" class="sg-content" placeholder="填写开票客户(可不填)" bindinput="textareaBinput" value="{{textareaBValue}}"></input>
</view>

<view class="cu-form-group">
  <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx)">发票内容:</view>
  <input selectable="true" class="sg-content" placeholder="填写发票内容(可不填)" bindinput="textareaCinput" value="{{textareaCValue}}"></input>
</view>

<view class="cu-form-group" style="border-top: 0rpx">
    <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx);">发票备注:</view>
</view>
	<view class="cu-form-group" style="border-top: 0rpx;height:150rpx">
	<textarea maxlength="-1" value="{{textareaAValue}}" bindinput="textareaAInput" placeholder="填写发票备注" style="height:200rpx;border: 1px dashed gray;text-color:gray;padding:5rpx;font-size:28rpx"></textarea>
</view>