// pages/finance/finance-Scan-Common/finance-Scan-Common.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    message:'请扫描发票二维码检验是否已录入',
    Receipt:'',//扫码得到的完整数据，用户不可见
    ReceiptScanPerson:'',//财务扫描人员
    ReceiptScanDate:'',//扫描时间
    isHide:true, //控制财务那两条信息是否展示
    isLoading:true, //加载数据
    ll:0 //控制图片的描述信息
  },
  //扫码功能把设备编号带过来
  btnscan:function(e){
    var that=this
    //微信自带扫码功能
    wx.scanCode({
      onlyFromCamera: true,
      success (res) {
        console.log(res)
        //赋值
        that.setData({ Receipt: res.result});
        //调用一下方法
        that.ReceiptScan();
      }
    });
  },
  //二维码扫描完成后判断是否已导入
  ReceiptScan:function(e){
  var that=this;
  //执行此方法把数据都清空一下
  that.setData({
    ReceiptScanPerson:'',//财务扫描人员
    ReceiptScanDate:'',//扫描时间
    message:'请扫描发票二维码检验是否已录入',
    isHide:true,
    isLoading:false,
    ll:0
  })
  if(that.data.Receipt==''||that.data.Receipt==null){
    wx: wx.showToast({
      title: '二维码解析无数据，请检查!',
      icon: 'none',
      duration: 4000
    })
    that.setData({
      isLoading:true
    })
  }else{
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'ReceiptSearch',
        Receipt:that.data.Receipt,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if(res.data.length==0){//没有返回数据的话说明这单发票不存在
          that.setData({
            ll:2,
            isLoading:true
          })
        }else{//有的话说明当前发票已存在
          that.setData({
            ReceiptScanPerson:res.data[0][0],
            ReceiptScanDate:res.data[0][1],
            isHide:false,
            isLoading:true,
            ll:1
          })
        }
      },
      fail: function (res) {
        // fail
        that.setData({
          isLoading:true
        })
      },
      complete: function (res) {
        // complete
        that.setData({
          isLoading:true
        })
      }
    })
  }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  }
})