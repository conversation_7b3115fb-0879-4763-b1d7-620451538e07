<!--pages/finance/finance-Scan-Common/finance-Scan-Common.wxml-->
<loading hidden="{{isLoading}}">
 加载中...
</loading>

<view class="cu-bar bg-white">
  <view class="action">
    <text class="cuIcon-title text-green"></text>
    <text class="text-xl text-bold text-black">发票扫描</text>
  </view>
  <view class="action">
    <button bindtap="btnscan" class="cu-btn bg-shimge shadow-blur round" type="">扫码</button>
  </view>
  </view>

<view class="cu-form-group" style="border-top: 0rpx">
<view class="padding-xs flex align-center">
<view class="cuIcon-title text-blue"/>
<view class="sg-label sg-mwidth4" style="font-size:33rpx">{{message}}</view>
</view>
</view>

<view class="cu-bar bg-white">
  <view class="action">
    <text class="cuIcon-title text-green"></text>
    <text class="text-xl text-bold text-black">发票校验结果</text>
  </view>
</view>

<view hidden="{{isHide}}">
<view class="cu-form-group">
  <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx)">财务扫描人员:</view>
  <text selectable="true" class="sg-content">{{ReceiptScanPerson}}</text>
</view>
<view class="cu-form-group">
  <view class="sg-label sg-mwidth4" style="min-width: calc(4.5em + 70rpx)">扫描时间:</view>
  <text selectable="true" class="sg-content">{{ReceiptScanDate}}</text>
</view>
</view>

<view wx:if="{{ll==0}}">
  <van-empty image="search" description="请扫码" />
</view>
<view wx:if="{{ll==1}}">
  <van-empty description="发票已录入" />
</view>
<view wx:if="{{ll==2}}">
  <van-empty image="error" description="发票未录入" />
</view>