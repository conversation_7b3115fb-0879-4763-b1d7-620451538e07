const app = getApp();

Page({
  data: {
    menus: [],
    arrangeQty: 0,
  },
  onShow: function () {
    //每次显示界面就刷新一下维修安排的红点
    var that = this;
    if (app.globalData.accountInfo && app.globalData.accountInfo.org) {
      wx.request({
        url: app.globalData.apiBase + "/SHIMGERepairServer.ashx",
        data: {
          api: "EquipmentsearchCount",
          LoginOrg: app.globalData.accountInfo.org,
        },
        method: "POST",
        header: { "Content-Type": "application/x-www-form-urlencoded" },
        success: function (res) {
          // success
          console.log(res); //打印请求返回的结果
          if (res.data.length == 0) {
            that.setData({
              arrangeQty: 0,
            });
          } else {
            that.setData({
              arrangeQty: res.data[0][0],
            });
          }
        },
        fail: function (res) {
          // fail
        },
        complete: function (res) {
          // complete
        },
      });
    }
  },
  onLoad: function (options) {
    let user = app.globalData.accountInfo;

    console.log("当前用户:", user);

    //菜单权限控制
    let menuset = new Map();
    if (user && user.menuList) {
      for (let m in user.menuList) {
        menuset.set(user.menuList[m].menuCode, user.menuList[m]);
      }
    }
    let menus = [];

    //菜单对应的图标,路径数组
    let menusData = require("../../data/data.js").menuList;

    //将菜单权限转换为用于界面布局的数组
    for (let i = 0; i < menusData.length; i++) {
      let mod = {
        module: menusData[i].module,
        menuitems: [],
      };

      let itemsData = menusData[i].menuitems;
      for (let j = 0; j < itemsData.length; j++) {
        let item = itemsData[j];
        if (menuset.has(item.code)) {
          mod.menuitems.push(item);
        }
      }

      if (mod.menuitems.length > 0) {
        menus.push(mod);
      }
    }

    this.setData({
      menus: menus,
    });
  },
  //点击菜单事件
  tapmenu: function (e) {
    console.log(e.currentTarget.dataset);

    let pageurl = e.currentTarget.dataset.menuPage;
    if (pageurl) {
      wx.navigateTo({
        url: pageurl,
      });
    }
  },
});
