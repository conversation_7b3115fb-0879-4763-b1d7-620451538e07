<block wx:for="{{menus}}" wx:for-item="menu" wx:key="module">
  <block wx:if="{{menu.menuitems.length > 0}}">
    <view class="cu-bar bg-white solid-bottom margin-top-xs">
      <view class="action">
        <text class="cuIcon-titles text-green"></text>
        <text class="text-xl text-bold">{{menu.module}}</text>
      </view>
    </view>
    <view class="cu-list grid col-4">
      <block wx:for="{{menu.menuitems}}" wx:for-item="menuitem" wx:key="code">
        <view class="cu-item" bindtap="tapmenu" data-menu-page="{{menuitem.page}}">
          <image class="sg-menuicon" mode="aspectFit" src="{{menuitem.image}}"/>
          <text class="num" wx:if="{{menuitem.title=='维修安排'}}" style="color:#fff;font-size:20rpx">{{arrangeQty}}</text>
          <text>{{menuitem.title}}</text>
        </view>
      </block>
    </view>
  </block>    
</block>