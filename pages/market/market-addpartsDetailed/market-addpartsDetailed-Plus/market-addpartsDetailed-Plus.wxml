<!--pages/market/market-addpartsDetailed/market-addpartsDetailed-Plus/market-addpartsDetailed-Plus.wxml-->
<loading hidden="{{loadingHidden}}">
 加载中...
</loading>

<view class="sg-fixed">
  <view class="cu-bar search bg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索料号、品名、规格" confirm-type="search" bindinput="onKeyChange" bindconfirm="searchData" value="{{searchKey}}"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>
</view>

<view wx:if="{{empty==0}}">
  <view class="solids-bottom padding-xs flex align-center">
    <view class="flex-sub text-center">
      <view class="solid-bottom text-xsl padding">
        <text class=" cuIcon-bad text-grey"></text>
      </view>
      <view class="padding">未查询到数据</view>
    </view>
  </view>
</view>
<view wx:else>
  <block wx:for="{{postList}}" wx:for-item="item" wx:for-index="idx" wx:key="idx">
  <view style="margin:10px;box-shadow: 10px 10px 10px gainsboro;">
  <view class="cu-form-group" style="min-height:10rpx"><van-tag type="primary">{{item.ronum}}</van-tag><view class="title" style="font-size: 28rpx;">母件料号</view><text style="font-size: 25rpx;flex:1">{{item.Code}}</text></view>
  <view class="cu-form-group" style="min-height:10rpx"><view class="title" style="font-size: 28rpx;">母件名称</view><text style="font-size: 25rpx;flex:1">{{item.Name}}</text></view>
  <view class="cu-form-group" style="min-height:10rpx"><view class="title" style="font-size: 28rpx;">母件规格</view><text style="font-size: 25rpx;flex:1">{{item.SPECS}}</text></view>
  <view hidden="{{item.FREQUENCY==''}}" class="cu-form-group" style="min-height:10rpx"><view class="title" style="font-size: 28rpx;">频率(Hz)</view><text style="font-size: 25rpx;flex:1">{{item.FREQUENCY}}</text></view>
</view>
  </block>
</view>

<view style="text-align:center;padding-top:5px;" hidden="{{downshow}}">
  <view class="cu-load {{'loading'}}"></view>
</view>
<view wx:if="{{postList.length>0}}" style="text-align:center;padding-top:5px" hidden="{{downshow2}}">
  <view class="cu-load {{'over'}}"></view>
</view>
