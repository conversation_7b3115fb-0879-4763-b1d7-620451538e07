// pages/market/market-addpartsDetailed/market-addpartsDetailed-List/market-addpartsDetailed-List.js
const app = getApp();
import {  http } from '../../../../utils/util.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    isLoading: false,//是否正在加载数据
    loadingHidden:true,
    postlist:{},
    empty:0,
    Count:0, //购物车这边的总计 默认先0
    ALL:0 //0表示可以全选 1表示可以全消
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.GetListInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.GetListInfo();
    wx.hideNavigationBarLoading();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  //获取列表数据
  GetListInfo(){
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //初始化
    this.setData({
    loadingHidden:false,//加载中
    isLoading:true,//加载中禁用控件
    postlist:{}
    })
    var that=this
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaPartsOrderDetails',
        UpdateUserCode:app.globalData.accountInfo.code,
        UpdateUserName:app.globalData.accountInfo.name
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
            that.setData({
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
            })
          }else{
          that.setData({
            postlist:res.data,
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
          })
          //展示列表，0则展示没有数据页面，其余无所谓
          that.setData({
          empty:Object.keys(that.data.postlist).length
          })
          //计算总分
          that.CountSum();
          }
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
            loadingHidden:true
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
          loadingHidden:true
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //单选
  onChange:function(event){
    var that=this;
    let index=event.currentTarget.dataset.index;
    let cCheck='false';
    if(that.data.postlist[index][5]=='false'){
      that.data.postlist[index][5]='true';
      that.setData({postlist:that.data.postlist})
      cCheck='true'
    }else{
      that.data.postlist[index][5]='false';
      that.setData({postlist:that.data.postlist})
      cCheck='false'
    }
    console.log('单选:'+index+'发生了改变,值变为'+cCheck);
    //调用算总分
    that.CountSum();
  },
  //计算总分
  CountSum:function(){
    var that=this;
    if(!that.data.postlist){
      return;
    }else{
      let Count2=0;
      for (let item in that.data.postlist) {
        if(that.data.postlist[item][5]=='true'){
          let qty = that.data.postlist[item][3];
          let Price = that.data.postlist[item][4];
          Count2=Count2+(qty*Price);
        }
      }
      //结束后再把价格附上 保留了两位小数
      that.setData({
        Count:Count2.toFixed(2)
      })
    }
  },
  //增加
  stepperplus:function(e){
    console.log(e)
    //获取选中行的信息，因为是用的外面配件的同一个接口 所以这边也要整理成那个格式
    var that = this;
    var carts=[];

    var lines = e.currentTarget.dataset.item
    let row = {};
    row.Item_Code = lines[0];
    row.Item_Name = lines[1];
    row.Item_Specs = lines[2];
    row.Qty = '';//后台直接+1 所以不用传
    row.Price = lines[4];
    row.Prod_Code = ''; //这边不用更新所以传空也无所谓
    row.Prod_Specs = '';//同上
    row.ItemExplodeCode_Code = '';//同上
    row.UpdateUserCode = app.globalData.accountInfo.code
    row.UpdateUserName = app.globalData.accountInfo.name;
    carts.push(row);

    if(carts==null || carts.length==0)
    return;

    //把json数组转成文本传给接口
    var stringcarts=JSON.stringify(carts);
    console.log(stringcarts);

    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
    return;

    //改后台之前，先把这边本地的列表上的数据更新一下，这样就可以算了，直接重新获取列表的话 被把勾选的操作给清掉
    that.data.postlist[e.currentTarget.dataset.index][3]=(parseInt(that.data.postlist[e.currentTarget.dataset.index][3])+1).toString();
    that.setData({postlist:that.data.postlist})

    //初始化
    this.setData({
    loadingHidden:true,//加载中
    isLoading:true//加载中禁用控件
    })
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaPartsInsert',
        Carts:stringcarts,
        Type:'2'
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded'},
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
          }else if(res.data=='提交成功'){
            that.CountSum(); //计算总分
          }else if(res.data=='提交失败'){
            wx.showToast({title: '增加失败',icon: 'none',duration: 2000});
          }
          that.setData({
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
          })
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
            loadingHidden:true
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
          loadingHidden:true
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //减少
  steeperminus:function(e){
    console.log(e)
    //获取选中行的信息，因为是用的外面配件的同一个接口 所以这边也要整理成那个格式
    var that = this;
    var carts=[];

    var lines = e.currentTarget.dataset.item
    let row = {};
    row.Item_Code = lines[0];
    row.Item_Name = lines[1];
    row.Item_Specs = lines[2];
    row.Qty = '';//后台直接-1 所以不用传
    row.Price = lines[4];
    row.Prod_Code = ''; //这边不用更新所以传空也无所谓
    row.Prod_Specs = '';//同上
    row.ItemExplodeCode_Code = '';//同上
    row.UpdateUserCode = app.globalData.accountInfo.code
    row.UpdateUserName = app.globalData.accountInfo.name;
    carts.push(row);

    if(carts==null || carts.length==0)
    return;

    //把json数组转成文本传给接口
    var stringcarts=JSON.stringify(carts);
    console.log(stringcarts);

    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
    return;

    //改后台之前，先把这边本地的列表上的数据更新一下，这样就可以算了，直接重新获取列表的话 被把勾选的操作给清掉
    that.data.postlist[e.currentTarget.dataset.index][3]=(parseInt(that.data.postlist[e.currentTarget.dataset.index][3])-1).toString();
    that.setData({postlist:that.data.postlist})

    //初始化
    this.setData({
    loadingHidden:true,//加载中
    isLoading:true//加载中禁用控件
    })
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaPartsInsert',
        Carts:stringcarts,
        Type:'3'
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded'},
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
          }else if(res.data=='提交成功'){
            that.CountSum(); //计算总分
          }else if(res.data=='提交失败'){
            wx.showToast({title: '减少失败',icon: 'none',duration: 2000});
          }
          that.setData({
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
          })
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
            loadingHidden:true
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
          loadingHidden:true
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //输入框失焦
  steeperblur:function(e){
    console.log(e)
    //获取选中行的信息，因为是用的外面配件的同一个接口 所以这边也要整理成那个格式
    var that = this;
    var carts=[];

    var lines = e.currentTarget.dataset.item
    let row = {};
    row.Item_Code = lines[0];
    row.Item_Name = lines[1];
    row.Item_Specs = lines[2];
    row.Qty = e.detail.value; //失焦后的数字
    row.Price = lines[4];
    row.Prod_Code = ''; //这边不用更新所以传空也无所谓
    row.Prod_Specs = '';//同上
    row.ItemExplodeCode_Code = '';//同上
    row.UpdateUserCode = app.globalData.accountInfo.code
    row.UpdateUserName = app.globalData.accountInfo.name;
    carts.push(row);

    if(carts==null || carts.length==0)
    return;

    //把json数组转成文本传给接口
    var stringcarts=JSON.stringify(carts);
    console.log(stringcarts);

    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
    return;

    //改后台之前，先把这边本地的列表上的数据更新一下，这样就可以算了，直接重新获取列表的话 被把勾选的操作给清掉
    that.data.postlist[e.currentTarget.dataset.index][3]=e.detail.value;
    that.setData({postlist:that.data.postlist})
    
    //初始化
    this.setData({
    loadingHidden:true,//加载中
    isLoading:true//加载中禁用控件
    })
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaPartsInsert',
        Carts:stringcarts,
        Type:'4'
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded'},
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
          }else if(res.data=='提交成功'){
            that.CountSum(); //计算总分
          }else if(res.data=='提交失败'){
            wx.showToast({title: '增加失败',icon: 'none',duration: 2000});
          }
          that.setData({
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
          })
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
            loadingHidden:true
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
          loadingHidden:true
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //全选按钮
  ChooseAll:function(){
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
    return;

    var that=this;
    var lines = that.data.postlist;

    if(that.data.ALL==0){ //进行全选
      for(var i=0; i< lines.length; i++){
        that.data.postlist[i][5]='true'
      }
      that.setData({postlist:that.data.postlist,ALL:1});
    }else if(that.data.ALL==1){ //进行全消
      for(var i=0; i< lines.length; i++){
        that.data.postlist[i][5]='false'
      }
      that.setData({postlist:that.data.postlist,ALL:0});
    }
    //计算总分
    that.CountSum();
  },

  //生成Excel并打开
  toExcel: function () {
    let that =this;

    let cback = () => {
      wx.hideLoading();
    };

    let ids = []
    for (let item in that.data.postlist) {
      if(that.data.postlist[item][5]=='true'){
        let id = parseInt(that.data.postlist[item][6])
        ids.push(id)
      }
    }
    if(ids.length==0){
      wx.showToast({title: '请勾选记录',icon: 'none',duration: 2000});
      return;
    }
    wx.showLoading({title: '加载附件',mask: true});
    this.getFilePath(ids,cback);
  },
  //获取文件临时路径
  getFilePath: function(ids,callBack){
    let that = this;
    let user = app.globalData.accountInfo.code;

    let url = app.globalData.apiBase + '/api/HWOrder'
    let data = {
      User: user,
      IDS: ids
    }
    console.log(url);

    let suc = rd => {
      if(rd == null || rd == undefined || rd == ''){
        if (callBack)
          callBack();
        wx.showToast({title: '获取文件路径失败',icon: 'none',duration: 2000});
      }
      else{
        that.openGFile(rd,callBack);
      }
    };

    let fl = () => {
      if (callBack)
        callBack();
      wx.showToast({title: '获取文件路径失败',icon: 'none',duration: 2000});
    };
    http(url, 'POST', data, suc, fl);
  },
  getFileName :function(path,ch) {
    var name = path;
    if(!ch){
      ch = '/';
    }
    var flen = path.length;
    var index = path.lastIndexOf(ch);
    if(index>=0 && index+1<flen){
      name = path.substr(index+1);
    }
    return name;
  },  
  //打开附件
  openGFile: function(fpath,callBack){
    let that = this;
    let url = app.globalData.apiBase + fpath;
    console.log('文档路径'+url);
    let name = '/' + that.getFileName(fpath);

    wx.downloadFile({
      url: url,
      filePath: wx.env.USER_DATA_PATH + name, //防止IOS中报错：No tempFilePath
      success: function(res) {
        var filePath = res.filePath

        if(app.globalData.platform=='windows'){
          wx.saveFileToDisk({
            filePath: filePath,
            success(res) {
              console.log(res)
            },
            fail(res) {
              console.error(res)
            }
          })
        }
        else{
          wx.openDocument({
            showMenu: true,
            filePath: filePath,
            complete: function (res) {
              console.log('打开文档'+filePath);
            }
          });
        }

        if (callBack)
          callBack();
      },
      fail: function(res){
        if (callBack)
          callBack();
        wx.showToast({title: '文件下载失败',icon: 'none',duration: 2000});
      },      
    })
  }, 
  //删除行
  itemdelete:function(e){
    var that=this;
    //避免加载数据时又去服务器取数据
    if (that.data.isLoading)
    return;
    
    wx.showModal({
    title:'删除',
    content:'确定删除该行吗?',
    success:function(res){
    if(res.confirm){
    console.log('确认删除');
    //初始化
    that.setData({
    loadingHidden:false,//加载中
    isLoading:true//加载中禁用控件
    })

    let index=e.currentTarget.dataset.index;//对象位置
    let itemcode=e.currentTarget.dataset.item[0] //子键料号
    let qty=e.currentTarget.dataset.item[3] //数量
    
    if(itemcode!=''&&itemcode!=null&&qty!=''&&qty!=null){
    //加载数据
    wx.request({
    url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
    data: {
      api: 'InSideOverSeaPartsOrderDelete',
      UpdateUserCode:app.globalData.accountInfo.code,
      UpdateUserName:app.globalData.accountInfo.name,
      ItemCode:itemcode,
      Qty:qty
    },
    method: 'POST',
    header: { 'Content-Type': 'application/x-www-form-urlencoded' },
    success: function (res) {
      // success
      console.log(res);//打印请求返回的结果
      if (res.statusCode == '200' || res.statusCode == '204'){
        if(res.data.length==0){
        }else if(res.data=='提交成功'){
          //删除本地的数据快 所以等后台删除成功了后再执行 失败不执行
          that.data.postlist.splice(index,1)
          that.setData({postlist:that.data.postlist})
          wx.showToast({title: '删除成功',icon: 'none',duration: 2000});
          //展示列表，0则展示没有数据页面，其余无所谓
          that.setData({
          empty:Object.keys(that.data.postlist).length
          })
          that.CountSum(); //计算总分
        }else if(res.data=='提交失败'){
          wx.showToast({title: '删除失败',icon: 'none',duration: 2000});
        }
        that.setData({
          loadingHidden:true,//隐藏加载中弹窗
          isLoading:false //按钮启用
        })
      }
      else {
        console.log('FailstatusCode:'+res.statusCode);
        that.setData({
          isLoading:false,
          loadingHidden:true
        })
    //判断是否网络问题
    wx.getNetworkType({
      success(res) {
        let networkType = res.networkType;
        if (networkType == 'none')
          wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
        else
          wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
      }
    });
      }
    },
    fail: function (res) {
      // fail
      console.log(res.data);
      that.setData({
        isLoading:false,
        loadingHidden:true
      })
    //判断是否网络问题
    wx.getNetworkType({
      success(res) {
        let networkType = res.networkType;
        if (networkType == 'none')
          wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
        else
          wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
      }
    });
    },
    complete: function (res) {
      // complete
    }
    })
    }
      }else if(res.cancel){
        console.log('取消删除')
      }
    }
  })
  },
  //全删除
  toAllDel:function(e){
    //前端的批量删除
    var that=this;
    let newArr=[] //前台重新绑定的数组
    let newsendArr=[] //传入后台的数组
    let delstring=''
    //没被选中 最后重新绑定展示的
    newArr=this.data.postlist.filter(item=>{
      return item[5]==="false"
    })
    //被选中 要批量删除的
    newsendArr=this.data.postlist.filter(item=>{
      return item[5]==="true"
    })
    if(newsendArr.length>0){
      //循环生成传入接口的字符串
      for(var i=0;i<newsendArr.length;i++){
        delstring=delstring+",'"+newsendArr[i][0]+'-'+newsendArr[i][3]+"'"
      }
      if(delstring.length>0){
        delstring=delstring.substr(1,delstring.length);
      }

    //经过上面这些筛选后delstring还有数据说明是有要删除的东西存在 调用批量删除的接口
    //避免加载数据时又去服务器取数据
    if (that.data.isLoading)
    return;
    
    wx.showModal({
    title:'批量删除',
    content:'确定批量删除吗?',
    success:function(res){
    if(res.confirm){
    console.log('确认删除');
    //初始化
    that.setData({
    loadingHidden:false,//加载中
    isLoading:true//加载中禁用控件
    })
    //调用接口
    wx.request({
    url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
    data: {
      api: 'InSideOverSeaPartsOrderDeleteAll',
      UpdateUserCode:app.globalData.accountInfo.code,
      UpdateUserName:app.globalData.accountInfo.name,
      ItemInfo:delstring
    },
    method: 'POST',
    header: { 'Content-Type': 'application/x-www-form-urlencoded' },
    success: function (res) {
      // success
      console.log(res);//打印请求返回的结果
      if (res.statusCode == '200' || res.statusCode == '204'){
        if(res.data.length==0){
        }else if(res.data=='提交成功'){
          //删除本地的数据快 所以等后台删除成功了后再执行 失败不执行
          that.setData({
            postlist:newArr //前台重新绑定没被删的数组信息
          })
          wx.showToast({title: '删除成功',icon: 'none',duration: 2000});
          //展示列表，0则展示没有数据页面，其余无所谓
          that.setData({
          empty:Object.keys(that.data.postlist).length
          })
          that.CountSum(); //计算总分
        }else if(res.data=='提交失败'){
          wx.showToast({title: '删除失败',icon: 'none',duration: 2000});
        }
        that.setData({
          loadingHidden:true,//隐藏加载中弹窗
          isLoading:false //按钮启用
        })
      }
      else {
        console.log('FailstatusCode:'+res.statusCode);
        that.setData({
          isLoading:false,
          loadingHidden:true
        })
    //判断是否网络问题
    wx.getNetworkType({
      success(res) {
        let networkType = res.networkType;
        if (networkType == 'none')
          wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
        else
          wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
      }
    });
      }
    },
    fail: function (res) {
      // fail
      console.log(res.data);
      that.setData({
        isLoading:false,
        loadingHidden:true
      })
    //判断是否网络问题
    wx.getNetworkType({
      success(res) {
        let networkType = res.networkType;
        if (networkType == 'none')
          wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
        else
          wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
      }
    });
    },
    complete: function (res) {
      // complete
    }
    })
      }else if(res.cancel){
        console.log('取消删除')
      }
    }
  })
  }else{
    wx.showToast({title: '请先勾选需要删除的项',icon: 'none',duration: 2000});
  }
  }
})