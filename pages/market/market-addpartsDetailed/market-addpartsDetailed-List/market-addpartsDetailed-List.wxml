<!--pages/market/market-addpartsDetailed/market-addpartsDetailed-List/market-addpartsDetailed-List.wxml-->
<loading hidden="{{loadingHidden}}">
 加载中...
</loading>

<view wx:if="{{empty==0}}">
  <view class="solids-bottom padding-xs flex align-center">
    <view class="flex-sub text-center">
      <view class="solid-bottom text-xsl padding">
        <text class=" cuIcon-bad text-grey"></text>
      </view>
      <view class="padding">订单为空</view>
    </view>
  </view>
</view>
<view wx:else>
  <block wx:for="{{postlist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx">
<view style="display: flex; flex-direction: row;margin: 5px 10px 1px 10px;">

<view style="display: flex;justify-content: center;align-items: center;flex: 1">
  <checkbox-group bindchange="onChange" data-index="{{idx}}">
  <checkbox checked="{{item[5]=='false'?false:true}}" value="{{idx}}" data-index="{{idx}}"/>
  </checkbox-group>
 </view>

 <view style="display: flex;flex-direction: column;flex: 3" data-index="{{idx}}" bindtap="onChange">
  <view style="display: flex;flex-direction: column">
   <view class="txt_headview"><text class="txt_head">料号:</text><text class="txt_body">{{item[0]}}</text></view>
   <view class="txt_headview"><text class="txt_head">品名:</text><text class="txt_body">{{item[1]}}</text></view>
   <view class="txt_headview"><text class="txt_head">规格:</text><text class="txt_body">{{item[2]}}</text></view>
   <view class="txt_headview"><text class="txt_head">价格:</text><text class="txt_body">{{item[4]==0?'':'$'+item[4]}}</text></view>
 </view>
</view>

<view style="display: flex;flex-direction: column;justify-content: center;align-items: center">
  <van-stepper disabled="{{isLoading}}" value="{{item[3]}}" data-index="{{idx}}" data-item="{{item}}" step="1" min="1" integer long-press="false" input-width="30px" button-size="25px" bind:plus="stepperplus" bind:minus="steeperminus" bind:blur="steeperblur"/>
  <view class='order acea-row row-middle' style="margin-top: 20px;">
        <form bindsubmit="itemdelete" data-item="{{item}}" data-index="{{idx}}">
          <button class='cu-btn' style="font-weight: normal;font-size: 12px;color: red;" formType="submit" disabled="{{isLoading}}">删 除</button>
        </form>
  </view>
</view>
</view>
<van-divider customStyle="margin-right:15px;margin-left:15px" />
</block>

<view  class='footer acea-row row-between-wrapper' >
    <view class="down">
    <view style="display: flex;justify-content: flex-start;align-items:center;flex:2">
    <view class='order acea-row row-middle' style="padding-left: 10px;padding-right: 5px;">
      <van-button icon="bulb-o" type="primary" disabled="{{isLoading}}" bind:click="ChooseAll" size="small">{{ALL==0?'全选':'全消'}}</van-button>
    </view>
    <view class='order acea-row row-middle padding-rt' style="padding-left: 5px;padding-right: 5px;">
      <van-button icon="warning-o" type="warning" disabled="{{isLoading}}" bind:click="toAllDel" size="small">批删</van-button>
    </view>
    <view class='order acea-row row-middle padding-rt' style="padding-left: 5px;padding-right: 5px;">
      <van-button icon="down" type="info" disabled="{{isLoading}}" bind:click="toExcel" size="small">生成Excel</van-button>
    </view>
    </view>

    <view style="display: flex;justify-content: flex-start;align-items:center;flex:1">价格总计:
    <text style="color: blue;font-size: 15px;">$ {{Count}}</text>
    </view>

    </view>

</view>
</view>
