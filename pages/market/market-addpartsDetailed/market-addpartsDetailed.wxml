<!--pages/market/market-addpartsDetailed/market-addpartsDetailed.wxml-->
<loading hidden="{{loadingHidden}}">
 加载中...
</loading>

<view class="pmain">
  <image class="bztImg" catchtap="onEnlargeImg" mode="aspectFit" src="{{imgUrl}}" binderror="findError"/>
  <view class="tbhead bg-shimge">
    <view class="basis-p20 padding-xs">序号</view>
    <view class="basis-p40 padding-xs">配件名称</view>
    <view class="basis-p15 padding-xs">单价</view>
    <view class="basis-p25 padding-xs">数量</view>
  </view>

  <block wx:for="{{partsItems}}" wx:key="DescFlexField_PrivateDescSeg3">
    <view class="tbbody" >
      <view class="basis-p20 text-multcut padding-xs">{{item.DescFlexField_PrivateDescSeg3}}</view>
      <view class="basis-p40 text-multcut padding-xs" bindtap="bindBOM" data-index="{{item}}" style="text-decoration-line: underline">{{item.Name}}</view>
      <view class="basis-p15 text-multcut padding-xs">{{item.Price==0?'':item.Price}}</view>
      <view class="basis-p25 text-multcut padding-xs">
        <view class="stepper">  
          <text class="normal" data-index="{{index}}" bindtap="bindMinus">-</text>  
          <input type="number" data-index="{{index}}" bindchange="bindManual" value="{{item.Qty}}" />   
          <text class="normal" data-index="{{index}}" bindtap="bindPlus">+</text>  
        </view> 
      </view>
    </view>
  </block>
</view>


<view  class='footer acea-row row-between-wrapper' >
    <view class="down">
    <view style="display: flex;justify-content: flex-end;align-items:center;">价格总计:
    <text style="color: blue;font-size: 15px;">$ {{Count}}</text>
    </view>

    <view style="display: flex;justify-content: flex-end;align-items:center;flex:1">
    <van-icon name="shopping-cart-o" info="{{CartCount}}" size="27px" bindtap="JumpOrder"/>

    <view class='order acea-row row-middle padding-rt' style="padding-left: 15px;">
        <form bindsubmit="addCart">
          <button class='placeOrder' style="background-color:#009fa8;font-weight: normal;font-size: 15px" formType="submit" disabled="{{isLoading}}">加入订单</button>
        </form>
    </view>
    </view>

    </view>

</view>
