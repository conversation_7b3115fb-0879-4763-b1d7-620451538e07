// pages/market/market-addpartsDetailed/market-addpartsDetailed.js
const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    CartCount:0, //登录账号的配件总数
    isLoading: false,//是否正在加载数据
    loadingHidden:true,
    imgUrl:null, //图片地址
    prodCode: '',//产品料号
    prodSPECS:'',//产品规格
    partsItems: {}, //部件
    Count:0 //价格总计
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      prodCode:options.Code,
      prodSPECS:options.SPECS
    }) ;
    console.log('进来的母件料号:'+this.data.prodCode)
    this.GetListInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
   //公共方法，获取值
  GetListInfo(){
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //初始化
    this.setData({
    loadingHidden:false,//加载中
    isLoading:true,//加载中禁用控件
    partsItems:{}
    })
    var that=this
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaPartsDetails',
        Page:1,
        Search:'',
        Type:2,
        ItemCode:that.data.prodCode
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
            that.setData({
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
            })
          }else{
          that.setData({
            imgUrl:app.globalData.apiBase+'/'+res.data[0]['ImgPath'],
            partsItems:res.data,
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
          })
          //获取当前账号的购物车数量
          that.GetOrderItemNum();
          }
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
            loadingHidden:true
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
          loadingHidden:true
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //数量改变
  bindManual:function(event){
    var that = this;
    var index = event.currentTarget.dataset.index;
    var num = event.detail.value;
    var item = that.data.partsItems[index];

    console.log('数量改变',index)

    if(num)
      item.Qty=parseInt(num);
    else
      item.Qty=0;

    that.setData({partsItems: that.data.partsItems});
    that.CountSum();
  },
  //数量减
  bindMinus:function(event){

    var that = this;
    var index = event.currentTarget.dataset.index;
    var item = that.data.partsItems[index];

    console.log('数量减',index)

    if(item.Qty>0)
      item.Qty=item.Qty-1;

    that.setData({partsItems: that.data.partsItems});
    that.CountSum();
  },
  //数量加
  bindPlus:function(event){
    var that = this;
    var index = event.currentTarget.dataset.index;
    var item = that.data.partsItems[index];

    console.log('数量加',index)

    item.Qty=item.Qty+1;

    that.setData({partsItems: that.data.partsItems});
    that.CountSum();
  },
  //放大图片
  onEnlargeImg: function(){

    var that = this;

    if(!that.data.imgUrl)
      return;

    if(app.globalData.platform=='windows'){
      that.onPCSaveFile();
    }
    else{
      wx.previewImage({
        current: that.data.imgUrl, // 当前显示图片的http链接
        urls: [that.data.imgUrl] // 需要预览的图片http链接列表
      })
    }
      
  },

  //PC端保存文件
  onPCSaveFile:function(){
    var that = this;

    if(this.data.imgloading == true)
      return;
    wx.showLoading({title: '加载附件', mask: true});
    that.setData({imgloading:true})

    let imgname = that.data.partsItems[0]['ImgPath'];
    let url = app.globalData.apiBase+'/'+imgname;
    console.log('文档路径'+url);
    let name = '/' + that.getFileName(imgname);

    wx.downloadFile({
      url: url,
      filePath: wx.env.USER_DATA_PATH + name, //防止IOS中报错：No tempFilePath
      success: function(res) {
        var filePath = res.filePath

        wx.saveFileToDisk({
          filePath: filePath,
          success(res) {
            wx.hideLoading()
            that.setData({imgloading:false})
            console.log(res)
          },
          fail(res) {
            wx.hideLoading()
            that.setData({imgloading:false})
            console.error(res)
          }
        })
      },
      fail: function(res){
        wx.hideLoading()
        wx.showToast({title: '附件下载失败',icon: 'none',duration: 2000});
      },      
    })

  },

  getFileName :function(path,ch) {
    var name = path;
    if(!ch){
      ch = '/';
    }
    var flen = path.length;
    var index = path.lastIndexOf(ch);
    if(index>=0 && index+1<flen){
      name = path.substr(index+1);
    }
    return name;
  },



  //根据子件料号查找母件信息
  bindBOM:function(e){
    console.log('跳转',e);
    let cCode = e.currentTarget.dataset.index.Code;

    var that = this;
    
    wx.navigateTo({
      url: '../../../../market-addpartsDetailed-Plus/market-addpartsDetailed-Plus?cCode='+cCode,
    });

  },
  //计算总分
  CountSum:function(){
    var that=this;
    if(!that.data.partsItems){
      return;
    }else{
      let Count2=0;
      for (let item in that.data.partsItems) {
        let qty = that.data.partsItems[item]['Qty'];
        let Price = that.data.partsItems[item]['Price'];
        Count2=Count2+(qty*Price);
      }
      //结束后再把价格附上 保留了两位小数
      that.setData({
        Count:Count2.toFixed(2)
      })
    }
  },
  //图片加载失败的提示
  findError: function (e) {
    wx.showToast({title: '料品档案维护的爆炸图序列号有误,请联系管理员处理!',icon: 'none',duration: 5000});
  },
  //加入订单按钮
  addCart:function(){
    var that = this;
    var carts=[];

    var lines = that.data.partsItems;
    for(var i=0; i< lines.length; i++){
      if(lines[i].Qty>0){
        let row = {};
        row.Item_Code = lines[i].Code;
        row.Item_Name = lines[i].Name;
        row.Item_Specs = lines[i].SPECS;
        row.Qty = lines[i].Qty;
        row.Price = lines[i].Price;
        row.Prod_Code = that.data.prodCode;
        row.Prod_Specs = that.data.prodSPECS;
        row.ItemExplodeCode_Code = lines[i].DescFlexField_PrivateDescSeg3;
        row.UpdateUserCode = app.globalData.accountInfo.code
        row.UpdateUserName = app.globalData.accountInfo.name;

        carts.push(row);
      }
    }

    if(carts==null || carts.length==0)
    return;

    //把json数组转成文本传给接口
    var stringcarts=JSON.stringify(carts);
    console.log(stringcarts);

    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
    return;

    //初始化
    this.setData({
    loadingHidden:false,//加载中
    isLoading:true//加载中禁用控件
    })
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaPartsInsert',
        Carts:stringcarts,
        Type:'1'
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded'},
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
          }else if(res.data=='提交成功'){
            wx.showToast({title: '已加入订单,点击购物车图标进入查看!',icon: 'none',duration: 3000});
            //加入成功后 配件列表数量清0 价格总计重算 购物车数量图标更新
            that.apReduction();
          }else if(res.data=='提交失败'){
            wx.showToast({title: '添加失败',icon: 'none',duration: 2000});
          }
          that.setData({
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
          })
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
            loadingHidden:true
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
          loadingHidden:true
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //数量归0,订单数量显示重新加载
  apReduction:function(){
    var that=this;
    if(!that.data.partsItems){
      return;
    }else{
      var lines = that.data.partsItems;
      //配件列表数量清0
      for (var i=0; i<lines.length; i++) {
        lines[i].Qty = 0;
      }
      that.setData({
        partsItems: lines
      })
      //价格总计重新计算
      that.CountSum();
      //购物车订单的 数字刷新
      that.GetOrderItemNum();
    }
  },
  //获取当前账号的订单数
  GetOrderItemNum:function(){
    var that=this;
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaPartsOrder',
        UpdateUserCode:app.globalData.accountInfo.code,
        UpdateUserName:app.globalData.accountInfo.name
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
          }else{
          that.setData({
            CartCount:res.data[0][0]
          })
          console.log('实时数量:'+that.data.CartCount);
          }
        }
        else {
      console.log('FailstatusCode:'+res.statusCode);
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      }
      },
      fail: function (res) {
      // fail
      console.log(res.data);
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
      // complete
      }
    })
  },
  //跳转到订单列表
  JumpOrder:function(){
    var that = this;

    wx.navigateTo({
      url: '../../../../market-addpartsDetailed-List/market-addpartsDetailed-List',
    });
  },
})