page{
  padding-bottom: 120rpx;
  background-color: white;
}
 
.pmain
{
  display: flex;
  flex-direction: column;
}

.bztImg
{
  width: 100%;
  height: 350rpx;  
}

.tbhead
{
  display: flex;
  align-items: center;
  text-align: center;
  line-height: 1.6;
  font-size: 32rpx;
}

.tbbody
{
  display: flex;
  align-items: center;
  font-size: 28rpx;
  min-height: 80rpx;
  margin-left: 8rpx;
  margin-right: 8rpx;
}

.tbfoot
{
	display: flex;
	position: relative;
	align-items: center;
	min-height: 80rpx;
  justify-content: space-between;
	position: fixed;
	width: 100%;
	bottom: 0;
	z-index: 90;
	box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.tbsubmit
{
	align-items: center;
	display: flex;
	justify-content: center;
	text-align: center;
	position: relative;
	flex: 2;
	align-self: stretch;  
}

.bg-shimge {
	background-color: #009fa8;
	color: #ffffff;
}
.padding-xs {
	padding: 10rpx;
}

.basis-p20{
	flex-basis: 20%;
}
.basis-p40 {
	flex-basis: 40%;
}
.basis-p15 {
	flex-basis: 15%;
}
.basis-p25 {
	flex-basis: 25%;
}


.text-multcut{
  line-height: 1.4em;
  overflow: hidden; 
  text-overflow: ellipsis;
  word-break: break-all;   
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}


/*主容器*/  
.stepper {  
  width: 70px;  
  height: 26px;  
  /*给主容器设一个边框*/  
  border: 1px solid #ccc;  
  border-radius: 3px;  
  margin:0 auto;  
}  

/*加号和减号*/  
.stepper text {  
  width: 19px;  
  line-height: 26px;  
  text-align: center;  
  float: left;  
}  

/*数值*/  
.stepper input {  
  width: 30px;  
  height: 26px;  
  float: left;  
  margin: 0 auto;  
  text-align: center;  
  font-size: 12px;  
  /*给中间的input设置左右边框即可*/  
  border-left: 1px solid #ccc;  
  border-right: 1px solid #ccc;  
}  

/*普通样式*/  
.stepper .normal{  
  color: black;  
}  

/*禁用样式*/  
.stepper .disabled{  
  color: #ccc;  
}  


.footer{
  display:flex;
  flex-direction: column;
  width:100%;
  background-color:white;
  position:fixed;
  bottom:0;
  box-sizing:border-box;
  border-top:1rpx solid #eee;
  z-index: 1000;
}

.footer .down{
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 120rpx;
  line-height: 90rpx;
  padding-left: 20px;
  align-items:center;
  font-weight: bold;
  font-size: 17px;
}

.footer .item .iconfont {
  text-align: center;
  font-size: 40rpx;
}

.footer .down.item .iconfont.icon-shoucang1 {
  color: #f00;
}

.footer .down.item.iconfont.icon-shoucang {
  font-size: 40rpx;
  position: relative;
}

.footer .down.item.iconfont.icon-gouwuche1 {
  font-size: 40rpx;
  position: relative;
}

.footer .down.item.iconfont.icon-gouwuche1 .num {
  color: #fff;
  position: absolute;
  font-size: 18rpx;
  padding: 2rpx 8rpx 3rpx;
  border-radius: 200rpx;
  top: -10rpx;
  right: -10rpx;
}

.icon-shoucang {
  position: relative;
  width: 80rpx;
  height: 100%;
  margin-right: 40rpx;
}

.icon-shezhi{
  position: relative;
  width: 80rpx;
  height: 100%;
  margin-right: 40rpx;
}

.icon-gouwuche1 {
  position: relative;
  width: 80rpx;
  height: 100%;
  margin-right: 5rpx;
}


.icon-gouwuche1 .num {
  display: inline-block;
  width: 30rpx;
  height: 30rpx;
  text-align: center;
  line-height: 30rpx;
  position: absolute;
  top: 10rpx;
  right: 13rpx;
  background-color: #f00;
  font-size: 20rpx;
  color: #fff;
  border-radius: 50%;
}

.icon-shezhi .text{
  position: absolute;
  bottom: -25rpx;
  left: 0rpx;
  font-size: 23rpx;
}


.icon-shoucang .text {
  position: absolute;
  bottom: -25rpx;
  left: 0rpx;
  font-size: 23rpx;
}

.icon-gouwuche1 .text {
  position: absolute;
  bottom: -25rpx;
  left: -4rpx;
  font-size: 23rpx;
}

.padding-rt{
  padding-right: 10rpx;
}