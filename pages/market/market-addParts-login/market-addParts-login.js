// pages/market/market-addParts-login/market-addParts-login.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loadingHidden:true,//加载中
    isLoading:false,//加载中禁用控件
    postList:{},
    empty:0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.GetListInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  //公共方法，获取值
  GetListInfo(){
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //初始化
    this.setData({
    loadingHidden:false,//加载中
    isLoading:true,//加载中禁用控件
    postList:{}
    })
    var that=this
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaParts',
        Page:1,
        Search:'',
        Type:4,
        ItemCode:''
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
            that.setData({
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
            })
          }else{
          that.setData({
            postList:res.data,
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
          })
          }
      //展示列表，0则展示没有数据页面，其余无所谓
      that.setData({
        empty:Object.keys(that.data.postList).length
     })
     console.log('本次数据加载共 '+that.data.empty+' 条');
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
            loadingHidden:true
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
          loadingHidden:true
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //点击系列跳转
  jppart:function(e){
    let tType = e.currentTarget.dataset.index.tType; //跳转的类型

    var that = this;
    
    wx.navigateTo({
      url: '../market-addParts/market-addParts?tType='+tType,
    });
  }
})