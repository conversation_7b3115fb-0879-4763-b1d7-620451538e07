
const app = getApp()
import { wuxToast, http, updateAccountExpire } from '../../../utils/util.js'

Page({
  data: {
    active: 0,
    isLoading: false,//是否正在加载数据
    loadState: 0,//加载状态  1:加载中,-1:加载失败,2:没有更多了
    dataList:[],//明细列表
    dataAllList:[],//明细列表
    dataMain:null,//主表
    skey:'', //搜索关键字
    user:null, //当前用户
  },
  onTabChange(event) {
    console.log(event.detail.name);
  },  
  onLoad: function (options) {
    this.data.user = app.globalData.accountInfo;
    
  }, 
  getRequestData: function (callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    wx.showNavigationBarLoading();
    this.setData({isLoading: true, loadState: 1, dataMain: null, dataAllList: [], dataList: []});

    let that = this;
    let key = this.data.skey;
    let url = app.globalData.apiBase + '/api/CustBOM?code=' + key;
    console.log(url);

    let suc = rd => {
      //更新数据
      let main = {};
      let lines = [];
      let alllines = [];
      if(rd && rd.main)
        main = rd.main;
      if(rd && rd.lines)
        lines = rd.lines;
      if(rd && rd.allLines)
        alllines = rd.allLines;        
      main.error = rd.error;

      that.setData({
        isLoading: false,
        loadState: 0,
        dataMain: main,
        dataList: lines,
        dataAllList: alllines
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();

      if (callBack)
        callBack(rd);
    };

    let fl = () => {
      //更新数据
      that.setData({isLoading: false,loadState: -1,dataMain: null, dataAllList: [], dataList: []});
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh(); 
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });     
    };

    http(url, 'GET', null, suc, fl);
  },
  //搜索文本改变事件
  onChange: function(e){
    this.setData({
      skey: e.detail
    });
  },
  //查询按钮
  onSearch: function(){
    this.getRequestData();
  },  
 //跳转至详情页
 taprow: function (e) {
  console.log(e.currentTarget.dataset);

  app.globalData.curBOMRequire = e.currentTarget.dataset.require;
  if(app.globalData.curBOMRequire){
    wx.navigateTo({
      url: 'cust-bom-detail/cust-bom-detail'
    });
  }
},  
})