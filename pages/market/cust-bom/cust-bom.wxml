<van-sticky>
  <van-search value="{{ skey }}" bind:change="onChange" bind:search="onSearch" placeholder="请输入合同号" />
</van-sticky>
 
<view wx:if="{{dataMain && dataMain.htbh}}">
  <van-cell-group wx:if="{{dataMain.scjjsyq}}">
    <van-cell title="合同备注:" title-width="4.5em" value-class="sg-text-ht2" value="{{dataMain.scjjsyq}}" />
  </van-cell-group>

  <van-tabs type="card" color="#009fa8" active="{{ active }}" bind:change="onTabChange">
    <van-tab title="合同中相关系列">
      <van-cell-group title="" wx:if="{{dataList && dataList.length>0}}">
        <block wx:for="{{dataList}}" wx:for-item="item" wx:key="prod">
          <van-cell title="" title-class="sg-flex-auto" value-class="sg-text-ht1" bind:click="taprow" data-require="{{item.prodRequire}}" is-link value="{{item.prodSeries}}" />
        </block>
      </van-cell-group>

      <van-cell-group title="异常提示" wx:if="{{dataMain.error}}">
        <van-cell title="" title-class="sg-flex-auto" value-class="sg-text-ht1" value="{{dataMain.error}}" />
      </van-cell-group>    
    </van-tab>
    <van-tab title="其他系列">
      <block wx:for="{{dataAllList}}" wx:for-item="item" wx:key="prod">
          <van-cell title="" title-class="sg-flex-auto" value-class="sg-text-ht1" bind:click="taprow" data-require="{{item.prodRequire}}" is-link value="{{item.prodSeries}}" />
        </block>    
    </van-tab>    
  </van-tabs>
</view>
<van-empty wx:if="{{dataMain && !dataMain.htbh}}" image="search" description="未查询到该合同号" />

<view wx:if="{{loadState==1}}" class="sg-flex-center">
  <van-loading type="spinner" size="22px" color="#009fa8" vertical>加载中...</van-loading>
</view>
<van-empty wx:elif="{{loadState==-1}}" image="network" description="加载失败" />