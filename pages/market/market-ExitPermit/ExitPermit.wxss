/* pages/ExitPermit/ExitPermit.wxss */
.container {
  padding: 20rpx;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 修改模式提示样式 */
.edit-mode-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  background-color: #e6f7f6;
  color: #20B2AA;
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

/* Tab导航样式 */
.tabs {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 8rpx;
}

.tab {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.tab.active {
  background-color: #20B2AA;
  color: #fff;
  font-weight: bold;
}

.tab-content {
  flex: 1;
}

/* 页面标题 */
.page-header {
  text-align: center;
  padding: 30rpx 0;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #20B2AA;
}

/* 表头信息样式 */
.header-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #20B2AA;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.label.required::before {
  content: "*";
  color: #ff4444;
  margin-right: 8rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #fff;
}

.input:focus {
  border-color: #20B2AA;
}

.input.readonly {
  background-color: #f8f8f8;
  color: #666;
}

/* 车牌选择器 */
.plate-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  background-color: #fff;
  cursor: pointer;
}

.plate-selector:active {
  background-color: #f8f8f8;
}

.plate-value {
  font-size: 28rpx;
  color: #333;
}

.plate-value.placeholder {
  color: #999;
}

/* 状态显示 */
.status-display {
  height: 80rpx;
  display: flex;
  align-items: center;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #ffd591;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

/* 明细区域样式 */
.items-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-count {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  border: 2rpx dashed #20B2AA;
  border-radius: 8rpx;
  background-color: #f0fffe;
  color: #20B2AA;
  font-size: 28rpx;
  cursor: pointer;
}

.action-btn:active {
  background-color: #e6f7f6;
}

/* 明细列表 */
.items-list {
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  overflow: hidden;
}

/* 表格横向滚动容器 */
.table-scroll {
  width: 100%;
  white-space: nowrap;
}

/* 表格内容区域 */
.items-header,
.item-row {
  min-width: 980rpx; /* 确保表格有最小宽度，超出屏幕时可滚动 */
}

.items-header {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e8e8e8;
}

.header-cell {
  padding: 20rpx 10rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #666;
  text-align: center;
  border-right: 1rpx solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100rpx;
  box-sizing: border-box;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell.organization {
  width: 180rpx;
  flex: 0 0 180rpx;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.2;
}

.header-cell.customer {
  width: 160rpx;
  flex: 0 0 160rpx;
}

.header-cell.order-number {
  width: 200rpx;
  flex: 0 0 200rpx;
}

.header-cell.quantity {
  width: 120rpx;
  flex: 0 0 120rpx;
}

.header-cell.accessory-quantity {
  width: 120rpx;
  flex: 0 0 120rpx;
}

.header-cell.promotion-quantity {
  width: 120rpx;
  flex: 0 0 120rpx;
}

.header-cell.action {
  width: 100rpx;
  flex: 0 0 100rpx;
}

.item-row {
  display: flex;
  border-bottom: 1rpx solid #e8e8e8;
}

.item-row:last-child {
  border-bottom: none;
}

.item-cell {
  padding: 10rpx;
  border-right: 1rpx solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100rpx;
  box-sizing: border-box;
}

.item-cell:last-child {
  border-right: none;
}

.item-cell.organization {
  width: 180rpx;
  flex: 0 0 180rpx;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.2;
  padding: 15rpx 10rpx;
}

.item-cell.customer {
  width: 160rpx;
  flex: 0 0 160rpx;
}

.item-cell.order-number {
  width: 200rpx;
  flex: 0 0 200rpx;
}

.item-cell.quantity {
  width: 120rpx;
  flex: 0 0 120rpx;
}

.item-cell.accessory-quantity {
  width: 120rpx;
  flex: 0 0 120rpx;
}

.item-cell.promotion-quantity {
  width: 120rpx;
  flex: 0 0 120rpx;
}

.item-cell.action {
  width: 100rpx;
  flex: 0 0 100rpx;
}

.cell-input {
  width: 100%;
  height: 60rpx;
  border: none;
  text-align: center;
  font-size: 26rpx;
  background-color: transparent;
}

.cell-input:focus {
  outline: none;
  background-color: #f0fffe;
}

.delete-btn {
  width: 80rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff4444;
  color: #fff;
  font-size: 22rpx;
  border-radius: 6rpx;
  cursor: pointer;
}

.delete-btn:active {
  background-color: #cc3333;
}

/* 组织选择器 */
.org-selector {
  width: 100%;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5rpx 10rpx;
  border: none;
  border-radius: 4rpx;
  background-color: transparent;
  cursor: pointer;
  box-sizing: border-box;
}

.org-selector:active {
  background-color: #f0fffe;
}

.selected-text {
  font-size: 24rpx;
  color: #333;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.3;
  flex: 1;
  text-align: left;
  padding-right: 10rpx;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.3;
  flex: 1;
  text-align: left;
  padding-right: 10rpx;
}

/* 组织选择器弹窗 */
.org-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.org-selector-popup {
  width: 600rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.org-selector-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e8e8e8;
  background-color: #f8f9fa;
}

.org-selector-popup .popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.org-selector-popup .close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.org-selector-popup .close-btn:active {
  background-color: #f0f0f0;
  border-radius: 50%;
}

.org-list {
  padding: 0;
}

.org-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.org-item:last-child {
  border-bottom: none;
}

.org-item:active {
  background-color: #f8f9fa;
}

.org-name {
  font-size: 32rpx;
  color: #333;
}

/* 客户选择器 */
.customer-selector {
  width: 100%;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5rpx 10rpx;
  border: none;
  border-radius: 4rpx;
  background-color: transparent;
  cursor: pointer;
  box-sizing: border-box;
}

.customer-selector:active {
  background-color: #f0fffe;
}

/* 客户选择器弹窗 */
.customer-selector-popup {
  width: 100%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.customer-selector-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e8e8e8;
  background-color: #f8f9fa;
  flex-shrink: 0;
}

.customer-selector-popup .popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.customer-selector-popup .close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.customer-selector-popup .close-btn:active {
  background-color: #f0f0f0;
  border-radius: 50%;
}

/* 搜索区域 */
.search-section {
  position: relative;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e8e8e8;
  background-color: #fff;
  flex-shrink: 0;
}

.search-input {
  width: 100%;
  height: 70rpx;
  padding: 0 60rpx 0 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #20B2AA;
  background-color: #fff;
}

.search-section van-icon {
  position: absolute;
  right: 50rpx;
  top: 50%;
  transform: translateY(-50%);
}

/* 客户列表 */
.customer-list {
  flex: 1;
  overflow-y: auto;
  max-height: 60vh;
}

.customer-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.customer-item:last-child {
  border-bottom: none;
}

.customer-item:active {
  background-color: #f8f9fa;
}

.customer-name {
  font-size: 32rpx;
  color: #333;
  flex: 1;
  text-align: left;
}

.empty-customer-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  color: #999;
}

.empty-customer-list .empty-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 加载更多样式 */
.load-more-section {
  padding: 30rpx 40rpx;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.loading-more .loading-text {
  margin-left: 10rpx;
  font-size: 28rpx;
}

.load-more-btn {
  padding: 20rpx 40rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  cursor: pointer;
}

.load-more-btn:active {
  background-color: #e9ecef;
}

.load-more-text {
  font-size: 28rpx;
  color: #20B2AA;
}

/* 初始加载状态 */
.loading-customer-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  color: #999;
}

.loading-customer-list .loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.empty-hint {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #ccc;
}

/* 汇总区域 */
.summary-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}



.summary-list {
  margin-top: 20rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.summary-org {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.summary-customer {
  font-size: 24rpx;
  color: #666;
}

.summary-quantities {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  align-items: flex-end;
}

.summary-quantity {
  font-size: 24rpx;
  color: #20B2AA;
  font-weight: bold;
}

/* 备注区域样式 */
.remark-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.remark-content {
  margin-top: 20rpx;
}

.remark-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  box-sizing: border-box;
  resize: none;
}

.remark-textarea:focus {
  border-color: #20B2AA;
  background-color: #fff;
}

.remark-display {
  margin-top: 15rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #20B2AA;
}

.remark-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #e8e8e8;
}

.btn-primary {
  width: 100%;
  height: 80rpx;
  background-color: #20B2AA;
  color: #fff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  cursor: pointer;
}

.btn-primary:active {
  background-color: #1a9a94;
}

.btn-primary.disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.btn-primary.disabled:active {
  background-color: #ccc;
}

/* 车牌选择器弹窗 */
.plate-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plate-selector-popup {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e8e8e8;
  background-color: #f8f9fa;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-btn:active {
  background-color: #f0f0f0;
  border-radius: 50%;
}

.plate-list {
  max-height: 60vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.plate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.plate-item:last-child {
  border-bottom: none;
}

.plate-item:active {
  background-color: #f8f9fa;
}

.plate-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.plate-number {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  font-family: 'Arial', 'Microsoft YaHei', sans-serif;
  letter-spacing: 2rpx;
}

.driver-name {
  font-size: 26rpx;
  color: #666;
}

.plate-status {
  padding: 8rpx 16rpx;
  background-color: #f6ffed;
  color: #52c41a;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.empty-plate-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-plate-list .empty-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}
/* 查询
条件样式 */
.query-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.date-item {
  flex: 1;
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.date-item:active {
  background-color: #f8f9fa;
}

.date-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.date-value {
  font-size: 28rpx;
  color: #333;
}

.date-separator {
  font-size: 28rpx;
  color: #666;
  padding: 0 10rpx;
}

.query-btn {
  width: 100%;
  height: 80rpx;
  background-color: #20B2AA;
  color: #fff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  cursor: pointer;
}

.query-btn:active {
  background-color: #1a9a94;
}

/* 历史记录列表样式 */
.history-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.exit-permit-history-list {
  margin-top: 20rpx;
}

.exit-permit-history-item {
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
}

.exit-permit-history-item:last-child {
  margin-bottom: 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.permit-no {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.history-info {
  margin-bottom: 20rpx;
}

.info-section {
  margin-bottom: 30rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #20B2AA;
  margin-bottom: 15rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #e0f7f6;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 明细表格滚动容器 */
.detail-table-scroll {
  width: 100%;
  white-space: nowrap;
  margin-top: 15rpx;
}

/* 明细表格样式 */
.detail-table {
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  min-width: 1000rpx; /* 确保表格有最小宽度，超出屏幕时可滚动 */
}

.detail-header {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e8e8e8;
}

.detail-cell {
  padding: 15rpx 10rpx;
  font-size: 24rpx;
  text-align: center;
  border-right: 1rpx solid #e8e8e8;
}

.detail-cell:last-child {
  border-right: none;
}

.detail-cell.org {
  width: 200rpx;
  flex: 0 0 200rpx;
  font-weight: bold;
  color: #666;
}

.detail-cell.customer {
  width: 160rpx;
  flex: 0 0 160rpx;
  font-weight: bold;
  color: #666;
}

.detail-cell.order {
  width: 200rpx;
  flex: 0 0 200rpx;
  font-weight: bold;
  color: #666;
}

.detail-cell.qty {
  width: 120rpx;
  flex: 0 0 120rpx;
  font-weight: bold;
  color: #666;
}

.detail-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .detail-cell {
  font-weight: normal;
  color: #333;
  background-color: #fff;
}

/* 汇总行样式 */
.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.summary-values {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  align-items: flex-end;
}

.summary-value {
  font-size: 24rpx;
  color: #20B2AA;
  font-weight: bold;
}

/* 历史操作按钮样式 */
.history-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.edit-btn {
  padding: 12rpx 32rpx;
  background-color: #20B2AA;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  min-width: 140rpx;
  cursor: pointer;
}

.edit-btn:active {
  background-color: #1a9999;
}

.delete-btn-action {
  padding: 12rpx 32rpx;
  background-color: #ff6b6b;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  min-width: 140rpx;
  cursor: pointer;
}

.delete-btn-action:active {
  background-color: #e55555;
}

/* 修改底部按钮布局，支持两个按钮 */
.bottom-buttons {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background-color: white;
  border-top: 1rpx solid #eee;
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.bottom-buttons .btn-secondary {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #20B2AA;
  color: #20B2AA;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  background-color: #fff;
  cursor: pointer;
}

.bottom-buttons .btn-secondary:active {
  background-color: #f0fffe;
}

.bottom-buttons .btn-primary {
  flex: 2;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* 状态标签样式扩展 */
.status-rejected {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1rpx solid #ffccc7;
}

.status-cancelled {
  background-color: #f5f5f5;
  color: #999;
  border: 1rpx solid #d9d9d9;
}