<!--pages/ExitPermit/ExitPermit.wxml-->
<view class="container">
  <!-- 修改模式提示 -->
  <view wx:if="{{isEditMode}}" class="edit-mode-tip">
    <van-icon name="edit" size="16" color="#20B2AA"/>
    <text>修改出门证模式</text>
  </view>

  <!-- Tab标签页 -->
  <view class="tabs">
    <view class="tab {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
      新建出门证
    </view>
    <view class="tab {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
      历史记录
    </view>
  </view>

  <!-- 新建出门证页面 -->
  <view wx:if="{{currentTab === 0}}" class="tab-content">
    <!-- 表头信息 -->
    <view class="header-section">
    <view class="section-title">基本信息</view>
    
    <!-- 车牌号选择 -->
    <view class="form-item">
      <view class="label required">车牌号</view>
      <view class="plate-selector" bindtap="showPlateSelector">
        <text class="plate-value {{!selectedPlateNumber ? 'placeholder' : ''}}">
          {{selectedPlateNumber || '请选择已进门的车辆'}}
        </text>
        <van-icon name="arrow-down" size="16" color="#999"/>
      </view>
    </view>

    <!-- 司机姓名（自动填充） -->
    <view class="form-item">
      <view class="label required">司机姓名</view>
      <input class="input readonly" 
             placeholder="选择车牌后自动填充" 
             value="{{driverName}}" 
             disabled="true"/>
    </view>

  
  </view>

  <!-- 出门明细 -->
  <view class="items-section">
    <view class="section-header">
      <view class="section-title">出门明细</view>
      <view class="item-count">共{{exitItems.length}}条</view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="action-btn scan-btn" bindtap="scanCode">
        <van-icon name="scan" size="20" color="#20B2AA"/>
        <text>扫码添加</text>
      </view>
      <view class="action-btn add-btn" bindtap="addItemManually">
        <van-icon name="plus" size="20" color="#20B2AA"/>
        <text>手动添加</text>
      </view>
    </view>

    <!-- 明细列表 -->
    <view wx:if="{{exitItems.length > 0}}" class="items-list">
      <!-- 表格容器，支持横向滚动 -->
      <scroll-view scroll-x="true" class="table-scroll">
        <!-- 表头 -->
        <view class="items-header">
          <view class="header-cell organization">组织</view>
          <view class="header-cell order-number">单号</view>
          <view class="header-cell customer">客户</view>
          <view class="header-cell quantity">成品数量</view>
          <view class="header-cell accessory-quantity">配件数量</view>
          <view class="header-cell promotion-quantity">物资数量</view>
          <view class="header-cell action">操作</view>
        </view>

        <!-- 表行 -->
        <view wx:for="{{exitItems}}" wx:key="id" class="item-row">
          <view class="item-cell organization">
            <view class="org-selector" 
                  bindtap="showOrgPicker" 
                  data-index="{{index}}">
              <text class="{{item.organization ? 'selected-text' : 'placeholder-text'}}">
                {{item.organization || '选择组织'}}
              </text>
              <van-icon name="arrow-down" size="12" color="#999"/>
            </view>
          </view>
          <view class="item-cell order-number">
            <input class="cell-input" 
                   placeholder="单号" 
                   value="{{item.orderNumber}}" 
                   bindinput="onItemInput"
                   bindblur="onOrderNumberBlur"
                   data-index="{{index}}"
                   data-field="orderNumber"/>
          </view>
          <view class="item-cell customer">
            <view class="customer-selector" 
                  bindtap="showCustomerPicker" 
                  data-index="{{index}}">
              <text class="{{item.customer ? 'selected-text' : 'placeholder-text'}}">
                {{item.customer || '选择客户'}}
              </text>
              <van-icon name="arrow-down" size="12" color="#999"/>
            </view>
          </view>
          
          <view class="item-cell quantity">
            <input class="cell-input" 
                   placeholder="成品数量" 
                   type="number"
                   value="{{item.quantity}}" 
                   bindinput="onItemInput"
                   data-index="{{index}}"
                   data-field="quantity"/>
          </view>
          <view class="item-cell accessory-quantity">
            <input class="cell-input" 
                   placeholder="配件数量" 
                   type="number"
                   value="{{item.accessoryQuantity}}" 
                   bindinput="onItemInput"
                   data-index="{{index}}"
                   data-field="accessoryQuantity"/>
          </view>
          <view class="item-cell promotion-quantity">
            <input class="cell-input" 
                   placeholder="物资数量" 
                   type="number"
                   value="{{item.promotionQuantity}}" 
                   bindinput="onItemInput"
                   data-index="{{index}}"
                   data-field="promotionQuantity"/>
          </view>
          <view class="item-cell action">
            <view class="delete-btn" bindtap="deleteItem" data-index="{{index}}">
              删除
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{exitItems.length === 0}}" class="empty-state">
      <van-icon name="orders-o" size="64px" color="#ccc"/>
      <text class="empty-text">暂无出门明细</text>
      <text class="empty-hint">点击上方按钮添加明细</text>
    </view>
  </view>

  <!-- 汇总信息 -->
  <view wx:if="{{summaryData.length > 0}}" class="summary-section">
    <view class="section-title">数量汇总</view>
    
    <view class="summary-list">
      <view wx:for="{{summaryData}}" wx:key="index" class="summary-item">
        <view class="summary-info">
          <text class="summary-org">{{item.organization}}</text>
          <text class="summary-customer">{{item.customer}}</text>
        </view>
        <view class="summary-quantities">
          <text class="summary-quantity">成品：{{item.quantity}}</text>
          <text class="summary-quantity">配件：{{item.accessoryQuantity || 0}}</text>
          <text class="summary-quantity">物资：{{item.promotionQuantity || 0}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 备注信息 -->
  <view class="remark-section">
    <view class="section-title">备注</view>
    <view class="remark-content">
      <textarea class="remark-textarea" 
                placeholder="请输入备注信息" 
                value="{{remark}}" 
                bindinput="onRemarkInput"
                maxlength="500"
                auto-height="true"/>
    </view>
  </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <view wx:if="{{isEditMode}}" class="btn-secondary" bindtap="resetForm">取消修改</view>
      <view class="btn-primary {{submitting ? 'disabled' : ''}}" 
            bindtap="{{submitting ? '' : 'confirmComplete'}}">
        {{isEditMode ? (submitting ? '保存中...' : '保存修改') : (submitting ? '提交中...' : '确认完成')}}
      </view>
    </view>
  </view>

  <!-- 历史记录页面 -->
  <view wx:if="{{currentTab === 1}}" class="tab-content">
    <!-- 查询条件 -->
    <view class="query-section">
      <view class="section-title">查询条件</view>
      <view class="date-range">
        <view class="date-item" bindtap="showDatePicker" data-type="start">
          <view class="date-label">开始日期</view>
          <view class="date-value">{{queryStartDate || '请选择'}}</view>
          <van-icon name="arrow-down" size="14" color="#999"/>
        </view>
        <view class="date-separator">至</view>
        <view class="date-item" bindtap="showDatePicker" data-type="end">
          <view class="date-label">结束日期</view>
          <view class="date-value">{{queryEndDate || '请选择'}}</view>
          <van-icon name="arrow-down" size="14" color="#999"/>
        </view>
      </view>
      <view class="query-btn" bindtap="queryHistory">查询</view>
    </view>



    <!-- 历史列表 -->
    <view class="history-section">
      <view class="section-title">出门证历史</view>
      
      <!-- 加载状态 -->
      <view wx:if="{{historyLoading}}" class="loading-container">
        <van-loading type="spinner" size="24px" color="#20B2AA"/>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 历史列表 -->
      <view wx:if="{{!historyLoading && historyList.length > 0}}" class="exit-permit-history-list">
        <view wx:for="{{historyList}}" wx:key="id" class="exit-permit-history-item">
          <view class="history-header">
            <view class="permit-no">出门证编号：{{item.exitPermitNo}}</view>
            <view class="status-tag status-{{item.status}}">{{item.statusText}}</view>
          </view>
          
          <view class="history-info">
            <!-- 基本信息 -->
            <view class="info-section">
              <view class="section-label">基本信息</view>
              <view class="info-row">
                <text class="info-label">车牌号：</text>
                <text class="info-value">{{item.plateNumber}}</text>
              </view>
              <view class="info-row">
                <text class="info-label">司机姓名：</text>
                <text class="info-value">{{item.driverName}}</text>
              </view>
              <view class="info-row">
                <text class="info-label">状态：</text>
                <text class="info-value">{{item.status}}</text>
              </view>
              <view class="info-row">
                <text class="info-label">创建时间：</text>
                <text class="info-value">{{item.createTime }}</text>
              </view>
            </view>

            <!-- 出门明细 -->
            <view wx:if="{{item.exitItems && item.exitItems.length > 0}}" class="info-section">
              <view class="section-label">出门明细</view>
              <scroll-view scroll-x="true" class="detail-table-scroll">
                <view class="detail-table">
                  <view class="detail-header">
                    <text class="detail-cell org">组织</text>
                    <text class="detail-cell order">单号</text>
                    <text class="detail-cell customer">客户</text>
                    <text class="detail-cell qty">成品数量</text>
                    <text class="detail-cell qty">配件数量</text>
                    <text class="detail-cell qty">物资数量</text>
                  </view>
                  <view wx:for="{{item.exitItems}}" wx:for-item="detail" wx:key="id" class="detail-row">
                    <text class="detail-cell org">{{detail.organization}}</text>
                    <text class="detail-cell order">{{detail.orderNumber}}</text>
                    <text class="detail-cell customer">{{detail.customer}}</text>
                    <text class="detail-cell qty">{{detail.quantity}}</text>
                    <text class="detail-cell qty">{{detail.accessoryQuantity || 0}}</text>
                    <text class="detail-cell qty">{{detail.promotionQuantity || 0}}</text>
                  </view>
                </view>
              </scroll-view>
            </view>

            <!-- 汇总信息 -->
            <view wx:if="{{item.summaryData && item.summaryData.length > 0}}" class="info-section">
              <view class="section-label">数量汇总</view>
              <view class="summary-list">
                <view wx:for="{{item.summaryData}}" wx:for-item="summary" wx:key="index" class="summary-item">
                  <view class="summary-info">
                    <text class="summary-org">{{summary.organization}}</text>
                    <text class="summary-customer">{{summary.customer}}</text>
                  </view>
                  <view class="summary-quantities">
                    <text class="summary-quantity">成品：{{summary.quantity}}</text>
                    <text class="summary-quantity">配件：{{summary.accessoryQuantity || 0}}</text>
                    <text class="summary-quantity">物资：{{summary.promotionQuantity || 0}}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 备注信息 -->
            <view wx:if="{{item.remark}}" class="info-section">
              <view class="section-label">备注</view>
              <view class="remark-display">
                <text class="remark-text">{{item.remark}}</text>
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view wx:if="{{item.canEdit}}" class="history-actions">
            <!-- 修改按钮：只有待完成状态才能修改 -->
            <view wx:if="{{item.canEdit}}" 
                  class="edit-btn" 
                  bindtap="editExitPermit" 
                  data-exit-permit="{{item}}">
              修改出门证
            </view>
            
            <!-- 删除按钮 -->
            <view wx:if="{{item.canEdit}}" 
                  class="delete-btn-action" 
                  bindtap="deleteExitPermit" 
                  data-exit-permit-id="{{item.id}}">
              删除出门证
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{!historyLoading && historyList.length === 0}}" class="empty-state">
        <van-icon name="orders-o" size="64px" color="#ccc"/>
        <text class="empty-text">暂无出门证记录</text>
      </view>
    </view>
  </view>

  <!-- 车牌选择器弹窗 -->
  <view wx:if="{{showPlateSelector}}" class="plate-selector-overlay" bindtap="hidePlateSelector">
    <view class="plate-selector-popup" catchtap="">
      <view class="popup-header">
        <text class="popup-title">选择车牌号</text>
        <view class="close-btn" bindtap="hidePlateSelector">
          <van-icon name="cross" size="20" color="#666"/>
        </view>
      </view>
      
      <view class="plate-list">
        <view wx:if="{{loading}}" class="loading-container">
          <van-loading type="spinner" size="24px" color="#20B2AA"/>
          <text class="loading-text">加载中...</text>
        </view>
        
        <view wx:if="{{!loading && plateNumberList.length > 0}}">
          <view wx:for="{{plateNumberList}}" 
                wx:key="plateNumber" 
                class="plate-item" 
                bindtap="selectPlateNumber"
                data-plate="{{item}}">
            <view class="plate-info">
              <text class="plate-number">{{item.plateNumber}}</text>
              <text class="driver-name">{{item.driverName}}</text>
            </view>
            <view class="plate-status">
              <text class="status-text">已进门</text>
            </view>
          </view>
        </view>
        
        <view wx:if="{{!loading && plateNumberList.length === 0}}" class="empty-plate-list">
          <van-icon name="orders-o" size="48px" color="#ccc"/>
          <text class="empty-text">暂无已进门车辆</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 组织选择器弹窗 -->
  <view wx:if="{{showOrgPicker}}" class="org-selector-overlay" bindtap="hideOrgPicker">
    <view class="org-selector-popup" catchtap="">
      <view class="popup-header">
        <text class="popup-title">组织</text>
        <view class="close-btn" bindtap="hideOrgPicker">
          <van-icon name="cross" size="20" color="#666"/>
        </view>
      </view>
      
      <view class="org-list">
        <view wx:for="{{organizationOptions}}" 
              wx:key="*this" 
              class="org-item" 
              bindtap="selectOrganization"
              data-org="{{item}}">
          <text class="org-name">{{item}}</text>
          <van-icon name="arrow" size="16" color="#999"/>
        </view>
      </view>
    </view>
  </view>

  <!-- 客户选择器弹窗 -->
  <van-popup 
    show="{{showCustomerPicker}}" 
    position="center" 
    round 
    bind:close="hideCustomerPicker"
    custom-style="width: 600rpx; max-height: 80vh;">
    <view class="customer-selector-popup">
      <view class="popup-header">
        <text class="popup-title">选择客户</text>
        <view class="close-btn" bindtap="hideCustomerPicker">
          <van-icon name="cross" size="20" color="#666"/>
        </view>
      </view>
      
      <!-- 搜索框 -->
      <view class="search-section">
        <input class="search-input" 
               placeholder="搜索客户名称" 
               value="{{customerSearchKeyword}}"
               bindinput="onCustomerSearch"/>
        <van-icon name="search" size="16" color="#999"/>
      </view>
      
      <view class="customer-list">
        <view wx:if="{{filteredCustomerOptions.length > 0}}">
          <view wx:for="{{filteredCustomerOptions}}" 
                wx:key="*this" 
                class="customer-item" 
                bindtap="selectCustomer"
                data-customer="{{item}}">
            <text class="customer-name">{{item}}</text>
            <van-icon name="arrow" size="16" color="#999"/>
          </view>
          
          <!-- 加载更多 -->
          <view wx:if="{{customerHasMore}}" class="load-more-section">
            <view wx:if="{{customerLoading}}" class="loading-more">
              <van-loading type="spinner" size="20px" color="#20B2AA"/>
              <text class="loading-text">加载中...</text>
            </view>
            <view wx:else class="load-more-btn" bindtap="loadMoreCustomers">
              <text class="load-more-text">加载更多</text>
            </view>
          </view>
        </view>
        
        <view wx:if="{{filteredCustomerOptions.length === 0 && !customerLoading}}" class="empty-customer-list">
          <van-icon name="orders-o" size="48px" color="#ccc"/>
          <text class="empty-text">{{customerSearchKeyword ? '未找到匹配的客户' : '暂无客户数据'}}</text>
        </view>
        
        <!-- 初始加载状态 -->
        <view wx:if="{{customerLoading && filteredCustomerOptions.length === 0}}" class="loading-customer-list">
          <van-loading type="spinner" size="24px" color="#20B2AA"/>
          <text class="loading-text">加载客户数据中...</text>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 日期选择器 -->
  <picker wx:if="{{showDatePicker}}" 
          mode="date" 
          value="{{datePickerType === 'start' ? queryStartDate : queryEndDate}}" 
          bindchange="onDateChange"
          bindcancel="hideDatePicker"
          style="position: fixed; top: -100px; left: -100px; opacity: 0;">
    <view style="width: 1px; height: 1px;"></view>
  </picker>
</view>