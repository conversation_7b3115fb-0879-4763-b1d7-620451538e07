const app = getApp();
const { http } = require('../../../utils/util.js');
const config = require('../../../config/config.js');

Page({
    data: {
        // Tab切换
        currentTab: 0, // 0-新建出门证, 1-历史记录

        // 表头信息
        selectedPlateNumber: '', // 选择的车牌号
        driverName: '', // 司机姓/*  */名（自动填充）
        remark: '', // 备注

        // 修改相关
        isEditMode: false, // 是否为修改模式
        editingExitPermitId: '', // 正在修改的出门证ID
        editingExitPermitNo: '', // 正在修改的出门证单号

        // 车牌号选择相关
        plateNumberList: [], // 已进门的车辆列表
        showPlateSelector: false, // 是否显示车牌选择器
        selectedGateRecordId: '', // 选中的进出门主键

        // 表行数据
        exitItems: [], // 出门证明细列表

        // 扫码相关
        showScanner: false, // 是否显示扫码界面

        // 组织选择相关
        organizationOptions: ['新界泵业（浙江）有限公司', '浙江老百姓泵业有限公司'], // 组织选项
        showOrgPicker: false, // 是否显示组织选择器
        currentEditIndex: -1, // 当前编辑的行索引

        // 客户选择相关
        customerOptions: [], // 客户选项列表（仅存储当前页数据）
        filteredCustomerOptions: [], // 过滤后的客户选项
        showCustomerPicker: false, // 是否显示客户选择器
        customerSearchKeyword: '', // 客户搜索关键词
        customerLoading: false, // 客户数据加载状态
        customerPage: 1, // 当前页码
        customerPageSize: 50, // 每页数量
        customerHasMore: true, // 是否还有更多数据

        // 汇总数据
        summaryData: [], // 按组织客户维度汇总的数据

        // 历史记录相关
        historyList: [], // 历史出门证列表
        historyLoading: false, // 历史加载状态
        queryStartDate: '', // 查询开始日期
        queryEndDate: '', // 查询结束日期
        showDatePicker: false, // 是否显示日期选择器
        datePickerType: '', // 日期选择器类型: start, end

        // 用户信息
        userCode: '', // 登录账号
        userName: '', // 用户姓名

        // 加载状态
        loading: false,
        submitting: false
    },

    onLoad: function (options) {
        console.log('出门证管理页面加载');
        this.getUserInfo();
        this.loadEnteredVehicles();

        // 设置默认查询日期范围（最近7天）
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);

        this.setData({
            queryStartDate: this.formatDate(startDate),
            queryEndDate: this.formatDate(endDate)
        });
    },

    // 格式化日期
    formatDate: function (date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    },

    // Tab切换
    switchTab: function (e) {
        const tab = parseInt(e.currentTarget.dataset.tab);
        console.log('切换到tab:', tab);

        // 如果在修改模式，提示用户
        if (this.data.isEditMode) {
            wx.showModal({
                title: '退出修改',
                content: '确定要退出修改模式吗？未保存的修改将丢失。',
                success: (res) => {
                    if (res.confirm) {
                        this.resetForm();
                        this.setData({
                            currentTab: tab
                        }, () => {
                            if (tab === 1) {
                                setTimeout(() => {
                                    this.loadHistoryData();
                                }, 200);
                            }
                        });
                    }
                }
            });
            return;
        }

        this.setData({
            currentTab: tab
        }, () => {
            if (tab === 1) {
                setTimeout(() => {
                    this.loadHistoryData();
                }, 200);
            }
        });
    },

    // 获取用户信息
    getUserInfo: function () {
        const accountInfo = app.globalData.accountInfo;
        if (accountInfo && accountInfo.code) {
            this.setData({
                userCode: accountInfo.code,
                userName: accountInfo.name || accountInfo.code
            });
        } else {
            // 如果没有登录信息，跳转到登录页
            wx.showModal({
                title: '提示',
                content: '请先登录',
                showCancel: false,
                success: () => {
                    wx.redirectTo({
                        url: '/pages/login/login'
                    });
                }
            });
        }
    },

    // 加载已进门的车辆列表
    loadEnteredVehicles: function () {
        this.setData({ loading: true });

        const apiUrl = config.api.baseUrl + config.api.getEnteredVehicles;

        http(apiUrl, 'POST', { userCode: this.data.userCode },
            (res) => {
                this.setData({ loading: false });

                if (res && res.status === true) {
                    this.setData({
                        plateNumberList: res.data || []
                    });
                } else {
                    wx.showToast({
                        title: res.message || '获取车辆列表失败',
                        icon: 'none'
                    });
                }
            },
            () => {
                this.setData({ loading: false });
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none'
                });
            }
        );
    },

    // 加载客户选项（分页）
    loadCustomerOptions: function (isLoadMore = false) {
        if (this.data.customerLoading) return;
        
        this.setData({ customerLoading: true });
        
        const apiUrl = config.api.baseUrl + config.api.getCustomerList;
        const params = {
            userCode: this.data.userCode,
            page: this.data.customerPage,
            pageSize: this.data.customerPageSize,
            keyword: this.data.customerSearchKeyword || ''
        };

        http(apiUrl, 'POST', params,
            (res) => {
                this.setData({ customerLoading: false });
                
                if (res && res.status === true && res.data) {
                    const newCustomers = res.data.list || [];
                    const hasMore = res.data.hasMore || false;
                    
                    let allCustomers = [];
                    if (isLoadMore) {
                        allCustomers = [...this.data.customerOptions, ...newCustomers];
                    } else {
                        allCustomers = newCustomers;
                    }
                    
                    this.setData({
                        customerOptions: allCustomers,
                        filteredCustomerOptions: allCustomers,
                        customerHasMore: hasMore
                    });
                } else {
                    console.log('加载客户列表失败:', res.message);
                    wx.showToast({
                        title: res.message || '加载客户列表失败',
                        icon: 'none'
                    });
                }
            },
            () => {
                this.setData({ customerLoading: false });
                console.log('加载客户列表网络错误');
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none'
                });
            }
        );
    },



    // 加载历史数据
    loadHistoryData: function () {
        console.log('开始加载历史数据');

        if (!this.data.queryStartDate || !this.data.queryEndDate) {
            wx.showToast({
                title: '请先设置查询日期范围',
                icon: 'none'
            });
            return;
        }

        this.setData({
            historyLoading: true
        });

        const apiUrl = config.api.baseUrl + config.api.getExitPermitHistory;

        http(apiUrl, 'POST', {
            userCode: this.data.userCode,
            startDate: this.data.queryStartDate,
            endDate: this.data.queryEndDate
        },
            (res) => {
                this.setData({
                    historyLoading: false
                });

                if (res && res.status === true) {
                    const historyList = res.data || [];
                    
                    // 为每个出门证计算汇总数据
                    historyList.forEach(exitPermit => {
                        if (exitPermit.exitItems && exitPermit.exitItems.length > 0) {
                            exitPermit.summaryData = this.calculateSingleExitPermitSummary(exitPermit.exitItems);
                        }
                    });
                    
                    this.setData({
                        historyList: historyList
                    });



                    wx.showToast({
                        title: `加载了${historyList.length}条记录`,
                        icon: 'success',
                        duration: 1500
                    });
                } else {
                    wx.showToast({
                        title: res.message || '获取历史记录失败',
                        icon: 'none'
                    });
                }
            },
            () => {
                this.setData({
                    historyLoading: false
                });
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none'
                });
            }
        );
    },



    // 显示日期选择器
    showDatePicker: function (e) {
        const type = e.currentTarget.dataset.type;
        this.setData({
            showDatePicker: true,
            datePickerType: type
        });
    },

    // 日期选择确认
    onDateChange: function (e) {
        const date = e.detail.value;
        const type = this.data.datePickerType;

        if (type === 'start') {
            this.setData({
                queryStartDate: date
            });
        } else if (type === 'end') {
            this.setData({
                queryEndDate: date
            });
        }

        this.setData({
            showDatePicker: false,
            datePickerType: ''
        });
    },

    // 隐藏日期选择器
    hideDatePicker: function () {
        this.setData({
            showDatePicker: false,
            datePickerType: ''
        });
    },

    // 查询历史
    queryHistory: function () {
        if (!this.data.queryStartDate || !this.data.queryEndDate) {
            wx.showToast({
                title: '请选择查询日期范围',
                icon: 'none'
            });
            return;
        }

        // 验证日期范围
        const startDate = new Date(this.data.queryStartDate);
        const endDate = new Date(this.data.queryEndDate);

        if (startDate > endDate) {
            wx.showToast({
                title: '开始日期不能大于结束日期',
                icon: 'none'
            });
            return;
        }

        this.loadHistoryData();
    },

    // 修改出门证
    editExitPermit: function (e) {
        const exitPermit = e.currentTarget.dataset.exitPermit;

        // 填充表单数据
        this.setData({
            isEditMode: true,
            editingExitPermitId: exitPermit.id, // 保存主键ID
            editingExitPermitNo: exitPermit.exitPermitNo, // 保存出门证单号
            selectedPlateNumber: exitPermit.plateNumber,
            driverName: exitPermit.driverName,
            selectedGateRecordId: exitPermit.gateRecordId || '', // 保存进出门主键
            remark: exitPermit.remark || '',
            exitItems: exitPermit.exitItems || [],
            summaryData: exitPermit.summaryData || [],
            currentTab: 0 // 切换到编辑页面
        });

        // 重新计算汇总
        this.calculateSummary();

        wx.showToast({
            title: '已进入修改模式',
            icon: 'success'
        });
    },

    // 删除出门证
    deleteExitPermit: function (e) {
        const exitPermitId = e.currentTarget.dataset.exitPermitId;
        const that = this;

        wx.showModal({
            title: '确认删除',
            content: '确定要删除这个出门证吗？删除后不可恢复。',
            success: function (res) {
                if (res.confirm) {
                    that.doDeleteExitPermit(exitPermitId);
                }
            }
        });
    },

    // 执行删除出门证
    doDeleteExitPermit: function (exitPermitId) {
        wx.showLoading({
            title: '删除中...',
            mask: true
        });

        const apiUrl = config.api.baseUrl + config.api.deleteExitPermit;

        http(apiUrl, 'POST', {
            exitPermitId: exitPermitId,
            userCode: this.data.userCode
        },
            (res) => {
                wx.hideLoading();

                if (res && res.status === true) {
                    wx.showToast({
                        title: '删除成功',
                        icon: 'success'
                    });

                    // 重新加载历史数据（会自动计算全局汇总）
                    this.loadHistoryData();
                } else {
                    wx.showToast({
                        title: res.message || '删除失败',
                        icon: 'none'
                    });
                }
            },
            () => {
                wx.hideLoading();
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none'
                });
            }
        );
    },

    // 显示车牌选择器
    showPlateSelector: function () {
        if (this.data.plateNumberList.length === 0) {
            wx.showToast({
                title: '暂无已进门车辆',
                icon: 'none'
            });
            return;
        }

        this.setData({
            showPlateSelector: true
        });
    },

    // 隐藏车牌选择器
    hidePlateSelector: function () {
        this.setData({
            showPlateSelector: false
        });
    },

    // 选择车牌号
    selectPlateNumber: function (e) {
        const plateInfo = e.currentTarget.dataset.plate;

        this.setData({
            selectedPlateNumber: plateInfo.plateNumber,
            driverName: plateInfo.driverName,
            selectedGateRecordId: plateInfo.gateRecordId, // 保存进出门主键
            showPlateSelector: false
        });

        wx.showToast({
            title: '车牌选择成功',
            icon: 'success'
        });
    },

    // 备注输入
    onRemarkInput: function (e) {
        this.setData({
            remark: e.detail.value
        });
    },

    // 扫码添加
    scanCode: function () {
        const that = this;

        wx.scanCode({
            success: function (res) {
                console.log('扫码结果:', res.result);
                that.addItemByCode(res.result);
            },
            fail: function () {
                wx.showToast({
                    title: '扫码失败，请重试',
                    icon: 'none'
                });
            }
        });
    },

    // 根据扫码结果添加明细
    addItemByCode: function (code) {
        wx.showLoading({
            title: '查询中...',
            mask: true
        });

        const apiUrl = config.api.baseUrl + config.api.getLogisticsItemByCode;

        http(apiUrl, 'POST', {
            code: code,
            userCode: this.data.userCode
        },
            (res) => {
                wx.hideLoading();

                if (res && res.status === true && res.data) {
                    const newItem = {
                        id: Date.now(),
                        organization: res.data.organization || '',
                        customer: res.data.customer || '',
                        orderNumber: res.data.orderNumber || code,
                        quantity: res.data.quantity || 0,
                        accessoryQuantity: 0,
                        promotionQuantity: 0,
                        originalQuantity: res.data.quantity || 0 // 保存原始数量
                    };

                    const exitItems = [...this.data.exitItems, newItem];
                    this.setData({
                        exitItems: exitItems
                    });

                    this.calculateSummary();

                    wx.showToast({
                        title: '添加成功',
                        icon: 'success'
                    });
                } else {
                    wx.showToast({
                        title: res.message || '未找到相关信息',
                        icon: 'none'
                    });
                }
            },
            () => {
                wx.hideLoading();
                wx.showToast({
                    title: '查询失败，请重试',
                    icon: 'none'
                });
            }
        );
    },

    // 手动添加明细
    addItemManually: function () {
        const newItem = {
            id: Date.now(),
            organization: '',
            customer: '',
            orderNumber: '',
            quantity: 0,
            accessoryQuantity: 0,
            promotionQuantity: 0,
            originalQuantity: 0
        };

        const exitItems = [...this.data.exitItems, newItem];
        this.setData({
            exitItems: exitItems
        });

        this.calculateSummary();
    },

    // 删除明细
    deleteItem: function (e) {
        const index = e.currentTarget.dataset.index;
        const that = this;

        wx.showModal({
            title: '确认删除',
            content: '确定要删除这条记录吗？',
            success: function (res) {
                if (res.confirm) {
                    const exitItems = that.data.exitItems;
                    exitItems.splice(index, 1);
                    that.setData({
                        exitItems: exitItems
                    });
                    that.calculateSummary();
                }
            }
        });
    },

    // 显示组织选择器
    showOrgPicker: function (e) {
        const index = e.currentTarget.dataset.index;
        this.setData({
            showOrgPicker: true,
            currentEditIndex: index
        });
    },

    // 隐藏组织选择器
    hideOrgPicker: function () {
        this.setData({
            showOrgPicker: false,
            currentEditIndex: -1
        });
    },

    // 选择组织
    selectOrganization: function (e) {
        const org = e.currentTarget.dataset.org;
        const index = this.data.currentEditIndex;

        if (index >= 0) {
            const exitItems = this.data.exitItems;
            exitItems[index].organization = org;

            this.setData({
                exitItems: exitItems,
                showOrgPicker: false,
                currentEditIndex: -1
            });

            this.calculateSummary();
        }
    },

    // 显示客户选择器
    showCustomerPicker: function (e) {
        const index = e.currentTarget.dataset.index;
        this.setData({
            showCustomerPicker: true,
            currentEditIndex: index,
            customerSearchKeyword: '',
            customerPage: 1,
            customerHasMore: true,
            customerOptions: [],
            filteredCustomerOptions: []
        });
        
        // 加载第一页客户数据
        this.loadCustomerOptions();
    },

    // 隐藏客户选择器
    hideCustomerPicker: function () {
        this.setData({
            showCustomerPicker: false,
            currentEditIndex: -1,
            customerSearchKeyword: ''
        });
    },



    // 客户搜索（防抖）
    onCustomerSearch: function (e) {
        const keyword = e.detail.value.trim();
        
        this.setData({
            customerSearchKeyword: keyword,
            customerPage: 1,
            customerHasMore: true
        });
        
        // 清除之前的定时器
        if (this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        
        // 防抖处理，500ms后执行搜索
        this.searchTimer = setTimeout(() => {
            this.loadCustomerOptions();
        }, 500);
    },

    // 选择客户
    selectCustomer: function (e) {
        const customer = e.currentTarget.dataset.customer;
        const index = this.data.currentEditIndex;

        if (index >= 0) {
            const exitItems = this.data.exitItems;
            exitItems[index].customer = customer;

            this.setData({
                exitItems: exitItems,
                showCustomerPicker: false,
                currentEditIndex: -1,
                customerSearchKeyword: ''
            });

            this.calculateSummary();
        }
    },

    // 加载更多客户
    loadMoreCustomers: function () {
        if (this.data.customerLoading || !this.data.customerHasMore) return;
        
        this.setData({
            customerPage: this.data.customerPage + 1
        });
        
        this.loadCustomerOptions(true);
    },

    // 明细字段输入
    onItemInput: function (e) {
        const { index, field } = e.currentTarget.dataset;
        const value = e.detail.value;
        const exitItems = this.data.exitItems;

        exitItems[index][field] = value;

        // 如果是数量字段，需要转换为数字
        if (field === 'quantity' || field === 'accessoryQuantity' || field === 'promotionQuantity') {
            exitItems[index][field] = parseInt(value) || 0;
        }

        this.setData({
            exitItems: exitItems
        });

        // 如果修改了数量字段，重新计算汇总
        if (field === 'quantity' || field === 'accessoryQuantity' || field === 'promotionQuantity') {
            this.calculateSummary();
        }
    },

    // 单号输入框离焦事件
    onOrderNumberBlur: function (e) {
        const { index } = e.currentTarget.dataset;
        const orderNumber = e.detail.value.trim();
        
        // 如果单号为空，不进行查询
        if (!orderNumber) {
            return;
        }

        // 检查当前行是否已经有组织信息，如果有说明已经填充过数据，不再重复查询
        const exitItems = this.data.exitItems;
        const currentItem = exitItems[index];
        
        if (currentItem.organization && currentItem.customer) {
            return;
        }

        // 调用接口查询数据
        this.queryItemByOrderNumber(orderNumber, index);
    },

    // 根据单号查询数据并填充到指定行
    queryItemByOrderNumber: function (orderNumber, index) {
        wx.showLoading({
            title: '查询中...',
            mask: true
        });

        const apiUrl = config.api.baseUrl + config.api.getLogisticsItemByCode;

        http(apiUrl, 'POST', {
            code: orderNumber,
            userCode: this.data.userCode
        },
            (res) => {
                wx.hideLoading();

                if (res && res.status === true && res.data) {
                    // 更新指定行的数据
                    const exitItems = this.data.exitItems;
                    const item = exitItems[index];
                    
                    // 只填充空字段，保留用户已输入的数据
                    if (!item.organization) {
                        item.organization = res.data.organization || '';
                    }
                    if (!item.customer) {
                        item.customer = res.data.customer || '';
                    }
                    if (!item.quantity || item.quantity === 0) {
                        item.quantity = res.data.quantity || 0;
                        item.originalQuantity = res.data.quantity || 0;
                    }

                    this.setData({
                        exitItems: exitItems
                    });

                    this.calculateSummary();

                    wx.showToast({
                        title: '数据填充成功',
                        icon: 'success'
                    });
                } else {
                    wx.showToast({
                        title: res.message || '未找到相关信息',
                        icon: 'none'
                    });
                }
            },
            () => {
                wx.hideLoading();
                wx.showToast({
                    title: '查询失败，请重试',
                    icon: 'none'
                });
            }
        );
    },

    // 计算汇总数据
    calculateSummary: function () {
        const summaryMap = new Map();

        this.data.exitItems.forEach(item => {
            if (item.organization && item.customer) {
                const key = `${item.organization}-${item.customer}`;
                const existing = summaryMap.get(key);

                if (existing) {
                    existing.quantity += item.quantity || 0;
                    existing.accessoryQuantity += item.accessoryQuantity || 0;
                    existing.promotionQuantity += item.promotionQuantity || 0;
                } else {
                    summaryMap.set(key, {
                        organization: item.organization,
                        customer: item.customer,
                        quantity: item.quantity || 0,
                        accessoryQuantity: item.accessoryQuantity || 0,
                        promotionQuantity: item.promotionQuantity || 0
                    });
                }
            }
        });

        const summaryData = Array.from(summaryMap.values());
        this.setData({
            summaryData: summaryData
        });
    },

    // 计算单个出门证汇总数据
    calculateSingleExitPermitSummary: function (exitItems) {
        const summaryMap = new Map();

        exitItems.forEach(item => {
            if (item.organization && item.customer) {
                const key = `${item.organization}-${item.customer}`;
                const existing = summaryMap.get(key);

                if (existing) {
                    existing.quantity += item.quantity || 0;
                    existing.accessoryQuantity += item.accessoryQuantity || 0;
                    existing.promotionQuantity += item.promotionQuantity || 0;
                } else {
                    summaryMap.set(key, {
                        organization: item.organization,
                        customer: item.customer,
                        quantity: item.quantity || 0,
                        accessoryQuantity: item.accessoryQuantity || 0,
                        promotionQuantity: item.promotionQuantity || 0
                    });
                }
            }
        });

        return Array.from(summaryMap.values());
    },



    // 确认完成
    confirmComplete: function () {
        if (!this.validateForm()) {
            return;
        }

        const that = this;
        const title = this.data.isEditMode ? '确认修改' : '确认完成';
        const content = this.data.isEditMode ?
            '确定要保存修改吗？' :
            '确定要完成这个出门证吗？完成后不可修改。';

        wx.showModal({
            title: title,
            content: content,
            success: function (res) {
                if (res.confirm) {
                    that.submitExitPermit();
                }
            }
        });
    },

    // 表单验证
    validateForm: function () {
        if (!this.data.selectedPlateNumber) {
            wx.showToast({
                title: '请选择车牌号',
                icon: 'none'
            });
            return false;
        }

        if (!this.data.driverName) {
            wx.showToast({
                title: '司机姓名不能为空',
                icon: 'none'
            });
            return false;
        }

        if (this.data.exitItems.length === 0) {
            wx.showToast({
                title: '请添加出门明细',
                icon: 'none'
            });
            return false;
        }

        // 验证明细数据完整性
        for (let i = 0; i < this.data.exitItems.length; i++) {
            const item = this.data.exitItems[i];
            if (!item.organization) {
                wx.showToast({
                    title: `第${i + 1}行组织不能为空`,
                    icon: 'none'
                });
                return false;
            }
            if (!item.customer) {
                wx.showToast({
                    title: `第${i + 1}行客户不能为空`,
                    icon: 'none'
                });
                return false;
            }
            if (!item.orderNumber) {
                wx.showToast({
                    title: `第${i + 1}行单号不能为空`,
                    icon: 'none'
                });
                return false;
            }
            if (!item.quantity || item.quantity <= 0) {
                wx.showToast({
                    title: `第${i + 1}行数量必须大于0`,
                    icon: 'none'
                });
                return false;
            }
        }

        return true;
    },

    // 提交出门证
    submitExitPermit: function () {
        this.setData({ submitting: true });

        const loadingTitle = this.data.isEditMode ? '保存中...' : '提交中...';
        wx.showLoading({
            title: loadingTitle,
            mask: true
        });

        const formData = {
            plateNumber: this.data.selectedPlateNumber,
            driverName: this.data.driverName,
            remark: this.data.remark,
            exitItems: this.data.exitItems,
            gateRecordId: this.data.selectedGateRecordId, // 添加进出门主键
            userCode: this.data.userCode,
            userName: this.data.userName,
            submitTime: new Date().toISOString()
        };

        // 修改模式添加特殊字段
        if (this.data.isEditMode) {
            formData.exitPermitId = this.data.editingExitPermitId;
            formData.exitPermitNo = this.data.editingExitPermitNo;
        }

        // 根据模式选择不同的API
        const apiPath = this.data.isEditMode ?
            config.api.updateExitPermit :
            config.api.submitExitPermit;
        const apiUrl = config.api.baseUrl + apiPath;

        http(apiUrl, 'POST', formData,
            (res) => {
                wx.hideLoading();
                this.setData({ submitting: false });

                if (res && res.status === true) {
                    const title = this.data.isEditMode ? '修改成功' : '提交成功';
                    const content = this.data.isEditMode ?
                        '出门证信息修改成功！' :
                        `出门证已成功提交！\n\n出门证编号：${res.data?.exitPermitNo || '已生成'}`;

                    wx.showModal({
                        title: title,
                        content: content,
                        showCancel: false,
                        confirmText: '确定',
                        success: () => {
                            this.resetForm();
                            this.setData({
                                currentTab: 1
                            });
                            // 重新加载历史数据
                            setTimeout(() => {
                                this.loadHistoryData();
                            }, 200);
                        }
                    });
                } else {
                    wx.showToast({
                        title: res.message || '操作失败，请重试',
                        icon: 'none'
                    });
                }
            },
            () => {
                wx.hideLoading();
                this.setData({ submitting: false });
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none'
                });
            }
        );
    },

    // 重置表单
    resetForm: function () {
        this.setData({
            selectedPlateNumber: '',
            driverName: '',
            remark: '',
            status: 'pending',
            exitItems: [],
            summaryData: [],
            isEditMode: false,
            editingExitPermitId: ''
        });
    }
});