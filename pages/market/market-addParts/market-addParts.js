// pages/market/market-addParts/market-addParts.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    url:app.globalData.apiBase,
    isLoading: false,//是否正在加载数据
    searchKey:'',//搜索关键字
    postList:{},//列表
    empty:0,
    page:1,//页数
    tType:'',//事业部信息
    loadingHidden:true,
    downshow:true,//触底加载显示转圈圈图标 先隐藏
    downshow2:true //没有更多了
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      tType:options.tType,
    });
    console.log('进来的事业部类型:'+this.data.tType)
    this.GetListInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.GetListInfo();
    wx.hideNavigationBarLoading();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('加载更多');
    //下拉的逻辑有点不一样，这边不引用通用方法
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //初始化
    this.setData({
    downshow2:true,
    downshow:false,
    page: this.data.page+1,
    isLoading:true,//加载中禁用控件
    })
    var that=this
    console.log('加载更多发送参数:'+that.data.page,that.data.searchKey);
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaParts',
        Page:that.data.page,
        Search:that.data.searchKey,
        Type:1,
        ItemCode:that.data.tType //类型1的专属 把事业部系列放在这个字段里面 后台筛选
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
            that.setData({
            isLoading:false, //按钮启用
            downshow2:false,
            downshow:true,
            Page:that.data.Page-1,
            })
          }else{
            wx: wx.showToast({
              title: '数据加载完毕',
              icon: 'none',
              duration: 1000
            }),
          that.setData({
            postList:that.data.postList.concat(res.data),
            downshow:true,
            downshow2:true,
            isLoading:false //按钮启用
          })
          }
      //展示列表，0则展示没有数据页面，其余无所谓
      that.setData({
        empty:Object.keys(that.data.postList).length
     })
     console.log('全部有 '+that.data.empty+' 条');
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
            downshow:true,
            downshow2:true,
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
          downshow:true,
          downshow2:true,
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //搜索文本改变事件
  onKeyChange: function(e){
    this.setData({
      searchKey: e.detail.value
    });
  },
  //搜索
  searchData:function(e){
    this.GetListInfo();
  },
  //公共方法，获取值
  GetListInfo(){
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //初始化
    this.setData({
    page:1,
    loadingHidden:false,//加载中
    isLoading:true,//加载中禁用控件
    postList:{},
    downshow:true,
    downshow2:true
    })
    var that=this
    console.log('加载数据发送的参数:'+that.data.page,that.data.searchKey);
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'InSideOverSeaParts',
        Page:that.data.page,
        Search:that.data.searchKey,
        Type:1,
        ItemCode:that.data.tType //类型1的专属 把事业部系列放在这个字段里面 后台筛选
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
            that.setData({
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
            })
          }else{
          that.setData({
            postList:res.data,
            loadingHidden:true,//隐藏加载中弹窗
            isLoading:false //按钮启用
          })
          }
      //展示列表，0则展示没有数据页面，其余无所谓
      that.setData({
        empty:Object.keys(that.data.postList).length
     })
     console.log('本次数据加载共 '+that.data.empty+' 条');
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
            loadingHidden:true,
            downshow:true,
            downshow2:true
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
          loadingHidden:true,
          downshow:true,
          downshow2:true
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  //跳转配件界面
  addpart:function(e){
    console.log('跳转',e.currentTarget.dataset);

    let Code = e.currentTarget.dataset.index.Code;
    let SPECS = e.currentTarget.dataset.index.SPECS;

    var that = this;
    
    wx.navigateTo({
      url: '../market-addpartsDetailed/market-addpartsDetailed?Code='+Code+'&SPECS='+SPECS,
    });
  }
})