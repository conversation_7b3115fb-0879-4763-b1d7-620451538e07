<!--pages/market/market-project/market-project.wxml-->
<view class="sg-fixed">
  <view class="cu-bar search bg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索报备人、区域、甲方单位" confirm-type="search" bindinput="bindKeyInput" bindconfirm="searchData" value="{{inputvalue}}"></input>
    </view>
    <view class="action">
      <button bindtap="searchData" class="cu-btn bg-shimge shadow-blur round" disabled="{{isLoading}}" type="">搜索</button>
    </view>
  </view>
</view>

<block wx:for="{{postList}}" wx:for-item="item" wx:for-index="idx">
  <view data-index="{{item}}" wx:if="{{idx%2==0}}"><!--wx:if-->
<view class="radius bg-white" style="margin:30rpx;padding:10rpx;box-shadow: 10px 10px 10px gainsboro;">
  <view bindtap='showandhide' id="{{item[22]}}">
<!--<view class="cu-tag round bg-grey" style="background-color:#1874CD">{{item[22]}}</view>-->
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:10rpx;background-color:#D3D3D3">报备人:<text style="color:black">{{item[1]}}</text></view>
  <view class="flex-sub bg-grey  margin-xs radius" style="padding:10rpx;background-color:#D3D3D3">报备日期:<text style="color:black">{{item[2]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:10rpx;background-color:#D3D3D3">报备区域:<text style="color:black">{{item[3]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:10rpx;background-color:#D3D3D3">单位全称:<text style="color:black">{{item[4]}}</text></view>
</view>
</view>
<!--下面是展开隐藏的明细-->
<view hidden="{{uhide==item[22]?false:true}}">
  <view bindtap='showandhide' id="{{item[22]}}">
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">详细住址:<text style="color:black">{{item[5]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">姓名:<text style="color:black">{{item[6]}}</text></view>
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">电话:<text style="color:black">{{item[7]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">联系人职务:<text style="color:black">{{item[8]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">当前已入库的竞品情况:<text style="color:black">{{item[9]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#87CEFA">运营服务商单位全称:<text style="color:black">{{item[10]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#87CEFA">运营服务商实际住址:<text style="color:black">{{item[11]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#87CEFA">运营服务商负责人姓名:<text style="color:black">{{item[12]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#F5DEB3">选用品牌:<text style="color:black">{{item[13]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#F5DEB3">设备类型:<text style="color:black">{{item[14]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#F5DEB3">入围性质:<text style="color:black">{{item[15]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#F5DEB3">参与竞争的竞品情况:<text style="color:black">{{item[16]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">未来业绩展望:<text style="color:black">{{item[17]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">有效期:<text style="color:black">{{item[18]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">成功几率:<text style="color:black">{{item[19]}}</text></view>
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">对接业务人员:<text style="color:black">{{item[20]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">其他需要说明的信息:<text style="color:black">{{item[21]}}</text></view>
</view>
</view>
<view class="flex" style="height:40px">
  <button class="flex-sub bg-grey shadow-blur round cu-btn" style="background-color:#B5B5B5;margin:6px" bindtap="DropCase" id="{{item[0]}}" disabled="{{isLoading}}" hidden="{{hidebtn==1?false:true}}">弃 单</button>
  <button class="flex-sub bg-grey shadow-blur round cu-btn" style="background-color:#009fa8;margin:6px" bindtap="GetCase" id="{{item[0]}}" disabled="{{isLoading}}" hidden="{{hidebtn==1?false:true}}">成 交</button>
</view>
</view>

</view>
  </view>
  <view data-index="{{item}}" wx:else><!--wx:else 以防需要不一样的这边加一下if else代码块-->
<view class="radius bg-white" style="margin:30rpx;padding:10rpx;box-shadow: 10px 10px 10px gainsboro;">
  <view bindtap='showandhide' id="{{item[22]}}">
<!--<view class="cu-tag round bg-grey" style="background-color:#1874CD">{{item[22]}}</view>-->
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:10rpx;background-color:#D3D3D3">报备人:<text style="color:black">{{item[1]}}</text></view>
  <view class="flex-sub bg-grey  margin-xs radius" style="padding:10rpx;background-color:#D3D3D3">报备日期:<text style="color:black">{{item[2]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:10rpx;background-color:#D3D3D3">报备区域:<text style="color:black">{{item[3]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:10rpx;background-color:#D3D3D3">单位全称:<text style="color:black">{{item[4]}}</text></view>
</view>
</view>
<!--下面是展开隐藏的明细-->
<view hidden="{{uhide==item[22]?false:true}}">
  <view bindtap='showandhide' id="{{item[22]}}">
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">详细住址:<text style="color:black">{{item[5]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">姓名:<text style="color:black">{{item[6]}}</text></view>
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">电话:<text style="color:black">{{item[7]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">联系人职务:<text style="color:black">{{item[8]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">当前已入库的竞品情况:<text style="color:black">{{item[9]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#87CEFA">运营服务商单位全称:<text style="color:black">{{item[10]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#87CEFA">运营服务商实际住址:<text style="color:black">{{item[11]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#87CEFA">运营服务商负责人姓名:<text style="color:black">{{item[12]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#F5DEB3">选用品牌:<text style="color:black">{{item[13]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#F5DEB3">设备类型:<text style="color:black">{{item[14]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#F5DEB3">入围性质:<text style="color:black">{{item[15]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#F5DEB3">参与竞争的竞品情况:<text style="color:black">{{item[16]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">未来业绩展望:<text style="color:black">{{item[17]}}</text></view>
</view>
<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">有效期:<text style="color:black">{{item[18]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">成功几率:<text style="color:black">{{item[19]}}</text></view>
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">对接业务人员:<text style="color:black">{{item[20]}}</text></view>
</view>

<view class="flex">
  <view class="flex-sub bg-grey margin-xs radius" style="padding:8rpx;background-color:#D3D3D3">其他需要说明的信息:<text style="color:black">{{item[21]}}</text></view>
</view>
</view>
<view class="flex" style="height:40px">
  <button class="flex-sub bg-grey shadow-blur round cu-btn" style="background-color:#B5B5B5;margin:6px" bindtap="DropCase" id="{{item[0]}}" disabled="{{isLoading}}" hidden="{{hidebtn==1?false:true}}">弃 单</button>
  <button class="flex-sub bg-grey shadow-blur round cu-btn" style="background-color:#009fa8;margin:6px" bindtap="GetCase" id="{{item[0]}}" disabled="{{isLoading}}" hidden="{{hidebtn==1?false:true}}">成 交</button>
</view>
</view>

</view>
  </view>
</block>