// pages/market/market-project/market-project.js
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    uhide:-1,
    isLoading: false,//是否正在加载数据
    postList:{},//数组信息
    inputvalue:'',//搜索字段
    hidebtn:0,//隐藏按钮
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //初始化
    this.setData({
    isLoading:true,//加载中禁用控件
    })
    var that=this
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'GetMarketUser',
        PersonID:app.globalData.accountInfo.personID
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
            that.setData({
            isLoading:false //按钮启用
            })
          }else{
          that.setData({
            hidebtn:res.data[0][0],
            isLoading:false //按钮启用
          })
          that.GetListInfo();
          }
        }
        else {
          that.setData({
            isLoading:false
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  },

  bindKeyInput:function(e){
    this.setData({
      inputvalue:e.detail.value
    })
  },
  searchData:function(){
    this.GetListInfo();
  },
  //点击切换隐藏和显示
  showandhide: function (event) { 
    console.log(event)
    var that = this;
    var hide = that.data.uhide;
    var itemId = event.currentTarget.id; 
    if (hide == itemId) {
      that.setData({
        uhide: -1
      })
    } else {
      that.setData({
        uhide: itemId
      })
    }
  },
  //弃单
  DropCase:function (event) {
     //避免加载数据时又去服务器取数据
     if (this.data.isLoading){
      return;
      }
      var OAID = event.currentTarget.id;
      //初始化
      this.setData({
      isLoading:true //加载中禁用控件
      })
      var that=this
        wx.showModal({
          title:'警告',
          content:'是否弃单？',
          success:function(res){
            if(res.confirm){
              console.log('确认弃单')
              that.InfoSendToServer('2',OAID);
            }
            else if(res.cancel){
              console.log('不弃单了')
              that.setData({
                isLoading:false
              })
            }
          }
        })
  },
  //成交
  GetCase:function (event) {
     //避免加载数据时又去服务器取数据
     if (this.data.isLoading){
      return;
      }
      var OAID = event.currentTarget.id;
      //初始化
      this.setData({
      isLoading:true //加载中禁用控件
      })
      var that=this
        wx.showModal({
          title:'提示',
          content:'是否成交？',
          success:function(res){
            if(res.confirm){
              console.log('确认成交')
              that.InfoSendToServer('1',OAID);
            }
            else if(res.cancel){
              console.log('不成交了')
              that.setData({
                isLoading:false
              })
            }
          }
        })
  },
  //按钮提交公用事件
  InfoSendToServer(status,OAID){
    var that=this;
    //提交数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'GetMarketBtn',
        BtnNum:status,
        OAID:OAID,
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data=='提交成功'){
            //提醒
            wx: wx.showToast({
              title: '成功！',
              icon: 'none',
              duration: 2000
            })
          that.setData({
            isLoading:false
          })
          that.GetListInfo();
            }else if(res.data=='提交失败'){
            //提醒
            wx: wx.showToast({
              title: '失败！',
              icon: 'none',
              duration: 2000
            })
            that.setData({
              isLoading:false
            })
            }
        }
        else {
          console.log('FailstatusCode:'+res.statusCode);
          that.setData({
            isLoading:false,
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '提交失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false,
        })
    //判断是否网络问题
    wx.getNetworkType({
      success(res) {
        let networkType = res.networkType;
        if (networkType == 'none')
          wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
        else
          wx.showToast({title: '提交失败',icon: 'none',duration: 2000});
      }
    });
      },
      complete: function (res) {
        // complete
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    //隐藏加载动画,停止下拉刷新
    wx.hideNavigationBarLoading();
    wx.stopPullDownRefresh();

    this.GetListInfo();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  //刷新加载数据的通用方法
  GetListInfo(){
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;
    //初始化
    this.setData({
    isLoading:true,//加载中禁用控件
    postList:{},
    uhide:-1
    })
    var that=this
    //加载数据
    wx.request({
      url: app.globalData.apiBase+'/SHIMGERepairServer.ashx',
      data: {
        api: 'GetMarketListInfo',
        inputvalue:that.data.inputvalue
      },
      method: 'POST',
      header: { 'Content-Type': 'application/x-www-form-urlencoded' },
      success: function (res) {
        // success
        console.log(res);//打印请求返回的结果
        if (res.statusCode == '200' || res.statusCode == '204'){
          if(res.data.length==0){
            that.setData({
            isLoading:false //按钮启用
            })
          }else{
          that.setData({
            postList:res.data,
            isLoading:false //按钮启用
          })
          }
        }
        else {
          that.setData({
            isLoading:false
          })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
        }
      },
      fail: function (res) {
        // fail
        console.log(res.data);
        that.setData({
          isLoading:false
        })
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wx.showToast({title: '请检查网络是否正常',icon: 'none',duration: 2000});
          else
            wx.showToast({title: '加载失败',icon: 'none',duration: 2000});
        }
      });
      },
      complete: function (res) {
        // complete
      }
    })
  }
})