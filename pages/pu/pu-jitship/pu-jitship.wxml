<wxs module="filter" src="../../../utils/filter.wxs"></wxs>
<wux-toast id="wux-toast" />
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view class="sg-fixed">
  <view class="cu-bar search bg-white">
    <view class="search-form round">
      <text class="cuIcon-search"></text>
      <input type="text" maxlength="100" placeholder="搜索供应商、产线、料品" confirm-type="search" bindinput="bindKeyInput" bindconfirm="searchData"value="{{searchKey}}">{{searchKey}}</input>
    </view>
  </view>
</view>

<checkbox-group bindchange="checkboxChange">
  <label class="flex align-center bg-white solids-bottom sg-row" wx:for="{{arrtrackList}}" wx:for-item="item" wx:for-index="idx" wx:key="id">
    <view class="basis-p10">
      <checkbox value="{{item.id}}" checked="{{item.checked}}" class="round shimge"></checkbox>
    </view>
    
    <view class="basis-p90">
      <view class="cu-form-group">
        <view class="flex justify-start align-center sg-box">
        <view class="label sg-mwidth2">工单:</view>
        <view class="content">{{item.cWorkCode}}</view>
        </view>
        <view class="flex justify-start align-center">
        <view class="label sg-mwidth2">产线:</view>
        <view class="content">{{item.cProdLineName}}</view>   
        </view> 
      </view>
      <view class="cu-form-group">
        <view class="flex justify-start align-center sg-box">
          <view class="label sg-mwidth2">料号:</view>
          <view class="content">{{item.cItemCode}}</view>
        </view>
        <view class="flex justify-start align-center">
          <view class="label sg-mwidth2">品名:</view>
          <view class="content">{{item.cItemName}}</view>   
        </view> 
      </view>
      <view class="cu-form-group">
        <view class="label sg-mwidth2">规格:</view>
        <view class="content">{{item.cItemSpecs}}</view>
      </view>
      <view class="cu-form-group">
        <view class="label sg-mwidth3">供应商:</view>
        <view class="content">{{item.cSuppName}}</view>
      </view>
      <view class="cu-form-group">
        <view class="flex justify-start align-center sg-box">
          <view class="label sg-mwidth4">配送数量:</view>
          <view class="content">{{item.fPlanQty}}</view>
        </view>
        <view class="flex justify-start align-center">
          <view class="label sg-mwidth4">配送时间:</view>
          <view class="content">{{item.dDelivery}}</view>
        </view>     
      </view>
    </view>

  </label>
</checkbox-group>
 
<view class="cu-load bg-red erro" hidden="{{!isError}}"></view>
<view class="cu-load {{loadState==1?'more':(loadState==2?'loading':(loadState==3?'over':''))}}"></view>

<view class="cu-bar tabbar border foot bg-shimge" disabled="{{isLoading}}" >
  <view class="submit" bindtap="takeover">配送到位</view>
</view> 