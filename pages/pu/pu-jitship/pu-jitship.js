const app = getApp()
import { wuxToast, http } from '../../../utils/util.js'

Page({
  data: {
    loadProgress: 0,//加载进度
    isLoading: false,//是否正在加载数据
    loadState: 0,//加载状态  1:继续下拉加载更多,2:加载中,3:没有更多了
    isError:false,//加载失败
    arrtrackList:[],//订单列表
    searchKey:'',//搜索关键字
    user:null, //当前用户
    page:1,  //查询的当前页码
    bottomId:0,//底部最后一行Id,用于上拉触底事件
  },
  onLoad: function (options) {
    this.data.user = app.globalData.accountInfo;

    this.getRequestData(true);
  },
  //获取后台数据
  getRequestData: function (again, callBack) {
    //避免加载数据时又去服务器取数据
    if (this.data.isLoading)
      return;

    this.data.isLoading = true;
    this.loadProgress();
    wx.showNavigationBarLoading();

    if (again) {
      //重新获取数据
      this.data.bottomId = 0;
      this.data.arrtrackList = [];
    }
    else{
      this.setData({ loadState: 2 });  
    }

    //加载失败提示
    if (this.data.isError)
      this.setData({ isError: false });

    let that = this;
    let pagesize = 30;
    let org = this.data.user.org;
    let user = this.data.user.code;
    let key = this.data.searchKey;
    let page = this.data.page;
    let begin = this.data.bottomId;

    let url = app.globalData.apiBase + '/api/JITShippingPageEx?user=' + user + '&org=' + org + '&key=' + key 
                                     + '&pageSize=' + pagesize + '&begin=' + begin;
    console.log(url);

    let suc = rd => {
      //更新数据
      let totalData = that.data.arrtrackList;
      //判断数据大小是否等于每页数大小，相等则认为还有数据，否则认为没有更多数据
      let hasmore = rd.length == pagesize ? 1 : 0;
      if (rd.length > 0) {
        totalData = totalData.concat(rd);
        that.data.bottomId = rd[rd.length - 1].autoID;
      }

      that.setData({
        loadState: hasmore,
        arrtrackList: totalData
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false;

      if (callBack)
        callBack(rd);
    };

    let fl = () => {
      //更新数据
      that.setData({
        isError: true,
        loadState: 0,
        bottomId:0,
        arrtrackList: []
      });
      //隐藏加载动画,停止下拉刷新
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
      that.data.isLoading = false; 
      //判断是否网络问题
      wx.getNetworkType({
        success(res) {
          let networkType = res.networkType;
          if (networkType == 'none')
            wuxToast('请检查网络是否正常');
        }
      });     
    };

    http(url, 'GET', null, suc, fl);
  },
  //加载进度,状态控制
  loadProgress() {
    let loading = this.data.isLoading;
    let progress = this.data.loadProgress;
    if (loading){
      progress = progress + 10;
      if (progress > 100 && progress < 110)
        progress = 100;
      else if (progress >= 110)
        progress = 10;
    }
    else{
      progress = 0;
    }

    this.setData({
      loadProgress: progress,
      isLoading: loading
    });

    if (progress>0 && progress <= 100) {
      setTimeout(() => {
        this.loadProgress();
      }, 100)
    }
  },
  //查询按钮,外面直接确认的
  searchData: function(){
    this.getRequestData(true, this.goTop);
  },
  //滚动条回到顶部
  goTop: function () {
    wx.pageScrollTo({
      scrollTop: 0
    });
  },
  //搜索文本改变事件
  bindKeyInput:function(e){
    this.data.searchKey = e.detail.value;
  },
  //下拉刷新
  onPullDownRefresh: function () {
    this.getRequestData(true);
  },
  //上拉加载更多
  onReachBottom: function () {
    if (this.data.loadState == 0 ||this.data.loadState == 3)
      return;

    this.data.page = this.data.page + 1

    this.getRequestData(false);
  },
  checkboxChange: function(e){
    console.log(e.detail);
    let items = this.data.arrtrackList;
    let values = e.detail.value;

    for (let i = 0; i < items.length; i++) {
      items[i].checked = false

      for (let j = 0; j < values.length; j++) {
        if (items[i].id == values[j]) {
          items[i].checked = true
          break
        }
      }
    }

    this.setData({ arrtrackList: items });

    console.log(items)
  },
  //配送到位
  takeover:function(e){

    let idnx = new Array();
    let lines = new Array();
    for(let i=0;i<this.data.arrtrackList.length;i++){
      if(this.data.arrtrackList[i].checked){
        let l = this.data.arrtrackList[i].id;
        lines.push(l);
        idnx.push(i);
      }
    }

    console.log('提交的id:',lines)

    if(lines == null || lines.length==0){
      wuxToast('请勾选配送计划');
      return
    }

    let that = this;
    let url = app.globalData.apiBase + '/api/JITShipping';

    console.log(url);


    wx.showModal({
      title: '提示',
      content: '是否确定勾选的记录已经配送到位?',
      confirmText: '是',
      cancelText: '否',
      success (res) {
        if (res.confirm) {
          
          wx.showLoading({ title: '正在提交...', mask: true });

          let dto = {}
          dto.idList = lines;
          dto.userName = that.data.user.name;
          dto.state = 2;
      
          let suc = rd => {
      
            wx.hideLoading();
            wuxToast('提交成功');
            //this.getRequestData(true);
            console.log('提交的序号:',idnx)
      
            for(let j=idnx.length-1;j>=0;j--){
              let dx = idnx[j];
              that.data.arrtrackList.splice(dx,1);
            }
      
            that.setData({ arrtrackList: that.data.arrtrackList });
          };
      
          let fl = () => {
            wx.hideLoading();
            //判断是否网络问题
            wx.getNetworkType({
              success(res) {
                let networkType = res.networkType;
                if (networkType == 'none')
                  wuxToast('请检查网络是否正常');
                else
                  wuxToast('提交失败');
              }
            });
          };
      
          http(url, 'POST', dto , suc, fl); 


        } else if (res.cancel) {
          
        }
      }
    })


  }

})