<wxs module="filter" src="../../../../utils/filter.wxs"></wxs>
<view class='load-progress {{loadProgress!=0?"show":"hide"}}'>
  <view class='load-progress-bar bg-orange' style="transform: translate3d(-{{100-loadProgress}}%, 0px, 0px);"></view>
</view>
<view wx:if="{{!isError}}">
  <view wx:if="{{arrtrackLine.length>0}}">
    <view class="cust-row">
      <view class="sg-label sg-mwidth4">单据类型:</view>
      <text selectable="true" class="sg-content">{{arrtrackMain.poDocType_Name}}</text>
    </view>
    <view class="cust-row">
      <view class="sg-label sg-mwidth3">订单号:</view>
      <text selectable="true" class="sg-content">{{arrtrackMain.docNo}}</text>
    </view>
    <view class="cust-row">
      <view class="sg-label sg-mwidth3">供应商:</view>
      <text selectable="true" class="sg-content">{{arrtrackMain.supplier_Name}}</text>
    </view>  
  </view>
  <block wx:for="{{arrtrackLine}}" wx:for-item="item" wx:for-index="idx" wx:key="poLine">
  <view class="{{item.demandQty?'bg-orange light':''}}">
    <view class="cust-row margin-top-xs">
      <view class="cust-col sg-box">
        <view class="sg-seqnum">{{idx+1}}.</view>
        <view class="sg-label sg-mwidth2">料号:</view>
        <text selectable="true" class="{{item.demandQty?'':'sg-content'}}">{{item.itemInfo_Code}}</text>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth2">品名:</view>
        <text selectable="true" class="{{item.demandQty?'':'sg-content'}}">{{item.itemInfo_Name}}</text>   
      </view> 
    </view>
    <view class="cust-row">
      <view class="sg-label-seq sg-mwidth2">规格:</view>
      <text selectable="true" class="{{item.demandQty?'':'sg-content'}}">{{item.itemInfo_SPECS}}</text>
    </view>
    <view class="cust-row">
      <view class="cust-col sg-box">
        <view class="sg-label-seq sg-mwidth2">交期:</view>
        <text selectable="true" class="{{item.demandQty?'':'sg-content'}}">{{filter.formatDate(item.planArriveDate)}}</text>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth4">未到货数:</view>
        <view class="{{item.demandQty?'':'sg-content'}}">{{item.unReceivedQtyTU+item.tradeUOM_Name}}</view>
      </view>     
    </view>
    <view class="cust-row" wx:if="{{item.lineMemo}}">
      <view class="sg-label-seq sg-mwidth2">备注:</view>
      <text selectable="true" class="{{item.demandQty?'':'sg-content'}}">{{item.lineMemo}}</text>
    </view>
    <view class="cust-row" wx:if="{{item.demandQty}}">
      <view class="cust-col sg-box">
        <view class="sg-label sg-mwidth6">缺料需求日期:</view>
        <text selectable="true" class="{{item.demandQty?'':'sg-content'}}">{{filter.formatDate(item.demandDate)}}</text>
      </view>
      <view class="cust-col">
        <view class="sg-label sg-mwidth6">缺料需求数量:</view>
        <text selectable="true" class="{{item.demandQty?'':'sg-content'}}">{{item.demandQty+item.tradeUOM_Name}}</text>   
      </view> 
    </view>    
  </view>
  </block>  
</view>
<view class="cu-load bg-red erro" wx:else></view>  